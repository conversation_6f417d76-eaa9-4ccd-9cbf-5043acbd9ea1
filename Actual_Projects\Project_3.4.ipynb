{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 3.4. ARMA Models\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import inspect\n", "import time\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px\n", "import seaborn as sns\n", "from IPython.display import VimeoVideo\n", "from pymongo import MongoClient\n", "from sklearn.metrics import mean_absolute_error\n", "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf\n", "from statsmodels.tsa.arima.model import ARIMA\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851728\", h=\"95c59d2805\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Import"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.1: Connect to MongoDB\n", "\n", "Create a client to connect to the MongoDB server, then assign the \"air-quality\" database to db, and the \"nairobi\" collection to nairobi.\n", "\n", "**Create a client object for a MongoDB instance.**\n", "**Access a database using PyMongo.**\n", "**Access a collection in a database using PyMongo.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = MongoClient(host=\"localhost\", port=27017)\n", "db = client[\"air-quality\"]\n", "nairobi = db[\"nairobi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(collection, resample_rule=\"1H\"):\n", "\n", "    results = collection.find(\n", "        {\"metadata.site\": 29, \"metadata.measurement\": \"P2\"},\n", "        projection={\"P2\": 1, \"timestamp\": 1, \"_id\": 0},\n", "    )\n", "\n", "    # Read results into DataFrame\n", "    df = pd.DataFrame(list(results)).set_index(\"timestamp\")\n", "\n", "    # Localize timezone\n", "    df.index = df.index.tz_localize(\"UTC\").tz_convert(\"Africa/Nairobi\")\n", "\n", "    # Remove outliers\n", "    df = df[df[\"P2\"] < 500]\n", "\n", "    # Resample and forward-fill\n", "    y = df[\"P2\"].resample(resample_rule).mean().fillna(method='ffill')\n", "\n", "    return y"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851670\", h=\"3efc0c20d4\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.2: Add Resample Rule Argument\n", "\n", "Change your wrangle function so that it has a resample_rule argument that allows the user to change the resampling interval. The argument default should be \"1H\".\n", "\n", "**What's an argument?**\n", "**Include an argument in a function in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "func_params = set(inspect.signature(wrangle).parameters.keys())\n", "assert func_params == set(\n", "    [\"collection\", \"resample_rule\"]\n", "), f\"Your function should take two arguments: `'collection'`, `'resample_rule'`. Your function takes the following arguments: {func_params}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.3: Load Data\n", "\n", "Use your wrangle function to read the data from the nairobi collection into the Series y."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = wrangle(nairobi)\n", "y.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert isinstance(y, pd.Series), f\"`y` should be a Series, not a {type(y)}.\"\n", "assert len(y) == 2928, f\"`y` should have 2,928 observations, not {len(y)}.\"\n", "assert (\n", "    y.isnull().sum() == 0\n", "), f\"There should be no null values in `y`. Your `y` has {y.isnull().sum()} null values.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Explore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851654\", h=\"687ff8d5ee\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.4: Create ACF Plot\n", "\n", "Create an ACF plot for the data in y. Be sure to label the x-axis as \"Lag [hours]\" and the y-axis as \"Correlation Coefficient\".\n", "\n", "**What's an ACF plot?**\n", "**Create an ACF plot using statsmodels**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "plot_acf(y, ax=ax)\n", "plt.xlabel(\"Lag [hours]\")\n", "plt.ylabel(\"Correlation Coefficient\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851644\", h=\"e857f05bfb\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.5: Create PACF Plot\n", "\n", "Create an PACF plot for the data in y. Be sure to label the x-axis as \"Lag [hours]\" and the y-axis as \"Correlation Coefficient\".\n", "\n", "**What's a PACF plot?**\n", "**Create an PACF plot using statsmodels**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "plot_pacf(y, ax=ax)\n", "plt.xlabel(\"Lag [hours]\")\n", "plt.ylabel(\"Correlation Coefficient\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Split"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.6: Split Data by Date\n", "\n", "Create a training set y_train that contains only readings from October 2018, and a test set y_test that contains readings from November 1, 2018.\n", "\n", "**Subset a DataFrame by selecting one or more rows in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_train = y[\"2018-10\"]\n", "y_test = y[\"2018-11-01\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    len(y_train) == 744\n", "), f\"`y_train` should have 744 observations, not {len(y_train)}.\"\n", "assert len(y_test) == 24, f\"`y_test` should have 24 observations, not {len(y_test)}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build Model\n", "### <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.7: Calculate Baseline MAE\n", "\n", "Calculate the baseline mean absolute error for your model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_train_mean = y_train.mean()\n", "y_pred_baseline = [y_train_mean] * len(y_train)\n", "mae_baseline = mean_absolute_error(y_train, y_pred_baseline)\n", "\n", "print(\"Mean P2 Reading:\", round(y_train_mean, 2))\n", "print(\"Baseline MAE:\", round(mae_baseline, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851576\", h=\"36e2dc6269\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.8: Create Parameter Ranges\n", "\n", "Create ranges for possible p and q values. p_params should range between 0 and 25, by steps of 8. q_params should range between 0 and 3 by steps of 1.\n", "\n", "**What's a hyperparameter?**\n", "**What's an iterator?**\n", "**Create a range in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["p_params = range(0, 25, 8)\n", "q_params = range(0, 3, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851476\", h=\"d60346ed30\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.9: Grid Search for Hyperparameters\n", "\n", "Complete the code below to train a model with every combination of hyperparameters in p_params and q_params. Every time the model is trained, the mean absolute error is calculated and then saved to a dictionary. If you're not sure where to start, do the code-along with <PERSON>!\n", "\n", "**What's an ARMA model?**\n", "**Append an item to a list in Python.**\n", "**Calculate the mean absolute error for a list of predictions in scikit-learn.**\n", "**Instantiate a predictor in statsmodels.**\n", "**Train a model in statsmodels.**\n", "**Write a for loop in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create dictionary to store MAEs\n", "mae_grid = dict()\n", "# Outer loop: Iterate through possible values for `p`\n", "for p in p_params:\n", "    # Create key-value pair in dict. Key is `p`, value is empty list.\n", "    mae_grid[p] = list()\n", "    # Inner loop: Iterate through possible values for `q`\n", "    for q in q_params:\n", "        # Combination of hyperparameters for model\n", "        order = (p, 0, q)\n", "        # Note start time\n", "        start_time = time.time()\n", "        # Train model\n", "        model = ARIMA(y_train, order=order).fit()\n", "        # Calculate model training time\n", "        elapsed_time = round(time.time() - start_time, 2)\n", "        print(f\"Trained ARIMA {order} in {elapsed_time} seconds.\")\n", "        # Generate in-sample (training) predictions\n", "        y_pred = model.fittedvalues\n", "        # Calculate training MAE\n", "        mae = mean_absolute_error(y_train[p:], y_pred)\n", "        # Append MAE to list in dictionary\n", "        mae_grid[p].append(mae)\n", "\n", "print()\n", "print(mae_grid)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851464\", h=\"12f4080d0b\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.10: Create MAE DataFrame\n", "\n", "Organize all the MAE's from above in a DataFrame names mae_df. Each row represents a possible value for q and each column represents a possible value for p.\n", "\n", "**Create a DataFrame from a dictionary using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae_df = pd.DataFrame(mae_grid)\n", "mae_df.round(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851453\", h=\"dfd415bc08\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.11: Create Heatmap\n", "\n", "Create heatmap of the values in mae_grid. Be sure to label your x-axis \"p values\" and your y-axis \"q values\".\n", "\n", "**Create a heatmap in seaborn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sns.heatmap(mae_df, annot=True, fmt=\".4f\")\n", "plt.xlabel(\"p values\")\n", "plt.ylabel(\"q values\")\n", "plt.title(\"MAE Heatmap for ARIMA Models\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851444\", h=\"8b58161f26\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.12: Plot Model Diagnostics\n", "\n", "Use the plot_diagnostics method to check the residuals for your model. Keep in mind that the plot will represent the residuals from the last model you trained, so make sure it was your best model, too!\n", "\n", "**Examine time series model residuals using statsmodels.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 12))\n", "model.plot_diagnostics(fig=fig)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851439\", h=\"c48d80cdf4\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.13: Walk-Forward Validation\n", "\n", "Complete the code below to perform walk-forward validation for your model for the entire test set y_test. Store your model's predictions in the Series y_pred_wfv. Choose the values for p and q that best balance model performance and computation time. Remember: This model is going to have to train 24 times before you can see your test MAE!\n", "\n", "**What's walk-forward validation?**\n", "**Perform walk-forward validation for time series model.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%capture\n", "\n", "y_pred_wfv = pd.Series(dtype=float, index=y_test.index)\n", "history = y_train.copy()\n", "for i in range(len(y_test)):\n", "    model = ARIMA(history, order=(8, 0, 2)).fit()\n", "    next_pred = model.forecast()\n", "    y_pred_wfv.iloc[i] = next_pred\n", "    history = pd.concat([history, y_test.iloc[i:i+1]])\n", "\n", "test_mae = mean_absolute_error(y_test, y_pred_wfv)\n", "print(\"Test MAE (walk forward validation):\", round(test_mae, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Communicate Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665851423\", h=\"8236ff348f\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.4.14: Create Final Visualization\n", "\n", "First, generate the list of training predictions for your model. Next, create a DataFrame df_predictions with the true values y_test and your predictions y_pred_wfv (don't forget the index). Finally, plot df_predictions using plotly express. Make sure that the y-axis is labeled \"P2\".\n", "\n", "**Generate in-sample predictions for a model in statsmodels.**\n", "**Create a DataFrame from a dictionary using pandas.**\n", "**Create a line plot in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_predictions = pd.DataFrame(\n", "    {\"y_test\": y_test, \"y_pred\": y_pred_wfv}, index=y_test.index\n", ")\n", "fig = px.line(df_predictions, labels={\"value\": \"P2\"})\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "**Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}