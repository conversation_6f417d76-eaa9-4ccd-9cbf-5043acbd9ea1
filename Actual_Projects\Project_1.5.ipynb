{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1.5. Housing in Brazil 🇧🇷\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import wqet_grader\n", "\n", "wqet_grader.init(\"Project 1 Assessment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this assignment, you'll work with a dataset of homes for sale in Brazil. Your goal is to determine if there are regional differences in the real estate market. Also, you will look at southern Brazil to see if there is a relationship between home size and price, similar to what you saw with housing in some states in Mexico.\n", "\n", "Before you start: Import the libraries you'll use in this notebook: Matplotlib, pandas, and plotly. Be sure to import them under the aliases we've used in this project."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import Matplotlib, pandas, and plotly\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "\n", "In this assignment, you'll work with real estate data from Brazil. In the data directory for this project there are two CSV that you need to import and clean, one-by-one.\n", "\n", "### Import\n", "\n", "First, you are going to import and clean the data in data/brasil-real-estate-1.csv."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.1: Import First Dataset\n", "\n", "Import the CSV file data/brasil-real-estate-1.csv into the DataFrame df1."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1 = pd.read_csv(\"data/brasil-real-estate-1.csv\")\n", "\n", "df1.info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.1\", df1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before you move to the next task, take a moment to inspect df1 using the info and head methods. What issues do you see in the data? What cleaning will you need to do before you can conduct your analysis?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.2: Drop NaN Values\n", "\n", "Drop all rows with NaN values from the DataFrame df1."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.dropna(inplace=True)\n", "\n", "wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.2\", df1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.3: Split Location Data\n", "\n", "Use the \"lat-lon\" column to create two separate columns in df1: \"lat\" and \"lon\". Make sure that the data type for these new columns is float."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[[\"lat\",\"lon\"]] = df1[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "df1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.3\", df1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.4: Create State Column\n", "\n", "Use the \"place_with_parent_names\" column to create a \"state\" column for df1. (Note that the state name always appears after \"|Brasil|\" in each string.)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[\"place_with_parent_names\"].str.split(\"|\", expand=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[\"state\"] = df1[\"place_with_parent_names\"].str.split(\"|\", expand=True)[2]\n", "df1.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.4\", df1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.5: Transform Price Column\n", "\n", "Transform the \"price_usd\" column of df1 so that all values are floating-point numbers instead of strings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1[\"price_usd\"] = df1[\"price_usd\"].str.replace(\"$\", \"\", regex=False).str.replace(',', '').astype(float)\n", "\n", "wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.5\", df1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.6: Drop Unnecessary Columns\n", "\n", "Drop the \"lat-lon\" and \"place_with_parent_names\" columns from df1."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.drop(columns=[\"lat-lon\", \"place_with_parent_names\"], inplace=True)\n", "\n", "wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.6\", df1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that you have cleaned data/brasil-real-estate-1.csv and created df1, you are going to import and clean the data from the second file, brasil-real-estate-2.csv."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1.5.7: Import Second Dataset\n", "\n", "Import the CSV file brasil-real-estate-2.csv into the DataFrame df2."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df2 = pd.read_csv(\"data/brasil-real-estate-2.csv\")\n", "df2.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 1 Assessment\", \"Task 1.5.7\", df2)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}