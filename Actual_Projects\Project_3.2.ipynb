{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 3.2. Linear Regression with Time Series Data\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px\n", "import pytz\n", "from IPython.display import VimeoVideo\n", "from pymongo import MongoClient\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_absolute_error"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412117\", h=\"c39a50bd58\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Import"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412469\", h=\"135f32c7da\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.1: Connect to MongoDB\n", "\n", "Complete to the create a client to connect to the MongoDB server, assign the \"air-quality\" database to db, and assign the \"nairobi\" connection to nairobi.\n", "\n", "**Create a client object for a MongoDB instance.**\n", "**Access a database using PyMongo.**\n", "**Access a collection in a database using PyMongo.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 1: Import the MongoClient class (already done in your notebook)\n", "from pymongo import MongoClient\n", "\n", "# Step 2: Connect to the MongoDB server\n", "client = MongoClient(\"mongodb://localhost:27017/\")\n", "\n", "# Step 3: Access the \"air-quality\" database\n", "db = client[\"air-quality\"]\n", "\n", "# Step 4: Access the \"nairobi\" collection\n", "nairobi = db[\"nairobi\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412480\", h=\"c20ed3e570\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.2: Create Wrangle Function\n", "\n", "Complete the wrangle function below so that the results from the database query are read into the DataFrame df. Be sure that the index of df is the \"timestamp\" from the results.\n", "\n", "**Create a DataFrame from a dictionary using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(collection):\n", "    # Query documents with specific site and measurement\n", "    results = collection.find(\n", "        {\"metadata.site\": 29, \"metadata.measurement\": \"P2\"},\n", "        projection={\"P2\": 1, \"timestamp\": 1, \"_id\": 0},\n", "    )\n", "    \n", "    # Convert to DataFrame\n", "    df = pd.DataFrame(results)\n", "    \n", "    # Convert 'timestamp' to datetime and set as index\n", "    df[\"timestamp\"] = pd.to_datetime(df[\"timestamp\"])\n", "    df = df.set_index(\"timestamp\")\n", "\n", "    # Localize to Africa/Nairobi timezone\n", "    df = df.tz_localize(\"UTC\").tz_convert(\"Africa/Nairobi\")\n", "    # Remove P2 values above 500\n", "    df = df[df[\"P2\"] <= 500]\n", "    \n", "    # Resample to hourly means\n", "    df = df.resample('H').mean()\n", "\n", "    # Forward fill missing values\n", "    df = df.ffill()\n", "    \n", "    # Create lagged feature \"P2.L1\" with 1-hour lag\n", "    df['P2.L1'] = df['P2'].shift(1)\n", "    \n", "    # Drop rows with NaN values caused by the lag\n", "    df = df.dropna()\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412496\", h=\"d757475f7c\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.3: Load Data\n", "\n", "Use your wrangle function to read the data from the nairobi collection into the DataFrame df."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = wrangle(nairobi)\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert any([isinstance(df, pd.DataFrame), isinstance(df, pd.Series)])\n", "assert len(df) <= 32907\n", "assert isinstance(df.index, pd.DatetimeIndex)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412520\", h=\"e03eefff07\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.4: Timezone Localization\n", "\n", "Add to your wrangle function so that the DatetimeIndex for df is localized to the correct timezone, \"Africa/Nairobi\". Don't forget to re-run all the cells above after you change the function.\n", "\n", "**Localize a timestamp to another timezone using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert df.index.tzinfo == pytz.timezone(\"Africa/Nairobi\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Explore"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412546\", h=\"97792cb982\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.5: Create Boxplot\n", "\n", "Create a boxplot of the \"P2\" readings in df.\n", "\n", "**Create a boxplot using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig, ax = plt.subplots(figsize=(15, 6))\n", "df[\"P2\"].plot(kind=\"box\", ax=ax)\n", "ax.set_title(\"Boxplot of P2 Readings\")\n", "ax.set_ylabel(\"P2 (µg/m³)\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412573\", h=\"b46049021b\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.6: Remove Outliers\n", "\n", "Add to your wrangle function so that all \"P2\" readings above 500 are dropped from the dataset. Don't forget to re-run all the cells above after you change the function.\n", "\n", "**Subset a DataFrame with a mask using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert len(df) <= 32906"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412594\", h=\"e56c2f6839\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.7: Create Time Series Plot\n", "\n", "Create a time series plot of the \"P2\" readings in df.\n", "\n", "**Create a line plot using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "# Plot the P2 values\n", "df[\"P2\"].plot(ax=ax)\n", "# Set title and labels\n", "ax.set_title(\"P2 Readings (Jan–May 2021)\")\n", "ax.set_xlabel(\"Date\")\n", "ax.set_ylabel(\"P2 Concentration (µg/m³)\")\n", "ax.grid(True)\n", "\n", "# Show the plot\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412601\", h=\"a16c5a73fc\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.8: Resample Data\n", "\n", "Add to your wrangle function to resample df to provide the mean \"P2\" reading for each hour. Use a forward fill to impute any missing values. Don't forget to re-run all the cells above after you change the function.\n", "\n", "**Resample time series data in pandas.**\n", "**Impute missing time series values using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Resample to hourly means\n", "#     df = df.resample('H').mean()\n", "\n", "#     # Forward fill missing values\n", "#     df = df.ffill()\n", "# Check your work\n", "assert len(df) <= 2928"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412649\", h=\"d2e99d2e75\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.9: Plot Rolling Average\n", "\n", "Plot the rolling average of the \"P2\" readings in df. Use a window size of 168 (the number of hours in a week).\n", "\n", "**What's a rolling window?**\n", "**Do a rolling window calculation in pandas.**\n", "**Make a line plot with time series data in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "\n", "# Calculate rolling mean with window size 168 and plot it\n", "df[\"P2\"].rolling(window=168).mean().plot(ax=ax)\n", "\n", "# Add title and labels for clarity\n", "ax.set_title(\"Rolling Average of P2 Readings (Weekly Window)\")\n", "ax.set_xlabel(\"Timestamp\")\n", "ax.set_ylabel(\"P2 (Rolling Mean)\")\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412693\", h=\"c3bca16aff\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.10: Create Lagged Feature\n", "\n", "Add to your wrangle function to create a column called \"P2.L1\" that contains the mean\"P2\" reading from the previous hour. Since this new feature will create NaN values in your DataFrame, be sure to also drop null rows from df.\n", "\n", "**Shift the index of a Series in pandas.**\n", "**Drop rows with missing values from a DataFrame using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create lagged feature \"P2.L1\" with 1-hour lag\n", "#    df['P2.L1'] = df['P2'].shift(1)\n", "    \n", "    # Drop rows with NaN values caused by the lag\n", "#    df = df.dropna()\n", "# Check your work\n", "assert len(df) <= 11686\n", "assert df.shape[1] == 2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412732\", h=\"059e4088c5\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.11: Create Correlation Matrix\n", "\n", "Create a correlation matrix for df.\n", "\n", "**Create a correlation matrix in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["correlation_matrix = df.corr()\n", "print(correlation_matrix)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412741\", h=\"7439cb107c\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.12: <PERSON><PERSON> <PERSON><PERSON><PERSON> Plot\n", "\n", "Create a scatter plot that shows PM 2.5 mean reading for each our as a function of the mean reading from the previous hour. In other words, \"P2.L1\" should be on the x-axis, and \"P2\" should be on the y-axis. Don't forget to label your axes!\n", "\n", "**Create a scatter plot using Matplotlib.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "fig, ax = plt.subplots(figsize=(6, 6))\n", "ax.scatter(df[\"P2.L1\"], df[\"P2\"], alpha=0.5)  # Scatter plot with some transparency\n", "ax.set_xlabel(\"P2.L1 (Previous Hour's Mean PM2.5 Reading)\")\n", "ax.set_ylabel(\"P2 (Current Hour's Mean PM2.5 Reading)\")\n", "ax.set_title(\"Scatter Plot of PM2.5 Readings: Current vs Previous Hour\")\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Split"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412762\", h=\"a5eba496f7\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.13: Split Data\n", "\n", "Split the DataFrame df into the feature matrix X and the target vector y. Your target is \"P2\".\n", "\n", "**Subset a DataFrame by selecting one or more columns in pandas.**\n", "**Select a Series from a DataFrame in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target = \"P2\"\n", "y = df[target]         # Target variable (Series)\n", "X = df[[\"P2.L1\"]]      # Features (DataFrame with one column)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412785\", h=\"03118eda71\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.14: Train-Test Split\n", "\n", "Split X and y into training and test sets. The first 80% of the data should be in your training set. The remaining 20% should be in the test set.\n", "\n", "**Divide data into training and test sets in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cutoff = int(len(X) * 0.8)\n", "\n", "X_train, y_train = X.iloc[:cutoff], y.iloc[:cutoff]\n", "X_test, y_test = X.iloc[cutoff:], y.iloc[cutoff:]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build Model\n", "### <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.15: Calculate Baseline MAE\n", "\n", "Calculate the baseline mean absolute error for your model.\n", "\n", "**Calculate summary statistics for a DataFrame or Series in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred_baseline = [y_train.mean()] * len(y_train)\n", "mae_baseline = mean_absolute_error(y_train, y_pred_baseline)\n", "\n", "print(\"Mean P2 Reading:\", round(y_train.mean(), 2))\n", "print(\"Baseline MAE:\", round(mae_baseline, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.16: Train Model\n", "\n", "Instantiate a LinearRegression model named model, and fit it to your training data.\n", "\n", "**Instantiate a predictor in scikit-learn.**\n", "**Fit a model to training data in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = LinearRegression()\n", "model.fit(X_train, y_train)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412844\", h=\"129865775d\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.17: Calculate MAE\n", "\n", "Calculate the training and test mean absolute error for your model.\n", "\n", "**Generate predictions using a trained model in scikit-learn.**\n", "**Calculate the mean absolute error for a list of predictions in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["training_mae = mean_absolute_error(y_train, model.predict(X_train))\n", "test_mae = mean_absolute_error(y_test, model.predict(X_test))\n", "\n", "print(\"Training MAE:\", round(training_mae, 2))\n", "print(\"Test MAE:\", round(test_mae, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Communicate Results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.18: Extract Model Parameters\n", "\n", "Extract the intercept and coefficient from your model.\n", "\n", "**Access an object in a pipeline in scikit-learn**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["intercept = model.intercept_\n", "coefficient = model.coef_[0]\n", "\n", "print(f\"P2 = {intercept} + ({coefficient} * P2.L1)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412870\", h=\"318d69683e\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.19: Create Predictions DataFrame\n", "\n", "Create a DataFrame df_pred_test that has two columns: \"y_test\" and \"y_pred\". The first should contain the true values for your test set, and the second should contain your model's predictions. Be sure the index of df_pred_test matches the index of y_test.\n", "\n", "**Create a DataFrame from a dictionary using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pred_test = pd.DataFrame({\n", "    \"y_test\": y_test,\n", "    \"y_pred\": model.predict(X_test)\n", "}, index=y_test.index)\n", "\n", "df_pred_test.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"665412891\", h=\"39d7356a26\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.2.20: Create Time Series Plot\n", "\n", "Create a time series line plot for the values in test_predictions using plotly express. Be sure that the y-axis is properly labeled as \"P2\".\n", "\n", "**Create a line plot using plotly express.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "\n", "fig = px.line(df_pred_test, y=[\"y_test\", \"y_pred\"], labels={\"value\": \"P2\", \"index\": \"Timestamp\"})\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "**Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}