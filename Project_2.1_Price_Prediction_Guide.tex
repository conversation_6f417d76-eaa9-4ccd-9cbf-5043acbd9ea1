\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 2.1: Price Prediction}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 2.1: Predicting Apartment Prices} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Your First Machine Learning Model}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Welcome to Machine Learning!}

Welcome to your first machine learning project! Today we're going to predict apartment prices in Buenos Aires, Argentina. Think of this like teaching a computer to estimate how much an apartment costs based on its size.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item How to clean and prepare real-world data
    \item Building your first machine learning model
    \item Understanding what makes a "good" prediction
    \item Visualizing your results
    \item Interpreting what your model learned
\end{itemize}

\begin{concept}
\textbf{What is Machine Learning?}
Imagine you're learning to estimate apartment prices. You look at many apartments and notice: bigger apartments usually cost more. Machine learning does the same thing - it finds patterns in data to make predictions about new examples.

Our goal: Train a computer to predict apartment prices based on size.
\end{concept}

\section{Task 2.1.1: Setting Up Your Environment}

Let's start by importing the tools we need. Think of this like getting your toolbox ready before starting a project.

\begin{lstlisting}[caption=Import Basic Tools]
import pandas as pd
import matplotlib.pyplot as plt
import warnings
warnings.simplefilter(action="ignore", category=FutureWarning)

print("Ready to start! Let's build our first ML model.")
\end{lstlisting}

\begin{learningtip}
\textbf{Why These Tools?}
\begin{itemize}
    \item \textbf{pandas:} Handles spreadsheet-like data (like Excel, but in Python)
    \item \textbf{matplotlib:} Creates charts and graphs
    \item \textbf{warnings:} Hides technical messages that might confuse us
\end{itemize}
We'll add more tools as we need them, so you can learn what each one does.
\end{learningtip}

\section{Task 2.1.2: Creating a Simple Data Cleaner}

Real-world data is messy! Let's create a simple function to clean our apartment data.

\begin{lstlisting}[caption=Simple Data Cleaning Function]
def wrangle(filepath):
    """Clean apartment data and keep only what we need."""
    
    # Read the CSV file (like opening an Excel file)
    df = pd.read_csv(filepath)
    print(f"Started with {len(df)} properties")
    
    # Keep only apartments in Buenos Aires under $400,000
    apartments = df[
        (df["place_with_parent_names"].str.contains("Capital Federal", na=False)) &
        (df["property_type"] == "apartment") &
        (df["price_aprox_usd"] < 400_000)
    ]
    print(f"Found {len(apartments)} apartments in Buenos Aires under $400K")
    
    # Remove unusually large or small apartments (outliers)
    size_low = apartments["surface_covered_in_m2"].quantile(0.1)
    size_high = apartments["surface_covered_in_m2"].quantile(0.9)
    
    cleaned = apartments[
        (apartments["surface_covered_in_m2"] >= size_low) &
        (apartments["surface_covered_in_m2"] <= size_high)
    ]
    print(f"After removing outliers: {len(cleaned)} apartments")
    
    return cleaned

# Test our function
df = wrangle("data/buenos-aires-real-estate-1.csv")
print(f"\nFinal dataset: {len(df)} apartments")
print("Success! Data is ready for analysis.")
\end{lstlisting}

\begin{learningtip}
\textbf{Why Clean Data?}
Imagine trying to learn apartment prices when your data includes:
\begin{itemize}
    \item Houses (not apartments)
    \item \$10 million penthouses (not typical)
    \item Apartments with 1 sq meter (data errors)
\end{itemize}
Cleaning helps our model focus on normal apartments, making better predictions.
\end{learningtip}

\section{Task 2.1.3: Exploring Our Data}

Before building a model, let's understand what we have. Think of this like looking around a neighborhood before buying a house.

\begin{lstlisting}[caption=Basic Data Exploration]
# Look at the first few apartments
print("First 5 apartments:")
print(df[["surface_covered_in_m2", "price_aprox_usd"]].head())

# Get basic statistics
print(f"\nDataset Summary:")
print(f"Number of apartments: {len(df)}")
print(f"Average price: ${df['price_aprox_usd'].mean():,.0f}")
print(f"Average size: {df['surface_covered_in_m2'].mean():.0f} sq meters")
print(f"Price range: ${df['price_aprox_usd'].min():,.0f} to ${df['price_aprox_usd'].max():,.0f}")
print(f"Size range: {df['surface_covered_in_m2'].min():.0f} to {df['surface_covered_in_m2'].max():.0f} sq meters")
\end{lstlisting}

\begin{concept}
\textbf{Understanding Your Data:}
Always explore your data first! Key questions to ask:
\begin{itemize}
    \item How many examples do I have? (More is usually better)
    \item What's the typical range? (Helps set expectations)
    \item Are there any weird values? (Might indicate problems)
\end{itemize}
\end{concept}

\section{Task 2.1.4: Visualizing Apartment Sizes}

Pictures are worth a thousand numbers! Let's see the distribution of apartment sizes.

\begin{lstlisting}[caption=Apartment Size Histogram]
# Create a histogram of apartment sizes
plt.figure(figsize=(10, 6))
plt.hist(df["surface_covered_in_m2"], bins=25, color='skyblue', 
         edgecolor='black', alpha=0.7)
plt.xlabel("Apartment Size (sq meters)")
plt.ylabel("Number of Apartments")
plt.title("Distribution of Apartment Sizes in Buenos Aires")
plt.grid(axis='y', alpha=0.3)

# Add some helpful statistics to the plot
avg_size = df["surface_covered_in_m2"].mean()
plt.axvline(avg_size, color='red', linestyle='--', linewidth=2, 
           label=f'Average: {avg_size:.0f} sq meters')
plt.legend()
plt.show()

print(f"Most apartments are between {df['surface_covered_in_m2'].quantile(0.25):.0f} and {df['surface_covered_in_m2'].quantile(0.75):.0f} square meters")
\end{lstlisting}

\begin{learningtip}
\textbf{Reading Histograms:}
\begin{itemize}
    \item \textbf{X-axis:} Apartment sizes (what we're measuring)
    \item \textbf{Y-axis:} How many apartments have that size
    \item \textbf{Tall bars:} Common sizes
    \item \textbf{Short bars:} Rare sizes
\end{itemize}
This helps us understand what "normal" looks like in our data.
\end{learningtip}

\section{Task 2.1.5: The Big Question - Do Bigger Apartments Cost More?}

Let's see if there's a relationship between size and price. This will tell us if size is useful for predicting price.

\begin{lstlisting}[caption=Price vs Size Scatter Plot]
# Create scatter plot
plt.figure(figsize=(12, 8))
plt.scatter(df["surface_covered_in_m2"], df["price_aprox_usd"], 
           alpha=0.6, s=50, color='green', edgecolor='black')
plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title("Buenos Aires Apartments: Does Size Predict Price?")
plt.grid(True, alpha=0.3)

# Format y-axis to show dollar signs
plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

plt.show()

print("Look for the pattern: Do points go up and to the right?")
print("If yes, bigger apartments generally cost more!")
\end{lstlisting}

\begin{concept}
\textbf{What Makes a Good Feature?}
A good feature (like apartment size) should have a clear relationship with what you're predicting (price). In a scatter plot, look for:
\begin{itemize}
    \item \textbf{Upward trend:} As X increases, Y increases
    \item \textbf{Points roughly in a line:} Strong relationship
    \item \textbf{Scattered everywhere:} Weak relationship
\end{itemize}
\end{concept}

\section{Task 2.1.6: Preparing Data for Machine Learning}

Machine learning algorithms are picky about data format. Let's organize our data the way they like it.

\begin{lstlisting}[caption=Organizing Data for ML]
# Create X (features) - what we know about each apartment
X_train = df[["surface_covered_in_m2"]].copy()  # Size information
print(f"Features (X): {X_train.shape}")
print("X contains the apartment sizes")

# Create y (target) - what we want to predict
y_train = df["price_aprox_usd"].copy()  # Price information  
print(f"Target (y): {y_train.shape}")
print("y contains the apartment prices")

print(f"\nWe have {len(X_train)} apartments to learn from")
print("Our goal: use size (X) to predict price (y)")
\end{lstlisting}

\begin{learningtip}
\textbf{ML Data Format:}
\begin{itemize}
    \item \textbf{X (features):} What you know (apartment size). Always 2D shape like (1343, 1)
    \item \textbf{y (target):} What you want to predict (price). Always 1D shape like (1343,)
    \item Think: "Given X, predict y" or "Given size, predict price"
\end{itemize}
This standard format works with all machine learning tools.
\end{learningtip}

\section{Task 2.1.7: Building a Baseline Model}

Before getting fancy, let's try the simplest possible approach: always predict the average price.

\begin{lstlisting}[caption=Simple Baseline Model]
# Calculate the average price
average_price = y_train.mean()
print(f"Average apartment price: ${average_price:,.0f}")

# Create baseline predictions (always predict the average)
baseline_predictions = [average_price] * len(y_train)
print(f"Baseline model: always predict ${average_price:,.0f}")

# How good is this simple approach?
from sklearn.metrics import mean_absolute_error

baseline_error = mean_absolute_error(y_train, baseline_predictions)
print(f"Baseline error: ${baseline_error:,.0f} per prediction")
print("This means our 'always predict average' approach is off by about $45K")
\end{lstlisting}

\begin{concept}
\textbf{Why Start with a Baseline?}
A baseline gives you a reference point. If your fancy model can't beat "always predict the average," something is wrong! It's like checking if your GPS is better than just saying "go straight."

Our goal: Build a model that makes smaller errors than \$45,000 per prediction.
\end{concept}

\section{Task 2.1.8: Your First Machine Learning Model!}

Now for the exciting part - let's train a computer to predict prices!

\begin{lstlisting}[caption=Training a Linear Regression Model]
from sklearn.linear_model import LinearRegression

# Create the model
model = LinearRegression()
print("Created a linear regression model")

# Train it on our data
model.fit(X_train, y_train)
print("Training complete! The model has learned the pattern.")

# Let's see what it learned
slope = model.coef_[0]
intercept = model.intercept_

print(f"\nWhat the model learned:")
print(f"For every additional square meter, price increases by ${slope:,.0f}")
print(f"A 0-square-meter apartment would cost ${intercept:,.0f} (theoretical)")
print(f"\nModel equation: Price = ${intercept:,.0f} + ${slope:,.0f} x Size")
\end{lstlisting}

\begin{learningtip}
\textbf{How Linear Regression Works:}
Linear regression finds the best line through your data points. The line has two parts:
\begin{itemize}
    \item \textbf{Slope:} How much Y changes when X increases by 1
    \item \textbf{Intercept:} Where the line crosses the Y-axis
\end{itemize}
Think: "For each extra square meter, price goes up by \$2,253"
\end{learningtip}

\section{Task 2.1.9: Testing Our Model}

Let's see how well our model predicts prices!

\begin{lstlisting}[caption=Making Predictions]
# Use our model to predict prices
predictions = model.predict(X_train)
print(f"Made {len(predictions)} predictions")

# How good are our predictions?
model_error = mean_absolute_error(y_train, predictions)
print(f"Model prediction error: ${model_error:,.0f}")
print(f"Baseline error was: ${baseline_error:,.0f}")

# Calculate improvement
improvement = baseline_error - model_error
percentage_better = (improvement / baseline_error) * 100

print(f"\nOur model is ${improvement:,.0f} better per prediction!")
print(f"That's {percentage_better:.1f}% better than just guessing the average!")
\end{lstlisting}

\begin{concept}
\textbf{Mean Absolute Error (MAE):}
MAE tells you how far off your predictions are, on average:
\begin{itemize}
    \item \textbf{Lower is better:} Smaller errors mean better predictions
    \item \textbf{Same units as target:} If predicting dollars, MAE is in dollars
    \item \textbf{Easy to interpret:} "My predictions are off by about \$32K"
\end{itemize}
\end{concept}

\section{Task 2.1.10: Visualizing Model Performance}

Pictures help us understand how well our model works. Let's create some charts!

\begin{lstlisting}[caption=Model Performance Visualization]
# Create a comprehensive plot
plt.figure(figsize=(14, 10))

# Subplot 1: Actual data with both models
plt.subplot(2, 2, 1)
plt.scatter(X_train, y_train, alpha=0.6, s=30, label='Actual Apartments', color='lightblue')
plt.plot(X_train, baseline_predictions, color='red', linewidth=2, 
         label=f'Baseline (always ${average_price:,.0f})', linestyle='--')

# Plot our model's line
size_range = [X_train.min().iloc[0], X_train.max().iloc[0]]
price_range = [model.predict([[s]])[0] for s in size_range]
plt.plot(size_range, price_range, color='green', linewidth=3, 
         label='Our ML Model')

plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title("Model Comparison")
plt.legend()
plt.grid(True, alpha=0.3)

# Subplot 2: Prediction accuracy
plt.subplot(2, 2, 2)
plt.scatter(y_train, predictions, alpha=0.6, s=30, color='purple')
plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 
         'r--', label='Perfect Predictions')
plt.xlabel("Actual Price")
plt.ylabel("Predicted Price")
plt.title("How Accurate Are Our Predictions?")
plt.legend()
plt.grid(True, alpha=0.3)

# Subplot 3: Prediction errors
plt.subplot(2, 2, 3)
errors = y_train - predictions
plt.hist(errors, bins=25, color='orange', alpha=0.7, edgecolor='black')
plt.xlabel("Prediction Error (Actual - Predicted)")
plt.ylabel("Number of Apartments")
plt.title("Distribution of Prediction Errors")
plt.axvline(0, color='red', linestyle='--', label='Perfect (No Error)')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Subplot 4: Model equation
plt.subplot(2, 2, 4)
plt.text(0.1, 0.7, f"Model Equation:", fontsize=14, fontweight='bold')
plt.text(0.1, 0.5, f"Price = ${intercept:,.0f} + ${slope:,.0f} x Size", fontsize=12)
plt.text(0.1, 0.3, f"Model Error: ${model_error:,.0f}", fontsize=12)
plt.text(0.1, 0.1, f"Improvement: {percentage_better:.1f}% better", fontsize=12, color='green')
plt.xlim(0, 1)
plt.ylim(0, 1)
plt.axis('off')
plt.title("Model Summary")

plt.tight_layout()
plt.show()
\end{lstlisting}

\section{Task 2.1.11: Making Individual Predictions}

Let's use our model to predict prices for specific apartments!

\begin{lstlisting}[caption=Predicting Individual Apartment Prices]
def predict_apartment_price(size_sq_meters):
    """Predict price for an apartment of given size."""
    prediction = model.predict([[size_sq_meters]])[0]
    print(f"Apartment size: {size_sq_meters} sq meters")
    print(f"Predicted price: ${prediction:,.0f}")
    return prediction

print("Let's predict some apartment prices:")
print("\n1. Small apartment:")
predict_apartment_price(50)

print("\n2. Medium apartment:")
predict_apartment_price(80)

print("\n3. Large apartment:")
predict_apartment_price(120)

print("\n4. Extra large apartment:")
predict_apartment_price(150)

# Test with actual apartments from our dataset
print("\n5. Comparing with real apartments:")
for i in range(3):
    actual_size = X_train.iloc[i, 0]
    actual_price = y_train.iloc[i]
    predicted_price = model.predict([[actual_size]])[0]
    error = abs(actual_price - predicted_price)
    
    print(f"\nApartment {i+1}:")
    print(f"  Size: {actual_size:.0f} sq meters")
    print(f"  Actual price: ${actual_price:,.0f}")
    print(f"  Predicted price: ${predicted_price:,.0f}")
    print(f"  Error: ${error:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Real-World Applications:}
Your model can now help with:
\begin{itemize}
    \item \textbf{Buyers:} "Is this apartment fairly priced?"
    \item \textbf{Sellers:} "How much should I ask for my apartment?"
    \item \textbf{Investors:} "Which apartments are undervalued?"
    \item \textbf{Real estate apps:} Automatic price estimates
\end{itemize}
\end{learningtip}

\section{Task 2.1.12: Understanding Model Limitations}

Every model has limitations. Let's understand what ours can and cannot do.

\begin{lstlisting}[caption=Model Limitations Analysis]
# Check prediction errors for different apartment sizes
small_apts = df[df["surface_covered_in_m2"] < 60]
medium_apts = df[(df["surface_covered_in_m2"] >= 60) & (df["surface_covered_in_m2"] < 100)]
large_apts = df[df["surface_covered_in_m2"] >= 100]

print("Prediction accuracy by apartment size:")

for name, apartments in [("Small", small_apts), ("Medium", medium_apts), ("Large", large_apts)]:
    if len(apartments) > 0:
        X_subset = apartments[["surface_covered_in_m2"]]
        y_subset = apartments["price_aprox_usd"]
        pred_subset = model.predict(X_subset)
        error_subset = mean_absolute_error(y_subset, pred_subset)
        
        print(f"{name} apartments (n={len(apartments)}): ${error_subset:,.0f} average error")

print(f"\nWhat our model assumes:")
print(f"- Bigger apartments always cost more")
print(f"- The relationship is a straight line")
print(f"- Size is the only thing that matters")
print(f"\nWhat our model ignores:")
print(f"- Neighborhood quality")
print(f"- Apartment condition")  
print(f"- Number of bedrooms")
print(f"- Floor level, balcony, etc.")
\end{lstlisting}

\begin{concept}
\textbf{Model Assumptions:}
Linear regression assumes:
\begin{itemize}
    \item \textbf{Linear relationship:} The effect is constant (each sq meter adds the same amount)
    \item \textbf{No missing factors:} Size explains most of the price variation
    \item \textbf{Consistent patterns:} The relationship is the same across all apartments
\end{itemize}
When these assumptions break, the model becomes less accurate.
\end{concept}

\section{Summary: What We Accomplished}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Data Cleaning:} Filtered and prepared real-world messy data
    \item \textbf{Exploratory Analysis:} Understood data through statistics and visualizations
    \item \textbf{Model Building:} Trained your first machine learning algorithm
    \item \textbf{Model Evaluation:} Measured and compared model performance
    \item \textbf{Interpretation:} Extracted business insights from model coefficients
    \item \textbf{Prediction:} Used the model to estimate prices for new apartments
\end{enumerate}

\subsection{Key Findings}
\begin{itemize}
    \item \textbf{Size matters:} Each square meter adds about \$2,253 to apartment price
    \item \textbf{Model works:} 28\% better predictions than just guessing the average
    \item \textbf{Room for improvement:} Still \$32K average error suggests other factors matter
    \item \textbf{Practical value:} Model provides useful price estimates for decision-making
\end{itemize}

\subsection{Important Concepts}
\begin{itemize}
    \item \textbf{Machine Learning Process:} Data → Model → Predictions → Evaluation
    \item \textbf{Linear Regression:} Finds the best line through data points
    \item \textbf{Baseline Comparison:} Always compare to simple approaches
    \item \textbf{Mean Absolute Error:} Measures average prediction accuracy
    \item \textbf{Model Limitations:} Understanding what your model can and cannot do
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Try the same analysis with a different Buenos Aires dataset
    \item Predict prices for apartments with sizes 45, 75, and 105 sq meters
    \item Create a histogram of prediction errors and describe the pattern
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Filter data to include only apartments with 1-3 bedrooms and rebuild the model
    \item Create separate models for different price ranges (under \$100K, \$100K-\$200K, etc.)
    \item Calculate the model's R-squared score and interpret what it means
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Build a model using number of bedrooms instead of size. Which predicts better?
    \item Try polynomial regression (size squared) and compare to linear regression
    \item Create a simple web interface where users can input apartment size and get price predictions
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Analyze different Buenos Aires dataset}
\begin{lstlisting}[caption=Working with Different Dataset]
# If you have a different Buenos Aires dataset, load it
# df_new = pd.read_csv("buenos_aires_dataset_2.csv")

# For demonstration, let's simulate working with a subset
# Use apartments from a different area or time period
df_subset = df.sample(frac=0.7, random_state=42)  # Random 70% of data

print(f"Original dataset: {len(df)} apartments")
print(f"New subset: {len(df_subset)} apartments")

# Apply same analysis pipeline
X_new = df_subset[["surface_covered_in_m2"]]
y_new = df_subset["price_aprox_usd"]

# Check correlation in new dataset
correlation_new = X_new.iloc[:, 0].corr(y_new)
print(f"Size-price correlation in new dataset: {correlation_new:.3f}")

# Train model on new data
model_new = LinearRegression()
model_new.fit(X_new, y_new)

# Compare model coefficients
print(f"Original model - Slope: ${model.coef_[0]:,.0f}, Intercept: ${model.intercept_:,.0f}")
print(f"New model - Slope: ${model_new.coef_[0]:,.0f}, Intercept: ${model_new.intercept_:,.0f}")

# Calculate performance
predictions_new = model_new.predict(X_new)
mae_new = mean_absolute_error(y_new, predictions_new)
print(f"New model MAE: ${mae_new:,.0f}")
\end{lstlisting}

\textbf{Exercise 2: Predict specific apartment sizes}
\begin{lstlisting}[caption=Predicting Specific Apartment Prices]
# Predict prices for requested sizes
sizes_to_predict = [45, 75, 105]

print("APARTMENT PRICE PREDICTIONS")
print("=" * 40)

for size in sizes_to_predict:
    predicted_price = model.predict([[size]])[0]
    
    # Find similar apartments in our dataset for comparison
    similar_apts = df[abs(df["surface_covered_in_m2"] - size) <= 5]
    
    print(f"\n{size} sq meter apartment:")
    print(f"  Predicted price: ${predicted_price:,.0f}")
    
    if len(similar_apts) > 0:
        avg_actual = similar_apts["price_aprox_usd"].mean()
        print(f"  Similar apartments average: ${avg_actual:,.0f}")
        print(f"  Difference: ${abs(predicted_price - avg_actual):,.0f}")
        print(f"  Number of similar apartments: {len(similar_apts)}")
    else:
        print(f"  No similar apartments in dataset for comparison")

# Create visualization
plt.figure(figsize=(10, 6))
plt.scatter(df["surface_covered_in_m2"], df["price_aprox_usd"], 
           alpha=0.6, color='lightblue', label='Actual apartments')

# Plot regression line
x_range = np.linspace(df["surface_covered_in_m2"].min(), 
                     df["surface_covered_in_m2"].max(), 100)
y_range = model.predict(x_range.reshape(-1, 1))
plt.plot(x_range, y_range, color='red', linewidth=2, label='Prediction line')

# Highlight our predictions
for size in sizes_to_predict:
    pred_price = model.predict([[size]])[0]
    plt.scatter(size, pred_price, color='orange', s=100, 
               label=f'{size}m² = ${pred_price:,.0f}' if size == sizes_to_predict[0] else '')

plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title("Apartment Price Predictions")
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
\end{lstlisting}

\textbf{Exercise 3: Histogram of prediction errors with analysis}
\begin{lstlisting}[caption=Analyzing Prediction Errors Pattern]
# Calculate prediction errors
errors = y_train - predictions

# Create detailed error analysis
plt.figure(figsize=(15, 10))

# Main histogram
plt.subplot(2, 3, 1)
plt.hist(errors, bins=30, color='skyblue', edgecolor='black', alpha=0.7)
plt.xlabel("Prediction Error (Actual - Predicted)")
plt.ylabel("Number of Apartments")
plt.title("Distribution of Prediction Errors")
plt.axvline(0, color='red', linestyle='--', label='Perfect Prediction')
plt.axvline(errors.mean(), color='orange', linestyle='--', label=f'Mean Error: ${errors.mean():,.0f}')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Error statistics
plt.subplot(2, 3, 2)
error_stats = {
    'Mean Error': errors.mean(),
    'Std Error': errors.std(),
    'Min Error': errors.min(),
    'Max Error': errors.max(),
    'Median Error': errors.median()
}

bars = plt.bar(range(len(error_stats)), error_stats.values(), 
               color=['red', 'orange', 'green', 'purple', 'blue'])
plt.xticks(range(len(error_stats)), error_stats.keys(), rotation=45)
plt.ylabel("Error Amount (USD)")
plt.title("Error Statistics")

# Add value labels on bars
for bar, value in zip(bars, error_stats.values()):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${value:,.0f}', ha='center', va='bottom')

# Errors vs apartment size
plt.subplot(2, 3, 3)
plt.scatter(X_train.iloc[:, 0], errors, alpha=0.6, color='green')
plt.xlabel("Apartment Size (sq meters)")
plt.ylabel("Prediction Error")
plt.title("Errors vs Apartment Size")
plt.axhline(0, color='red', linestyle='--')
plt.grid(True, alpha=0.3)

# Absolute errors
plt.subplot(2, 3, 4)
abs_errors = np.abs(errors)
plt.hist(abs_errors, bins=25, color='coral', edgecolor='black', alpha=0.7)
plt.xlabel("Absolute Prediction Error")
plt.ylabel("Number of Apartments")
plt.title("Absolute Error Distribution")
plt.axvline(abs_errors.mean(), color='blue', linestyle='--', 
           label=f'Mean: ${abs_errors.mean():,.0f}')
plt.legend()

# Error percentiles
plt.subplot(2, 3, 5)
percentiles = [10, 25, 50, 75, 90]
error_percentiles = [np.percentile(abs_errors, p) for p in percentiles]
plt.bar(percentiles, error_percentiles, color='lightgreen', edgecolor='black')
plt.xlabel("Percentile")
plt.ylabel("Absolute Error (USD)")
plt.title("Error Percentiles")
for i, (p, e) in enumerate(zip(percentiles, error_percentiles)):
    plt.text(p, e + 1000, f'${e:,.0f}', ha='center', va='bottom')

# Pattern analysis text
plt.subplot(2, 3, 6)
pattern_analysis = f"""
ERROR PATTERN ANALYSIS:

Distribution Shape:
• Mean error: ${errors.mean():,.0f}
• Standard deviation: ${errors.std():,.0f}
• Skewness: {'Right' if errors.mean() > 0 else 'Left'}

Key Insights:
• 50% of errors under ${np.percentile(abs_errors, 50):,.0f}
• 90% of errors under ${np.percentile(abs_errors, 90):,.0f}
• Largest error: ${abs_errors.max():,.0f}

Pattern:
• {'Normal' if abs(errors.mean()) < errors.std()/2 else 'Biased'} distribution
• Model {'underestimates' if errors.mean() > 0 else 'overestimates'} on average
"""

plt.text(0.05, 0.95, pattern_analysis, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=10, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

print("PATTERN ANALYSIS SUMMARY:")
print("=" * 35)
print(f"The error distribution shows:")
print(f"1. Average error: ${abs_errors.mean():,.0f}")
print(f"2. Most errors are between ${np.percentile(abs_errors, 25):,.0f} and ${np.percentile(abs_errors, 75):,.0f}")
print(f"3. Model bias: {'Slight underestimation' if errors.mean() > 1000 else 'Slight overestimation' if errors.mean() < -1000 else 'Well calibrated'}")
print(f"4. Error pattern: {'Consistent across sizes' if abs(np.corrcoef(X_train.iloc[:, 0], abs_errors)[0,1]) < 0.3 else 'Varies with apartment size'}")
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Filter by bedrooms and rebuild model}
\begin{lstlisting}[caption=Bedroom-Filtered Model Analysis]
# Filter data for apartments with 1-3 bedrooms
if 'bedrooms' in df.columns:
    bedroom_col = 'bedrooms'
elif 'rooms' in df.columns:
    bedroom_col = 'rooms'
else:
    # Create simulated bedroom data based on size
    df['bedrooms_estimated'] = pd.cut(df['surface_covered_in_m2'], 
                                     bins=[0, 40, 70, 100, 200], 
                                     labels=[1, 2, 3, 4])
    bedroom_col = 'bedrooms_estimated'

# Filter for 1-3 bedrooms
df_filtered = df[df[bedroom_col].isin([1, 2, 3])].copy()

print(f"Original dataset: {len(df)} apartments")
print(f"Filtered dataset (1-3 bedrooms): {len(df_filtered)} apartments")

# Analyze bedroom distribution
print(f"\nBedroom distribution:")
print(df_filtered[bedroom_col].value_counts().sort_index())

# Build separate models for each bedroom count
bedroom_models = {}
bedroom_performance = {}

for bedrooms in [1, 2, 3]:
    bedroom_data = df_filtered[df_filtered[bedroom_col] == bedrooms]
    
    if len(bedroom_data) >= 10:  # Need minimum data points
        X_bed = bedroom_data[["surface_covered_in_m2"]]
        y_bed = bedroom_data["price_aprox_usd"]
        
        # Train model
        model_bed = LinearRegression()
        model_bed.fit(X_bed, y_bed)
        bedroom_models[bedrooms] = model_bed
        
        # Evaluate
        pred_bed = model_bed.predict(X_bed)
        mae_bed = mean_absolute_error(y_bed, pred_bed)
        bedroom_performance[bedrooms] = mae_bed
        
        print(f"\n{bedrooms}-bedroom apartments:")
        print(f"  Count: {len(bedroom_data)}")
        print(f"  Price per sq meter: ${model_bed.coef_[0]:,.0f}")
        print(f"  Base price: ${model_bed.intercept_:,.0f}")
        print(f"  MAE: ${mae_bed:,.0f}")

# Compare with original model
X_filtered = df_filtered[["surface_covered_in_m2"]]
y_filtered = df_filtered["price_aprox_usd"]
model_filtered = LinearRegression()
model_filtered.fit(X_filtered, y_filtered)
pred_filtered = model_filtered.predict(X_filtered)
mae_filtered = mean_absolute_error(y_filtered, pred_filtered)

print(f"\nModel Comparison:")
print(f"Original model MAE: ${mean_absolute_error(y_train, predictions):,.0f}")
print(f"Filtered model MAE: ${mae_filtered:,.0f}")
print(f"Improvement: {((mean_absolute_error(y_train, predictions) - mae_filtered) / mean_absolute_error(y_train, predictions) * 100):.1f}%")

# Visualize bedroom-specific models
plt.figure(figsize=(12, 8))
colors = ['red', 'blue', 'green']

for i, bedrooms in enumerate([1, 2, 3]):
    if bedrooms in bedroom_models:
        bedroom_data = df_filtered[df_filtered[bedroom_col] == bedrooms]
        
        plt.subplot(2, 2, i+1)
        plt.scatter(bedroom_data["surface_covered_in_m2"], 
                   bedroom_data["price_aprox_usd"], 
                   alpha=0.6, color=colors[i])
        
        # Plot model line
        x_range = np.linspace(bedroom_data["surface_covered_in_m2"].min(),
                             bedroom_data["surface_covered_in_m2"].max(), 100)
        y_range = bedroom_models[bedrooms].predict(x_range.reshape(-1, 1))
        plt.plot(x_range, y_range, color='black', linewidth=2)
        
        plt.xlabel("Size (sq meters)")
        plt.ylabel("Price (USD)")
        plt.title(f"{bedrooms}-Bedroom Apartments")
        plt.grid(True, alpha=0.3)

# All bedrooms together
plt.subplot(2, 2, 4)
for i, bedrooms in enumerate([1, 2, 3]):
    if bedrooms in bedroom_models:
        bedroom_data = df_filtered[df_filtered[bedroom_col] == bedrooms]
        plt.scatter(bedroom_data["surface_covered_in_m2"], 
                   bedroom_data["price_aprox_usd"], 
                   alpha=0.6, color=colors[i], label=f'{bedrooms} bedrooms')

plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title("All Bedroom Types")
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 2: Models for different price ranges}
\begin{lstlisting}[caption=Price Range Specific Models]
# Define price ranges
price_ranges = {
    "Budget": (0, 100000),
    "Mid-Range": (100000, 200000),
    "Premium": (200000, 300000),
    "Luxury": (300000, float('inf'))
}

range_models = {}
range_performance = {}

print("PRICE RANGE ANALYSIS")
print("=" * 35)

for range_name, (min_price, max_price) in price_ranges.items():
    # Filter data for this price range
    range_data = df[(df["price_aprox_usd"] >= min_price) & 
                    (df["price_aprox_usd"] < max_price)]
    
    if len(range_data) >= 10:  # Need minimum data
        X_range = range_data[["surface_covered_in_m2"]]
        y_range = range_data["price_aprox_usd"]
        
        # Train model
        model_range = LinearRegression()
        model_range.fit(X_range, y_range)
        range_models[range_name] = model_range
        
        # Evaluate
        pred_range = model_range.predict(X_range)
        mae_range = mean_absolute_error(y_range, pred_range)
        r2_range = model_range.score(X_range, y_range)
        range_performance[range_name] = {'mae': mae_range, 'r2': r2_range}
        
        print(f"\n{range_name} (${min_price:,} - ${max_price:,}):")
        print(f"  Apartments: {len(range_data)}")
        print(f"  Average size: {range_data['surface_covered_in_m2'].mean():.0f} sq meters")
        print(f"  Average price: ${range_data['price_aprox_usd'].mean():,.0f}")
        print(f"  Price per sq meter: ${model_range.coef_[0]:,.0f}")
        print(f"  Base price: ${model_range.intercept_:,.0f}")
        print(f"  MAE: ${mae_range:,.0f}")
        print(f"  R²: {r2_range:.3f}")

# Visualize range-specific models
plt.figure(figsize=(15, 10))

colors = ['blue', 'green', 'orange', 'red']
for i, (range_name, model_range) in enumerate(range_models.items()):
    min_price, max_price = price_ranges[range_name]
    range_data = df[(df["price_aprox_usd"] >= min_price) & 
                    (df["price_aprox_usd"] < max_price)]
    
    plt.subplot(2, 3, i+1)
    plt.scatter(range_data["surface_covered_in_m2"], 
               range_data["price_aprox_usd"], 
               alpha=0.6, color=colors[i])
    
    # Plot model line
    x_range = np.linspace(range_data["surface_covered_in_m2"].min(),
                         range_data["surface_covered_in_m2"].max(), 100)
    y_range_pred = model_range.predict(x_range.reshape(-1, 1))
    plt.plot(x_range, y_range_pred, color='black', linewidth=2)
    
    plt.xlabel("Size (sq meters)")
    plt.ylabel("Price (USD)")
    plt.title(f"{range_name} Apartments")
    plt.grid(True, alpha=0.3)

# Performance comparison
plt.subplot(2, 3, 5)
range_names = list(range_performance.keys())
maes = [range_performance[name]['mae'] for name in range_names]
r2s = [range_performance[name]['r2'] for name in range_names]

x_pos = np.arange(len(range_names))
plt.bar(x_pos, maes, color='lightcoral', alpha=0.7)
plt.xlabel("Price Range")
plt.ylabel("Mean Absolute Error (USD)")
plt.title("Model Accuracy by Price Range")
plt.xticks(x_pos, range_names, rotation=45)

# Add value labels
for i, mae in enumerate(maes):
    plt.text(i, mae + max(maes)*0.01, f'${mae:,.0f}', ha='center', va='bottom')

plt.subplot(2, 3, 6)
plt.bar(x_pos, r2s, color='lightblue', alpha=0.7)
plt.xlabel("Price Range")
plt.ylabel("R² Score")
plt.title("Model Fit by Price Range")
plt.xticks(x_pos, range_names, rotation=45)
plt.ylim(0, 1)

# Add value labels
for i, r2 in enumerate(r2s):
    plt.text(i, r2 + 0.02, f'{r2:.3f}', ha='center', va='bottom')

plt.tight_layout()
plt.show()

# Summary insights
print(f"\nKEY INSIGHTS:")
best_r2_range = max(range_performance.keys(), key=lambda x: range_performance[x]['r2'])
best_mae_range = min(range_performance.keys(), key=lambda x: range_performance[x]['mae'])
print(f"• Best fit (R²): {best_r2_range} range")
print(f"• Most accurate (MAE): {best_mae_range} range")
print(f"• Size-price relationship varies significantly across price ranges")
\end{lstlisting}

\textbf{Exercise 3: Calculate and interpret R-squared}
\begin{lstlisting}[caption=R-squared Analysis and Interpretation]
from sklearn.metrics import r2_score

# Calculate R-squared for our model
r2 = r2_score(y_train, predictions)
r2_sklearn = model.score(X_train, y_train)  # Alternative calculation

print("R-SQUARED ANALYSIS")
print("=" * 30)
print(f"R² using r2_score: {r2:.4f}")
print(f"R² using model.score: {r2_sklearn:.4f}")
print(f"R² as percentage: {r2*100:.2f}%")

# Manual calculation to understand the formula
ss_total = np.sum((y_train - y_train.mean())**2)  # Total sum of squares
ss_residual = np.sum((y_train - predictions)**2)  # Residual sum of squares
r2_manual = 1 - (ss_residual / ss_total)

print(f"\nManual calculation:")
print(f"Total Sum of Squares (TSS): {ss_total:,.0f}")
print(f"Residual Sum of Squares (RSS): {ss_residual:,.0f}")
print(f"R² = 1 - (RSS/TSS) = {r2_manual:.4f}")

# Interpretation framework
def interpret_r2(r2_value):
    if r2_value >= 0.9:
        return "Excellent fit - model explains most price variation"
    elif r2_value >= 0.7:
        return "Good fit - model explains most price variation"
    elif r2_value >= 0.5:
        return "Moderate fit - model explains some price variation"
    elif r2_value >= 0.3:
        return "Weak fit - model explains little price variation"
    else:
        return "Poor fit - model barely better than average"

interpretation = interpret_r2(r2)
unexplained_variance = (1 - r2) * 100

print(f"\nINTERPRETATION:")
print(f"• {interpretation}")
print(f"• Model explains {r2*100:.1f}% of price variation")
print(f"• {unexplained_variance:.1f}% of price variation is unexplained")
print(f"• For every $100 of price variation, our model explains ${r2*100:.0f}")

# Visualize R-squared concept
plt.figure(figsize=(15, 5))

# Plot 1: Actual vs Predicted
plt.subplot(1, 3, 1)
plt.scatter(y_train, predictions, alpha=0.6, color='blue')
plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 
         'r--', linewidth=2, label='Perfect predictions (R²=1.0)')
plt.xlabel("Actual Price")
plt.ylabel("Predicted Price")
plt.title(f"Actual vs Predicted\nR² = {r2:.3f}")
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Residuals vs Fitted
plt.subplot(1, 3, 2)
residuals = y_train - predictions
plt.scatter(predictions, residuals, alpha=0.6, color='green')
plt.axhline(0, color='red', linestyle='--', linewidth=2)
plt.xlabel("Predicted Price")
plt.ylabel("Residuals (Actual - Predicted)")
plt.title("Residual Plot")
plt.grid(True, alpha=0.3)

# Plot 3: Explained vs Unexplained Variance
plt.subplot(1, 3, 3)
labels = ['Explained by Model', 'Unexplained']
sizes = [r2*100, (1-r2)*100]
colors = ['lightgreen', 'lightcoral']
plt.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
plt.title('Variance Explanation')

plt.tight_layout()
plt.show()

# Compare with baseline models
baseline_predictions_mean = np.full_like(y_train, y_train.mean())
baseline_predictions_median = np.full_like(y_train, y_train.median())

r2_baseline_mean = r2_score(y_train, baseline_predictions_mean)
r2_baseline_median = r2_score(y_train, baseline_predictions_median)

print(f"\nCOMPARISON WITH BASELINES:")
print(f"Our model R²: {r2:.4f}")
print(f"Always predict mean R²: {r2_baseline_mean:.4f}")
print(f"Always predict median R²: {r2_baseline_median:.4f}")
print(f"Improvement over mean baseline: {r2 - r2_baseline_mean:.4f}")

# What affects R-squared
print(f"\nFACTORS AFFECTING R²:")
print(f"• Perfect correlation (r=1.0) → R²=1.0")
print(f"• Our correlation: r={np.corrcoef(X_train.iloc[:, 0], y_train)[0,1]:.3f}")
print(f"• R² ≈ correlation² = {np.corrcoef(X_train.iloc[:, 0], y_train)[0,1]**2:.3f}")
print(f"• Actual R²: {r2:.3f}")
print(f"• Difference due to model assumptions and data patterns")
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Bedrooms vs Size model comparison}
\begin{lstlisting}[caption=Bedrooms vs Size Model Comparison]
# Prepare bedroom data (simulate if not available)
if 'bedrooms' not in df.columns:
    # Estimate bedrooms based on size
    df['bedrooms'] = pd.cut(df['surface_covered_in_m2'], 
                           bins=[0, 45, 70, 100, float('inf')], 
                           labels=[1, 2, 3, 4])
    df['bedrooms'] = df['bedrooms'].astype(float)

# Remove rows with missing bedroom data
df_clean = df.dropna(subset=['bedrooms']).copy()

print("BEDROOMS vs SIZE MODEL COMPARISON")
print("=" * 45)

# Model 1: Size-based (our original)
X_size = df_clean[["surface_covered_in_m2"]]
y_price = df_clean["price_aprox_usd"]

model_size = LinearRegression()
model_size.fit(X_size, y_price)
pred_size = model_size.predict(X_size)
mae_size = mean_absolute_error(y_price, pred_size)
r2_size = r2_score(y_price, pred_size)

# Model 2: Bedroom-based
X_bedrooms = df_clean[["bedrooms"]]
model_bedrooms = LinearRegression()
model_bedrooms.fit(X_bedrooms, y_price)
pred_bedrooms = model_bedrooms.predict(X_bedrooms)
mae_bedrooms = mean_absolute_error(y_price, pred_bedrooms)
r2_bedrooms = r2_score(y_price, pred_bedrooms)

print(f"Dataset: {len(df_clean)} apartments")
print(f"\nSIZE-BASED MODEL:")
print(f"  MAE: ${mae_size:,.0f}")
print(f"  R²: {r2_size:.4f}")
print(f"  Interpretation: Each sq meter adds ${model_size.coef_[0]:,.0f}")

print(f"\nBEDROOM-BASED MODEL:")
print(f"  MAE: ${mae_bedrooms:,.0f}")
print(f"  R²: {r2_bedrooms:.4f}")
print(f"  Interpretation: Each bedroom adds ${model_bedrooms.coef_[0]:,.0f}")

# Determine which is better
better_model = "Size" if mae_size < mae_bedrooms else "Bedrooms"
improvement = abs(mae_size - mae_bedrooms) / max(mae_size, mae_bedrooms) * 100

print(f"\nWINNER: {better_model}-based model")
print(f"Improvement: {improvement:.1f}% better MAE")

# Detailed analysis
correlations = {
    'Size-Price': np.corrcoef(df_clean['surface_covered_in_m2'], df_clean['price_aprox_usd'])[0,1],
    'Bedrooms-Price': np.corrcoef(df_clean['bedrooms'], df_clean['price_aprox_usd'])[0,1],
    'Size-Bedrooms': np.corrcoef(df_clean['surface_covered_in_m2'], df_clean['bedrooms'])[0,1]
}

print(f"\nCORRELATION ANALYSIS:")
for pair, corr in correlations.items():
    print(f"  {pair}: {corr:.3f}")

# Visualize comparison
plt.figure(figsize=(15, 10))

# Size-based model
plt.subplot(2, 3, 1)
plt.scatter(df_clean['surface_covered_in_m2'], df_clean['price_aprox_usd'], 
           alpha=0.6, color='blue')
x_range = np.linspace(df_clean['surface_covered_in_m2'].min(), 
                     df_clean['surface_covered_in_m2'].max(), 100)
y_range = model_size.predict(x_range.reshape(-1, 1))
plt.plot(x_range, y_range, color='red', linewidth=2)
plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title(f"Size Model (R²={r2_size:.3f})")
plt.grid(True, alpha=0.3)

# Bedroom-based model
plt.subplot(2, 3, 2)
plt.scatter(df_clean['bedrooms'], df_clean['price_aprox_usd'], 
           alpha=0.6, color='green')
# For categorical x-axis, create line between points
bedroom_means = df_clean.groupby('bedrooms')['price_aprox_usd'].mean()
plt.plot(bedroom_means.index, bedroom_means.values, 'ro-', linewidth=2, markersize=8)
plt.xlabel("Number of Bedrooms")
plt.ylabel("Price (USD)")
plt.title(f"Bedroom Model (R²={r2_bedrooms:.3f})")
plt.grid(True, alpha=0.3)

# Prediction accuracy comparison
plt.subplot(2, 3, 3)
models = ['Size Model', 'Bedroom Model']
maes = [mae_size, mae_bedrooms]
r2s = [r2_size, r2_bedrooms]

x_pos = np.arange(len(models))
bars = plt.bar(x_pos, maes, color=['blue', 'green'], alpha=0.7)
plt.xlabel("Model Type")
plt.ylabel("Mean Absolute Error (USD)")
plt.title("Model Accuracy Comparison")
plt.xticks(x_pos, models)

for bar, mae in zip(bars, maes):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${mae:,.0f}', ha='center', va='bottom')

# Residual analysis
plt.subplot(2, 3, 4)
residuals_size = y_price - pred_size
plt.hist(residuals_size, bins=25, alpha=0.6, color='blue', label='Size Model')
plt.xlabel("Residuals")
plt.ylabel("Frequency")
plt.title("Size Model Residuals")
plt.axvline(0, color='red', linestyle='--')
plt.grid(axis='y', alpha=0.3)

plt.subplot(2, 3, 5)
residuals_bedrooms = y_price - pred_bedrooms
plt.hist(residuals_bedrooms, bins=25, alpha=0.6, color='green', label='Bedroom Model')
plt.xlabel("Residuals")
plt.ylabel("Frequency")
plt.title("Bedroom Model Residuals")
plt.axvline(0, color='red', linestyle='--')
plt.grid(axis='y', alpha=0.3)

# Model insights
plt.subplot(2, 3, 6)
insights_text = f"""
MODEL COMPARISON INSIGHTS:

Size Model:
• MAE: ${mae_size:,.0f}
• R²: {r2_size:.3f}
• $/sqm: ${model_size.coef_[0]:,.0f}

Bedroom Model:
• MAE: ${mae_bedrooms:,.0f}
• R²: {r2_bedrooms:.3f}
• $/bedroom: ${model_bedrooms.coef_[0]:,.0f}

Winner: {better_model} Model
Improvement: {improvement:.1f}%

Why {better_model.lower()} works better:
• Higher correlation with price
• More continuous measurement
• {"Captures size nuances" if better_model=="Size" else "Simpler, more intuitive"}
"""

plt.text(0.05, 0.95, insights_text, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=9, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Business implications
print(f"\nBUSINESS IMPLICATIONS:")
if better_model == "Size":
    print(f"• Size is a better predictor → Focus on sq meter pricing")
    print(f"• Room count matters less → Open floor plans could work")
    print(f"• Marketing: Emphasize total space over room count")
else:
    print(f"• Room count is better predictor → Bedroom count crucial")
    print(f"• Size matters less → Efficient layouts preferred")
    print(f"• Marketing: Emphasize number of bedrooms")

print(f"• Combined model might be even better (future project!)")
\end{lstlisting}

\textbf{Exercise 2: Polynomial regression comparison}
\begin{lstlisting}[caption=Linear vs Polynomial Regression Comparison]
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline

print("LINEAR vs POLYNOMIAL REGRESSION COMPARISON")
print("=" * 50)

# Prepare data
X_size = df[["surface_covered_in_m2"]].dropna()
y_price = df["price_aprox_usd"][X_size.index]

# Linear model (degree 1)
model_linear = LinearRegression()
model_linear.fit(X_size, y_price)
pred_linear = model_linear.predict(X_size)
mae_linear = mean_absolute_error(y_price, pred_linear)
r2_linear = r2_score(y_price, pred_linear)

# Polynomial models (degrees 2, 3)
models_poly = {}
predictions_poly = {}
performance_poly = {}

for degree in [2, 3]:
    # Create polynomial features
    model_poly = make_pipeline(PolynomialFeatures(degree), LinearRegression())
    model_poly.fit(X_size, y_price)
    
    pred_poly = model_poly.predict(X_size)
    mae_poly = mean_absolute_error(y_price, pred_poly)
    r2_poly = r2_score(y_price, pred_poly)
    
    models_poly[degree] = model_poly
    predictions_poly[degree] = pred_poly
    performance_poly[degree] = {'mae': mae_poly, 'r2': r2_poly}
    
    print(f"\nDEGREE {degree} POLYNOMIAL:")
    print(f"  MAE: ${mae_poly:,.0f}")
    print(f"  R²: {r2_poly:.4f}")
    print(f"  Improvement over linear: {((mae_linear - mae_poly)/mae_linear*100):.1f}%")

print(f"\nLINEAR MODEL (Baseline):")
print(f"  MAE: ${mae_linear:,.0f}")
print(f"  R²: {r2_linear:.4f}")

# Find best model
all_models = {1: {'mae': mae_linear, 'r2': r2_linear}}
all_models.update(performance_poly)

best_degree = min(all_models.keys(), key=lambda x: all_models[x]['mae'])
best_mae = all_models[best_degree]['mae']
best_r2 = all_models[best_degree]['r2']

print(f"\nBEST MODEL: Degree {best_degree}")
print(f"  MAE: ${best_mae:,.0f}")
print(f"  R²: {best_r2:.4f}")

# Visualize comparison
plt.figure(figsize=(15, 10))

# Plot 1: All models together
plt.subplot(2, 3, 1)
plt.scatter(X_size, y_price, alpha=0.5, color='lightblue', s=20, label='Data')

# Create smooth curves for visualization
x_smooth = np.linspace(X_size.min().values[0], X_size.max().values[0], 300).reshape(-1, 1)

# Linear
y_linear_smooth = model_linear.predict(x_smooth)
plt.plot(x_smooth, y_linear_smooth, color='red', linewidth=2, label=f'Linear (R²={r2_linear:.3f})')

# Polynomials
colors = ['green', 'purple']
for i, degree in enumerate([2, 3]):
    y_poly_smooth = models_poly[degree].predict(x_smooth)
    plt.plot(x_smooth, y_poly_smooth, color=colors[i], linewidth=2, 
             label=f'Degree {degree} (R²={performance_poly[degree]["r2"]:.3f})')

plt.xlabel("Size (sq meters)")
plt.ylabel("Price (USD)")
plt.title("Model Comparison")
plt.legend()
plt.grid(True, alpha=0.3)

# Performance comparison
plt.subplot(2, 3, 2)
degrees = [1, 2, 3]
maes = [mae_linear] + [performance_poly[d]['mae'] for d in [2, 3]]
r2s = [r2_linear] + [performance_poly[d]['r2'] for d in [2, 3]]

bars = plt.bar(degrees, maes, color=['red', 'green', 'purple'], alpha=0.7)
plt.xlabel("Polynomial Degree")
plt.ylabel("Mean Absolute Error (USD)")
plt.title("Model Accuracy by Complexity")
plt.xticks(degrees)

for bar, mae in zip(bars, maes):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${mae:,.0f}', ha='center', va='bottom')

# R² comparison
plt.subplot(2, 3, 3)
plt.bar(degrees, r2s, color=['red', 'green', 'purple'], alpha=0.7)
plt.xlabel("Polynomial Degree")
plt.ylabel("R² Score")
plt.title("Model Fit by Complexity")
plt.xticks(degrees)
plt.ylim(0, max(r2s) * 1.1)

for i, r2 in enumerate(r2s):
    plt.text(i, r2 + 0.01, f'{r2:.3f}', ha='center', va='bottom')

# Residual analysis for best model
plt.subplot(2, 3, 4)
if best_degree == 1:
    best_predictions = pred_linear
else:
    best_predictions = predictions_poly[best_degree]

residuals = y_price - best_predictions
plt.scatter(best_predictions, residuals, alpha=0.6, color='blue')
plt.axhline(0, color='red', linestyle='--', linewidth=2)
plt.xlabel("Predicted Price")
plt.ylabel("Residuals")
plt.title(f"Residuals - Degree {best_degree} Model")
plt.grid(True, alpha=0.3)

# Overfitting check - compare train vs validation performance
from sklearn.model_selection import train_test_split

X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
    X_size, y_price, test_size=0.3, random_state=42)

train_scores = []
val_scores = []

for degree in [1, 2, 3, 4, 5]:
    if degree == 1:
        model = LinearRegression()
        model.fit(X_train_split, y_train_split)
        train_pred = model.predict(X_train_split)
        val_pred = model.predict(X_val_split)
    else:
        model = make_pipeline(PolynomialFeatures(degree), LinearRegression())
        model.fit(X_train_split, y_train_split)
        train_pred = model.predict(X_train_split)
        val_pred = model.predict(X_val_split)
    
    train_score = r2_score(y_train_split, train_pred)
    val_score = r2_score(y_val_split, val_pred)
    
    train_scores.append(train_score)
    val_scores.append(val_score)

plt.subplot(2, 3, 5)
degrees_extended = [1, 2, 3, 4, 5]
plt.plot(degrees_extended, train_scores, 'o-', color='blue', label='Training R²')
plt.plot(degrees_extended, val_scores, 'o-', color='red', label='Validation R²')
plt.xlabel("Polynomial Degree")
plt.ylabel("R² Score")
plt.title("Overfitting Analysis")
plt.legend()
plt.grid(True, alpha=0.3)
plt.xticks(degrees_extended)

# Summary insights
plt.subplot(2, 3, 6)
summary_text = f"""
POLYNOMIAL REGRESSION ANALYSIS:

Best Model: Degree {best_degree}
Best MAE: ${best_mae:,.0f}")
Best R²: {best_r2:.3f}")

Key Findings:
• Linear: Simple, interpretable
• Quadratic: Captures curves
• Cubic: More complex patterns

Overfitting Risk:
• Degree 4+: Training >> Validation
• Degree {best_degree}: Good balance

Recommendation:
Use degree {best_degree} polynomial
{"(linear is sufficient)" if best_degree == 1 else "(captures non-linearity)"}

Business Impact:
{"Linear relationship confirmed" if best_degree == 1 else "Price increases faster for larger apartments"}
"""

plt.text(0.05, 0.95, summary_text, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=9, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Statistical significance test
from scipy.stats import f_oneway

print(f"\nSTATISTICAL COMPARISON:")
linear_residuals = abs(y_price - pred_linear)
if 2 in predictions_poly:
    quad_residuals = abs(y_price - predictions_poly[2])
    improvement_significant = np.mean(linear_residuals) - np.mean(quad_residuals)
    print(f"Average error reduction: ${improvement_significant:,.0f}")
    print(f"Relative improvement: {improvement_significant/np.mean(linear_residuals)*100:.1f}%")
\end{lstlisting}

\section{Next Steps in Your ML Journey}

\textbf{Coming up in future projects:}
\begin{itemize}
    \item \textbf{Project 2.2:} Using multiple features (latitude + longitude)
    \item \textbf{Project 2.3:} Working with categorical data (neighborhood names)
    \item \textbf{Advanced topics:} Cross-validation, feature engineering, ensemble methods
    \item \textbf{Other domains:} Image recognition, natural language processing, time series
\end{itemize}

Congratulations! You've built your first machine learning model and taken your first step into the exciting world of data science and artificial intelligence.

\end{document} 