{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project 1.1: Organizing Tabular Data in Python\n", "## WorldQuant University - Applied Data Science Lab\n", "\n", "### Learning Objectives\n", "- Understand what tabular data is and how to organize it\n", "- Work with Python data structures: lists and dictionaries\n", "- Learn the principles of \"tidy data\"\n", "- Introduction to pandas DataFrames\n", "- Practice data manipulation and calculations\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## What's Tabular Data?\n", "\n", "Information can come in many forms, and part of a data scientist's job is making sure that information is organized in a way that's conducive to analysis. \n", "\n", "Consider these five houses from the Mexico real estate dataset:\n", "- House 1: $115,910.26, 128 m², 4 rooms\n", "- House 2: $48,718.17, 210 m², 3 rooms\n", "- House 3: $28,977.56, 58 m², 2 rooms\n", "- House 4: $36,932.27, 79 m², 3 rooms\n", "- House 5: $83,903.51, 111 m², 3 rooms\n", "\n", "### Principles of \"Tidy Data\":\n", "1. **Each row** corresponds to a single observation (house)\n", "2. **Each column** corresponds to a feature (price, area, rooms)\n", "3. **Each cell** contains only one value"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with Lists\n", "\n", "Python comes with several data structures that we can use to organize tabular data. Let's start by putting a single observation in a list."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Declare variable `house_0_list`\n", "house_0_list = [115910.26, 128, 4]\n", "\n", "# Print object type of `house_0_list`\n", "print(\"house_0_list type:\", type(house_0_list))\n", "\n", "# Print length of `house_0_list`\n", "print(\"house_0_list length:\", len(house_0_list))\n", "\n", "# Get output of `house_0_list`\n", "house_0_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.1: Calculate Price per Square Meter\n", "\n", "One metric that people in the real estate industry look at is price per square meter because it allows them to compare houses of different sizes. Can you use the information in this list to calculate the price per square meter for house_0?\n", "\n", "**Learning Goals:**\n", "- Access an item in a list using Python\n", "- Perform basic mathematical operations in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate price per square meter\n", "house_0_price_m2 = house_0_list[0] / house_0_list[1]\n", "\n", "# Print object type\n", "print(\"house_0_price_m2 type:\", type(house_0_price_m2))\n", "\n", "# Display result\n", "print(f\"Price per m²: ${house_0_price_m2:.2f}\")\n", "house_0_price_m2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.2: Append Price per Square Meter\n", "\n", "Next, use the append method to add the price per square meter to the end of house_0.\n", "\n", "**Learning Goal:** Append an item to a list in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Append price per m² to house_0_list\n", "house_0_list.append(house_0_price_m2)\n", "\n", "# Print updated information\n", "print(\"house_0_list type:\", type(house_0_list))\n", "print(\"house_0_list length:\", len(house_0_list))\n", "print(\"Updated house_0_list:\", house_0_list)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with Nested Lists\n", "\n", "Now that you can work with data for a single house, let's think about how to organize the whole dataset. One option would be to create a list for each observation and then put those together in another list. This is called a **nested list**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create nested list with all houses\n", "houses_nested_list = [\n", "    [115910.26, 128.0, 4.0],\n", "    [48718.17, 210.0, 3.0],\n", "    [28977.56, 58.0, 2.0],\n", "    [36932.27, 79.0, 3.0],\n", "    [83903.51, 111.0, 3.0],\n", "]\n", "\n", "# Print information about the nested list\n", "print(\"houses_nested_list type:\", type(houses_nested_list))\n", "print(\"houses_nested_list length:\", len(houses_nested_list))\n", "print(\"\\nFirst house:\", houses_nested_list[0])\n", "print(\"All houses:\")\n", "for i, house in enumerate(houses_nested_list):\n", "    print(f\"  House {i+1}: {house}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.3: Calculate Price per Square Meter for All Houses\n", "\n", "Instead of calculating the price per square meter for each house one-by-one, we can automate this repetitive task using a **for loop**.\n", "\n", "**Learning Goals:**\n", "- Understand what a for loop is\n", "- Write a for loop in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create for loop to iterate through houses_nested_list\n", "for house in houses_nested_list:\n", "    # Calculate price per m² and append to each house\n", "    price_per_m2 = house[0] / house[1]\n", "    house.append(price_per_m2)\n", "\n", "# Display results\n", "print(\"Updated houses with price per m²:\")\n", "for i, house in enumerate(houses_nested_list):\n", "    print(f\"  House {i+1}: ${house[0]:,.2f}, {house[1]} m², {house[2]} rooms, ${house[3]:.2f}/m²\")\n", "\n", "print(\"\\nFull nested list:\")\n", "houses_nested_list"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with Dictionaries\n", "\n", "Lists are good for organizing data, but one drawback is that we can only represent values. A better option might be a **dictionary**, where each value is associated with a key. This makes the data more self-explanatory."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create dictionary for house_0\n", "house_0_dict = {\n", "    \"price_approx_usd\": 115910.26,\n", "    \"surface_covered_in_m2\": 128,\n", "    \"rooms\": 4,\n", "}\n", "\n", "# Print information\n", "print(\"house_0_dict type:\", type(house_0_dict))\n", "print(\"\\nHouse 0 information:\")\n", "for key, value in house_0_dict.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "house_0_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.4: Add Price per Square Meter to Dictionary\n", "\n", "Calculate the price per square meter for house_0 and add it to the dictionary under the key \"price_per_m2\".\n", "\n", "**Learning Goals:**\n", "- Understand what a dictionary is\n", "- Access an item in a dictionary in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add \"price_per_m2\" key-value pair to house_0_dict\n", "house_0_dict[\"price_per_m2\"] = house_0_dict[\"price_approx_usd\"] / house_0_dict[\"surface_covered_in_m2\"]\n", "\n", "# Display updated dictionary\n", "print(\"Updated house_0_dict:\")\n", "for key, value in house_0_dict.items():\n", "    if key == \"price_per_m2\":\n", "        print(f\"  {key}: ${value:.2f}\")\n", "    else:\n", "        print(f\"  {key}: {value}\")\n", "\n", "house_0_dict"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Working with List of Dictionaries (JSON Format)\n", "\n", "If we wanted to combine all our observations together, the best way would be to create a **list of dictionaries**. This way of storing data is so popular, it has its own name: **JSON**."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create list of dictionaries (JSON format)\n", "houses_rowwise = [\n", "    {\n", "        \"price_approx_usd\": 115910.26,\n", "        \"surface_covered_in_m2\": 128,\n", "        \"rooms\": 4,\n", "    },\n", "    {\n", "        \"price_approx_usd\": 48718.17,\n", "        \"surface_covered_in_m2\": 210,\n", "        \"rooms\": 3,\n", "    },\n", "    {\n", "        \"price_approx_usd\": 28977.56,\n", "        \"surface_covered_in_m2\": 58,\n", "        \"rooms\": 2,\n", "    },\n", "    {\n", "        \"price_approx_usd\": 36932.27,\n", "        \"surface_covered_in_m2\": 79,\n", "        \"rooms\": 3,\n", "    },\n", "    {\n", "        \"price_approx_usd\": 83903.51,\n", "        \"surface_covered_in_m2\": 111,\n", "        \"rooms\": 3,\n", "    },\n", "]\n", "\n", "# Print information\n", "print(\"houses_rowwise type:\", type(houses_rowwise))\n", "print(\"houses_rowwise length:\", len(houses_rowwise))\n", "print(\"\\nFirst house dictionary:\")\n", "print(houses_rowwise[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.5: Add Price per Square Meter Using a For Loop\n", "\n", "Using a for loop, calculate the price per square meter and store the result under a \"price_per_m2\" key for each observation in houses_rowwise.\n", "\n", "**Learning Goals:**\n", "- Understand JSON format\n", "- Write a for loop in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create for loop to iterate through houses_rowwise\n", "for house in houses_rowwise:\n", "    # Calculate and add price_per_m2 for each house\n", "    house[\"price_per_m2\"] = house[\"price_approx_usd\"] / house[\"surface_covered_in_m2\"]\n", "\n", "# Display results\n", "print(\"houses_rowwise type:\", type(houses_rowwise))\n", "print(\"houses_rowwise length:\", len(houses_rowwise))\n", "print(\"\\nUpdated houses with price per m²:\")\n", "for i, house in enumerate(houses_rowwise):\n", "    print(f\"\\n  House {i+1}:\")\n", "    for key, value in house.items():\n", "        if \"price\" in key and key != \"rooms\":\n", "            print(f\"    {key}: ${value:,.2f}\")\n", "        else:\n", "            print(f\"    {key}: {value}\")\n", "\n", "houses_rowwise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.6: Calculate Mean House Price\n", "\n", "JSON is great for organizing data, but column-wise calculations can be more complicated. Let's calculate the mean house price for our dataset.\n", "\n", "**Learning Goals:**\n", "- Write a for loop in Python\n", "- Append an item to a list in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create empty list for house prices\n", "house_prices = []\n", "\n", "# Iterate through houses_rowwise and collect prices\n", "for house in houses_rowwise:\n", "    house_prices.append(house[\"price_approx_usd\"])\n", "\n", "# Calculate mean house price\n", "mean_house_price = sum(house_prices) / len(house_prices)\n", "\n", "# Display results\n", "print(\"House prices:\", house_prices)\n", "print(f\"\\nMean house price: ${mean_house_price:,.2f}\")\n", "print(\"mean_house_price type:\", type(mean_house_price))\n", "\n", "mean_house_price"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Column-wise Data Organization\n", "\n", "One way to make column-wise calculations easier is to organize our data by **features** instead of observations. We'll still use dictionaries and lists, but implement them differently."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Organize data by columns/features\n", "houses_columnwise = {\n", "    \"price_approx_usd\": [115910.26, 48718.17, 28977.56, 36932.27, 83903.51],\n", "    \"surface_covered_in_m2\": [128.0, 210.0, 58.0, 79.0, 111.0],\n", "    \"rooms\": [4.0, 3.0, 2.0, 3.0, 3.0],\n", "}\n", "\n", "# Print information\n", "print(\"houses_columnwise type:\", type(houses_columnwise))\n", "print(\"\\nColumn-wise organization:\")\n", "for key, values in houses_columnwise.items():\n", "    print(f\"  {key}: {values}\")\n", "\n", "houses_columnwise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.7: Calculate Mean Price Using Column-wise Data\n", "\n", "Calculate the mean house price using the column-wise organized data.\n", "\n", "**Learning Goal:** Perform common aggregation tasks on a list in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate mean house price using column-wise data\n", "mean_house_price_col = sum(houses_columnwise[\"price_approx_usd\"]) / len(houses_columnwise[\"price_approx_usd\"])\n", "\n", "print(f\"Mean house price (column-wise): ${mean_house_price_col:,.2f}\")\n", "print(\"mean_house_price type:\", type(mean_house_price_col))\n", "\n", "# Verify it's the same as row-wise calculation\n", "print(f\"\\nVerification:\")\n", "print(f\"Row-wise mean: ${mean_house_price:,.2f}\")\n", "print(f\"Column-wise mean: ${mean_house_price_col:,.2f}\")\n", "print(f\"Are they equal? {abs(mean_house_price - mean_house_price_col) < 0.01}\")\n", "\n", "mean_house_price_col"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.1.8: Create Price per Square Meter Column\n", "\n", "When data is organized by columns/features, row-wise operations become more difficult. Create a \"price_per_m2\" column in houses_columnwise.\n", "\n", "**Learning Goals:**\n", "- Add a key-value pair to a dictionary in Python\n", "- Zip two lists together in Python\n", "- Write a for loop in Python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Method 1: Using zip and for loop\n", "print(\"Prices:\", houses_columnwise[\"price_approx_usd\"])\n", "print(\"Areas:\", houses_columnwise[\"surface_covered_in_m2\"])\n", "\n", "# Initialize the price_per_m2 column\n", "houses_columnwise[\"price_per_m2\"] = []\n", "\n", "# Calculate price per m² using zip\n", "for price, area in zip(houses_columnwise[\"price_approx_usd\"], houses_columnwise[\"surface_covered_in_m2\"]):\n", "    price_per_m2 = price / area\n", "    houses_columnwise[\"price_per_m2\"].append(price_per_m2)\n", "\n", "# Display results\n", "print(\"\\nUpdated houses_columnwise:\")\n", "for key, values in houses_columnwise.items():\n", "    if \"price\" in key:\n", "        formatted_values = [f\"${v:,.2f}\" for v in values]\n", "        print(f\"  {key}: {formatted_values}\")\n", "    else:\n", "        print(f\"  {key}: {values}\")\n", "\n", "print(\"\\nhouses_columnwise type:\", type(houses_columnwise))\n", "houses_columnwise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction to pandas DataFrames\n", "\n", "While you've shown that you can wrangle data using lists and dictionaries, it's not as intuitive as working with a spreadsheet. Fortunately, **pandas** is one of the best known data science libraries that allows you to organize data into **DataFrames** - much more powerful than spreadsheets!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import pandas library\n", "import pandas as pd\n", "\n", "# Create DataFrame from houses_columnwise\n", "df_houses = pd.DataFrame(houses_columnwise)\n", "\n", "# Print information\n", "print(\"df_houses type:\", type(df_houses))\n", "print(\"df_houses shape:\", df_houses.shape)\n", "print(\"\\nDataFrame info:\")\n", "print(df_houses.info())\n", "print(\"\\nDataFrame contents:\")\n", "df_houses"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Analysis with Our DataFrame\n", "\n", "Let's perform some basic analysis on our housing data using pandas functionality."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics\n", "print(\"=== BASIC STATISTICS ===\")\n", "print(f\"Number of houses: {len(df_houses)}\")\n", "print(f\"Average price: ${df_houses['price_approx_usd'].mean():,.2f}\")\n", "print(f\"Median price: ${df_houses['price_approx_usd'].median():,.2f}\")\n", "print(f\"Price range: ${df_houses['price_approx_usd'].min():,.2f} - ${df_houses['price_approx_usd'].max():,.2f}\")\n", "\n", "print(\"\\n=== AREA STATISTICS ===\")\n", "print(f\"Average area: {df_houses['surface_covered_in_m2'].mean():.1f} m²\")\n", "print(f\"Median area: {df_houses['surface_covered_in_m2'].median():.1f} m²\")\n", "print(f\"Area range: {df_houses['surface_covered_in_m2'].min():.0f} - {df_houses['surface_covered_in_m2'].max():.0f} m²\")\n", "\n", "print(\"\\n=== PRICE PER M² STATISTICS ===\")\n", "print(f\"Average price per m²: ${df_houses['price_per_m2'].mean():.2f}\")\n", "print(f\"Median price per m²: ${df_houses['price_per_m2'].median():.2f}\")\n", "print(f\"Price per m² range: ${df_houses['price_per_m2'].min():.2f} - ${df_houses['price_per_m2'].max():.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display descriptive statistics\n", "print(\"=== COMPREHENSIVE STATISTICS ===\")\n", "print(df_houses.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Find the most and least expensive houses\n", "print(\"=== HOUSE ANALYSIS ===\")\n", "most_expensive_idx = df_houses['price_approx_usd'].idxmax()\n", "least_expensive_idx = df_houses['price_approx_usd'].idxmin()\n", "\n", "print(\"Most expensive house:\")\n", "most_expensive = df_houses.loc[most_expensive_idx]\n", "print(f\"  Price: ${most_expensive['price_approx_usd']:,.2f}\")\n", "print(f\"  Area: {most_expensive['surface_covered_in_m2']} m²\")\n", "print(f\"  Rooms: {most_expensive['rooms']}\")\n", "print(f\"  Price per m²: ${most_expensive['price_per_m2']:.2f}\")\n", "\n", "print(\"\\nLeast expensive house:\")\n", "least_expensive = df_houses.loc[least_expensive_idx]\n", "print(f\"  Price: ${least_expensive['price_approx_usd']:,.2f}\")\n", "print(f\"  Area: {least_expensive['surface_covered_in_m2']} m²\")\n", "print(f\"  Rooms: {least_expensive['rooms']}\")\n", "print(f\"  Price per m²: ${least_expensive['price_per_m2']:.2f}\")\n", "\n", "# Best value (lowest price per m²)\n", "best_value_idx = df_houses['price_per_m2'].idxmin()\n", "print(\"\\nBest value house (lowest price per m²):\")\n", "best_value = df_houses.loc[best_value_idx]\n", "print(f\"  Price: ${best_value['price_approx_usd']:,.2f}\")\n", "print(f\"  Area: {best_value['surface_covered_in_m2']} m²\")\n", "print(f\"  Rooms: {best_value['rooms']}\")\n", "print(f\"  Price per m²: ${best_value['price_per_m2']:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "🎉 **Congratulations!** You've mastered the fundamentals of organizing tabular data in Python!\n", "\n", "### What You've Learned:\n", "\n", "1. **Tabular Data Concepts:**\n", "   - Understanding tidy data principles\n", "   - Organizing data into observations (rows) and features (columns)\n", "\n", "2. **Python Data Structures:**\n", "   - **Lists:** Simple, ordered collections of values\n", "   - **Nested Lists:** Lists containing other lists for tabular data\n", "   - **Dictionaries:** Key-value pairs for labeled data\n", "   - **List of Dictionaries (JSON):** Combining the best of both worlds\n", "\n", "3. **Data Organization Strategies:**\n", "   - **Row-wise:** Each dictionary represents one observation\n", "   - **Column-wise:** Each key represents one feature\n", "   - **Trade-offs:** Row-wise easier for individual records, column-wise easier for analysis\n", "\n", "4. **Programming Skills:**\n", "   - For loops for repetitive tasks\n", "   - List comprehensions for efficient data processing\n", "   - Basic mathematical operations\n", "   - Data type understanding\n", "\n", "5. **pandas Introduction:**\n", "   - DataFrames as powerful tabular data structures\n", "   - Basic data analysis and statistics\n", "   - Professional data manipulation tools\n", "\n", "### Next Steps:\n", "In the following projects, you'll learn to:\n", "- Import and clean real-world datasets\n", "- Perform exploratory data analysis\n", "- Create visualizations\n", "- Build predictive models\n", "\n", "**You're now ready to tackle real-world data science challenges!** 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}