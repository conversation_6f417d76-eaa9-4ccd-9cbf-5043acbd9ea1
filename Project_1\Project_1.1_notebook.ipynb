# Declare variable `house_0_list`
house_0_list = [115910.26, 128, 4]

# Print object type of `house_0_list`
print("house_0_list type:", type(house_0_list))

# Print length of `house_0_list`
print("house_0_list length:", len(house_0_list))

# Get output of `house_0_list`
house_0_list

# Calculate price per square meter
house_0_price_m2 = house_0_list[0] / house_0_list[1]

# Print object type
print("house_0_price_m2 type:", type(house_0_price_m2))

# Display result
print(f"Price per m²: ${house_0_price_m2:.2f}")
house_0_price_m2

# Append price per m² to house_0_list
house_0_list.append(house_0_price_m2)

# Print updated information
print("house_0_list type:", type(house_0_list))
print("house_0_list length:", len(house_0_list))
print("Updated house_0_list:", house_0_list)

# Create nested list with all houses
houses_nested_list = [
    [115910.26, 128.0, 4.0],
    [48718.17, 210.0, 3.0],
    [28977.56, 58.0, 2.0],
    [36932.27, 79.0, 3.0],
    [83903.51, 111.0, 3.0],
]

# Print information about the nested list
print("houses_nested_list type:", type(houses_nested_list))
print("houses_nested_list length:", len(houses_nested_list))
print("\nFirst house:", houses_nested_list[0])
print("All houses:")
for i, house in enumerate(houses_nested_list):
    print(f"  House {i+1}: {house}")

# Create for loop to iterate through houses_nested_list
for house in houses_nested_list:
    # Calculate price per m² and append to each house
    price_per_m2 = house[0] / house[1]
    house.append(price_per_m2)

# Display results
print("Updated houses with price per m²:")
for i, house in enumerate(houses_nested_list):
    print(f"  House {i+1}: ${house[0]:,.2f}, {house[1]} m², {house[2]} rooms, ${house[3]:.2f}/m²")

print("\nFull nested list:")
houses_nested_list

# Create dictionary for house_0
house_0_dict = {
    "price_approx_usd": 115910.26,
    "surface_covered_in_m2": 128,
    "rooms": 4,
}

# Print information
print("house_0_dict type:", type(house_0_dict))
print("\nHouse 0 information:")
for key, value in house_0_dict.items():
    print(f"  {key}: {value}")

house_0_dict

# Add "price_per_m2" key-value pair to house_0_dict
house_0_dict["price_per_m2"] = house_0_dict["price_approx_usd"] / house_0_dict["surface_covered_in_m2"]

# Display updated dictionary
print("Updated house_0_dict:")
for key, value in house_0_dict.items():
    if key == "price_per_m2":
        print(f"  {key}: ${value:.2f}")
    else:
        print(f"  {key}: {value}")

house_0_dict

# Create list of dictionaries (JSON format)
houses_rowwise = [
    {
        "price_approx_usd": 115910.26,
        "surface_covered_in_m2": 128,
        "rooms": 4,
    },
    {
        "price_approx_usd": 48718.17,
        "surface_covered_in_m2": 210,
        "rooms": 3,
    },
    {
        "price_approx_usd": 28977.56,
        "surface_covered_in_m2": 58,
        "rooms": 2,
    },
    {
        "price_approx_usd": 36932.27,
        "surface_covered_in_m2": 79,
        "rooms": 3,
    },
    {
        "price_approx_usd": 83903.51,
        "surface_covered_in_m2": 111,
        "rooms": 3,
    },
]

# Print information
print("houses_rowwise type:", type(houses_rowwise))
print("houses_rowwise length:", len(houses_rowwise))
print("\nFirst house dictionary:")
print(houses_rowwise[0])

# Create for loop to iterate through houses_rowwise
for house in houses_rowwise:
    # Calculate and add price_per_m2 for each house
    house["price_per_m2"] = house["price_approx_usd"] / house["surface_covered_in_m2"]

# Display results
print("houses_rowwise type:", type(houses_rowwise))
print("houses_rowwise length:", len(houses_rowwise))
print("\nUpdated houses with price per m²:")
for i, house in enumerate(houses_rowwise):
    print(f"\n  House {i+1}:")
    for key, value in house.items():
        if "price" in key and key != "rooms":
            print(f"    {key}: ${value:,.2f}")
        else:
            print(f"    {key}: {value}")

houses_rowwise

# Create empty list for house prices
house_prices = []

# Iterate through houses_rowwise and collect prices
for house in houses_rowwise:
    house_prices.append(house["price_approx_usd"])

# Calculate mean house price
mean_house_price = sum(house_prices) / len(house_prices)

# Display results
print("House prices:", house_prices)
print(f"\nMean house price: ${mean_house_price:,.2f}")
print("mean_house_price type:", type(mean_house_price))

mean_house_price

# Organize data by columns/features
houses_columnwise = {
    "price_approx_usd": [115910.26, 48718.17, 28977.56, 36932.27, 83903.51],
    "surface_covered_in_m2": [128.0, 210.0, 58.0, 79.0, 111.0],
    "rooms": [4.0, 3.0, 2.0, 3.0, 3.0],
}

# Print information
print("houses_columnwise type:", type(houses_columnwise))
print("\nColumn-wise organization:")
for key, values in houses_columnwise.items():
    print(f"  {key}: {values}")

houses_columnwise

# Calculate mean house price using column-wise data
mean_house_price_col = sum(houses_columnwise["price_approx_usd"]) / len(houses_columnwise["price_approx_usd"])

print(f"Mean house price (column-wise): ${mean_house_price_col:,.2f}")
print("mean_house_price type:", type(mean_house_price_col))

# Verify it's the same as row-wise calculation
print(f"\nVerification:")
print(f"Row-wise mean: ${mean_house_price:,.2f}")
print(f"Column-wise mean: ${mean_house_price_col:,.2f}")
print(f"Are they equal? {abs(mean_house_price - mean_house_price_col) < 0.01}")

mean_house_price_col

# Method 1: Using zip and for loop
print("Prices:", houses_columnwise["price_approx_usd"])
print("Areas:", houses_columnwise["surface_covered_in_m2"])

# Initialize the price_per_m2 column
houses_columnwise["price_per_m2"] = []

# Calculate price per m² using zip
for price, area in zip(houses_columnwise["price_approx_usd"], houses_columnwise["surface_covered_in_m2"]):
    price_per_m2 = price / area
    houses_columnwise["price_per_m2"].append(price_per_m2)

# Display results
print("\nUpdated houses_columnwise:")
for key, values in houses_columnwise.items():
    if "price" in key:
        formatted_values = [f"${v:,.2f}" for v in values]
        print(f"  {key}: {formatted_values}")
    else:
        print(f"  {key}: {values}")

print("\nhouses_columnwise type:", type(houses_columnwise))
houses_columnwise

# Import pandas library
import pandas as pd

# Create DataFrame from houses_columnwise
df_houses = pd.DataFrame(houses_columnwise)

# Print information
print("df_houses type:", type(df_houses))
print("df_houses shape:", df_houses.shape)
print("\nDataFrame info:")
print(df_houses.info())
print("\nDataFrame contents:")
df_houses

# Basic statistics
print("=== BASIC STATISTICS ===")
print(f"Number of houses: {len(df_houses)}")
print(f"Average price: ${df_houses['price_approx_usd'].mean():,.2f}")
print(f"Median price: ${df_houses['price_approx_usd'].median():,.2f}")
print(f"Price range: ${df_houses['price_approx_usd'].min():,.2f} - ${df_houses['price_approx_usd'].max():,.2f}")

print("\n=== AREA STATISTICS ===")
print(f"Average area: {df_houses['surface_covered_in_m2'].mean():.1f} m²")
print(f"Median area: {df_houses['surface_covered_in_m2'].median():.1f} m²")
print(f"Area range: {df_houses['surface_covered_in_m2'].min():.0f} - {df_houses['surface_covered_in_m2'].max():.0f} m²")

print("\n=== PRICE PER M² STATISTICS ===")
print(f"Average price per m²: ${df_houses['price_per_m2'].mean():.2f}")
print(f"Median price per m²: ${df_houses['price_per_m2'].median():.2f}")
print(f"Price per m² range: ${df_houses['price_per_m2'].min():.2f} - ${df_houses['price_per_m2'].max():.2f}")

# Display descriptive statistics
print("=== COMPREHENSIVE STATISTICS ===")
print(df_houses.describe())

# Find the most and least expensive houses
print("=== HOUSE ANALYSIS ===")
most_expensive_idx = df_houses['price_approx_usd'].idxmax()
least_expensive_idx = df_houses['price_approx_usd'].idxmin()

print("Most expensive house:")
most_expensive = df_houses.loc[most_expensive_idx]
print(f"  Price: ${most_expensive['price_approx_usd']:,.2f}")
print(f"  Area: {most_expensive['surface_covered_in_m2']} m²")
print(f"  Rooms: {most_expensive['rooms']}")
print(f"  Price per m²: ${most_expensive['price_per_m2']:.2f}")

print("\nLeast expensive house:")
least_expensive = df_houses.loc[least_expensive_idx]
print(f"  Price: ${least_expensive['price_approx_usd']:,.2f}")
print(f"  Area: {least_expensive['surface_covered_in_m2']} m²")
print(f"  Rooms: {least_expensive['rooms']}")
print(f"  Price per m²: ${least_expensive['price_per_m2']:.2f}")

# Best value (lowest price per m²)
best_value_idx = df_houses['price_per_m2'].idxmin()
print("\nBest value house (lowest price per m²):")
best_value = df_houses.loc[best_value_idx]
print(f"  Price: ${best_value['price_approx_usd']:,.2f}")
print(f"  Area: {best_value['surface_covered_in_m2']} m²")
print(f"  Rooms: {best_value['rooms']}")
print(f"  Price per m²: ${best_value['price_per_m2']:.2f}")