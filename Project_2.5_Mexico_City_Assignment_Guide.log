This is pdfTeX, Version 3.141592653-2.6-1.40.25 (MiKTeX 24.1) (preloaded format=pdflatex 2025.5.30)  4 JUN 2025 21:05
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./Project_2.5_Mexico_City_Assignment_Guide.tex
(Project_2.5_Mexico_City_Assignment_Guide.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count187
\c@section=\count188
\c@subsection=\count189
\c@subsubsection=\count190
\c@paragraph=\count191
\c@subparagraph=\count192
\c@figure=\count193
\c@table=\count194
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count195
\Gm@cntv=\count196
\c@Gm@tempcnt=\count197
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks20

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/geometry\geometry.cfg))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen149
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count198
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count199
\leftroot@=\count266
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count267
\DOTSCASE@=\count268
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count269
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count270
\dotsspace@=\muskip16
\c@parentequation=\count271
\dspbrk@lvl=\count272
\tag@help=\toks22
\row@=\count273
\column@=\count274
\maxfields@=\count275
\andhelp@=\toks23
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks24
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\graphics.c
fg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen158
\Gin@req@width=\dimen159
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.sty
\lst@mode=\count276
\lst@gtempboxa=\box53
\lst@token=\toks26
\lst@length=\count277
\lst@currlwidth=\dimen160
\lst@column=\count278
\lst@pos=\count279
\lst@lostspace=\dimen161
\lst@width=\dimen162
\lst@newlines=\count280
\lst@lineno=\count281
\lst@maxwidth=\dimen163

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstmisc.sty
File: lstmisc.sty 2023/02/27 1.9 (Carsten Heinz)
\c@lstnumber=\count282
\lst@skipnumbers=\count283
\lst@framebox=\box54
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\listings.cfg
File: listings.cfg 2023/02/27 1.9 listings configuration
))
Package: listings 2023/02/27 1.9 (Carsten Heinz)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/infwarerr\infwarerr.s
ty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/kvdefinekeys\kvdefine
keys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdfescape\pdfescape.s
ty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pdftexcmds\pdftexcmds
.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/letltxmacro\letltxmacro
.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/gettitlestring\gettit
lestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count285
)
\@linkdim=\dimen164
\Hy@linkcounter=\count286
\Hy@pagecounter=\count287

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count288

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4448.
\c@Hy@tempcnt=\count289

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/url\url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen165

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/bigintcalc\bigintcalc
.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count290
\Field@Width=\dimen166
\Fld@charsize=\dimen167
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring OFF on input line 6085.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count291
\c@Item=\count292
\c@Hfootnote=\count293
)
Package hyperref Info: Driver (autodetected): hpdftex.
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2023-11-26 v7.01g Hyperref driver for pdfTeX

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count294
\c@bookmark@seq@number=\count295

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/rerunfilecheck\rerunfil
echeck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/uniquecounter\uniquec
ounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip53
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/fancyhdr\fancyhdr.sty
Package: fancyhdr 2025/02/07 v5.2 Extensive control of page headers and footers

\f@nch@headwidth=\skip54
\f@nch@offset@elh=\skip55
\f@nch@offset@erh=\skip56
\f@nch@offset@olh=\skip57
\f@nch@offset@orh=\skip58
\f@nch@offset@elf=\skip59
\f@nch@offset@erf=\skip60
\f@nch@offset@olf=\skip61
\f@nch@offset@orf=\skip62
\f@nch@height=\skip63
\f@nch@footalignment=\skip64
\f@nch@widthL=\skip65
\f@nch@widthC=\skip66
\f@nch@widthR=\skip67
\@temptokenb=\toks27
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/titlesec\titlesec.sty
Package: titlesec 2025/01/04 v2.17 Sectioning titles
\ttl@box=\box55
\beforetitleunit=\skip68
\aftertitleunit=\skip69
\ttl@plus=\dimen168
\ttl@minus=\dimen169
\ttl@toksa=\toks28
\titlewidth=\dimen170
\titlewidthlast=\dimen171
\titlewidthfirst=\dimen172
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/enumitem\enumitem.sty
Package: enumitem 2025/02/06 v3.11 Customized lists
\labelindent=\skip70
\enit@outerparindent=\dimen173
\enit@toks=\toks29
\enit@inbox=\box56
\enit@count@id=\count296
\enitdp@description=\count297
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen174
\lightrulewidth=\dimen175
\cmidrulewidth=\dimen176
\belowrulesep=\dimen177
\belowbottomsep=\dimen178
\aboverulesep=\dimen179
\abovetopsep=\dimen180
\cmidrulesep=\dimen181
\cmidrulekern=\dimen182
\defaultaddspace=\dimen183
\@cmidla=\count298
\@cmidlb=\count299
\@aboverulesep=\dimen184
\@belowrulesep=\dimen185
\@thisruleclass=\count300
\@lastruleclass=\count301
\@thisrulewidth=\dimen186
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/float\float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count302
\float@exts=\toks30
\float@box=\box57
\@float@everytoks=\toks31
\@floatcapt=\box58
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen187
\ar@mcellbox=\box59
\extrarowheight=\dimen188
\NC@list=\toks32
\extratabsurround=\skip71
\backup@length=\skip72
\ar@cellbox=\box60
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\longtable.sty
Package: longtable 2023-11-01 v4.19 Multi-page Table package (DPC)
\LTleft=\skip73
\LTright=\skip74
\LTpre=\skip75
\LTpost=\skip76
\LTchunksize=\count303
\LTcapwidth=\dimen189
\LT@head=\box61
\LT@firsthead=\box62
\LT@foot=\box63
\LT@lastfoot=\box64
\LT@gbox=\box65
\LT@cols=\count304
\LT@rows=\count305
\c@LT@tables=\count306
\c@LT@chunks=\count307
\LT@p@ftn=\toks33
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tcolorbox\tcolorbox.sty
Package: tcolorbox 2024/01/10 version 6.2.0 text color boxes
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgf.sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/utilities\pgfrcs.st
y
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-common.tex
\pgfutil@everybye=\toks34
\pgfutil@tempdima=\dimen190
\pgfutil@tempdimb=\dimen191
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfutil
-latex.def
\pgfutil@abb=\box66
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfrcs.
code.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/basiclayer\pgfcore.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/systemlayer\pgfsys.
sty
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
.code.tex
\pgfkeys@pathtoks=\toks35
\pgfkeys@temptoks=\toks36

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/utilities\pgfkeys
libraryfiltered.code.tex
\pgfkeys@tmptoks=\toks37
))
\pgf@x=\dimen192
\pgf@y=\dimen193
\pgf@xa=\dimen194
\pgf@ya=\dimen195
\pgf@xb=\dimen196
\pgf@yb=\dimen197
\pgf@xc=\dimen198
\pgf@yc=\dimen199
\pgf@xd=\dimen256
\pgf@yd=\dimen257
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count308
\c@pgf@countb=\count309
\c@pgf@countc=\count310
\c@pgf@countd=\count311
\t@pgf@toka=\toks38
\t@pgf@tokb=\toks39
\t@pgf@tokc=\toks40
\pgf@sys@id@count=\count312

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgf.c
fg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
s-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
ssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count313
\pgfsyssoftpath@bigbuffer@items=\count314
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/systemlayer\pgfsy
sprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
e.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmath.code
.tex
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathutil.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathparse
r.code.tex
\pgfmath@dimen=\dimen258
\pgfmath@count=\count315
\pgfmath@box=\box67
\pgfmath@toks=\toks41
\pgfmath@stack@operand=\toks42
\pgfmath@stack@operation=\toks43
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.basic.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.trigonometric.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.random.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.comparison.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.base.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.round.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.misc.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfunct
ions.integerarithmetics.code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathcalc.
code.tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfmathfloat
.code.tex
\c@pgfmathroundto@lastzeros=\count316
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/math\pgfint.code.
tex)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen259
\pgf@picmaxx=\dimen260
\pgf@picminy=\dimen261
\pgf@picmaxy=\dimen262
\pgf@pathminx=\dimen263
\pgf@pathmaxx=\dimen264
\pgf@pathminy=\dimen265
\pgf@pathmaxy=\dimen266
\pgf@xx=\dimen267
\pgf@xy=\dimen268
\pgf@yx=\dimen269
\pgf@yy=\dimen270
\pgf@zx=\dimen271
\pgf@zy=\dimen272
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen273
\pgf@path@lasty=\dimen274
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen275
\pgf@shorten@start@additional=\dimen276
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
escopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box68
\pgf@hbox=\box69
\pgf@layerbox@main=\box70
\pgf@picture@serial@count=\count317
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
egraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen277
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen278
\pgf@pt@y=\dimen279
\pgf@pt@temp=\dimen280
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
equick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
earrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen281
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen282
\pgf@sys@shading@range@num=\count318
\pgf@shadingcount=\count319
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
eexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box71
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
elayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
etransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
epatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/basiclayer\pgfcor
erdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
shapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box72
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/generic/pgf/modules\pgfmodule
plot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen283
\pgf@nodesepend=\dimen284
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/pgf/compatibility\pgfco
mp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/tools\verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks44
\verbatim@line=\toks45
\verbatim@in@stream=\read3
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/environ\environ.sty
Package: environ 2014/05/04 v0.3 A new way to define environments

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/trimspaces\trimspaces.s
ty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
))
\tcb@titlebox=\box73
\tcb@upperbox=\box74
\tcb@lowerbox=\box75
\tcb@phantombox=\box76
\c@tcbbreakpart=\count320
\c@tcblayer=\count321
\c@tcolorbox@number=\count322
\l__tcobox_tmpa_box=\box77
\l__tcobox_tmpa_dim=\dimen285
\tcb@temp=\box78
\tcb@temp=\box79
\tcb@temp=\box80
\tcb@temp=\box81
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/listings\lstlang1.sty
File: lstlang1.sty 2023/02/27 1.9 listings language file
)
Package hyperref Info: Option `colorlinks' set `true' on input line 81.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/l3backend\l3backend-pdf
tex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count323
\l__pdf_internal_box=\box82
) (Project_2.5_Mexico_City_Assignment_Guide.aux)
\openout1 = `Project_2.5_Mexico_City_Assignment_Guide.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 83.
LaTeX Font Info:    ... okay on input line 83.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 452.9679pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 700.50687pt, 72.26999pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=452.9679pt
* \textheight=700.50687pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=15.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/context/base/mkii\supp-pdf.mk
ii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count324
\scratchdimen=\dimen286
\scratchbox=\box83
\nofMPsegments=\count325
\nofMParguments=\count326
\everyMPshowfont=\toks46
\MPscratchCnt=\count327
\MPscratchDim=\dimen287
\MPnumerator=\count328
\makeMPintoPDFobject=\count329
\everyMPtoPDFconversion=\toks47
)
(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/epstopdf-pkg\epstopdf-b
ase.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/00miktex\epstopdf-sys.c
fg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
\c@lstlisting=\count330
Package hyperref Info: Link coloring ON on input line 83.
 (Project_2.5_Mexico_City_Assignment_Guide.out)
(Project_2.5_Mexico_City_Assignment_Guide.out)
\@outlinefile=\write4
\openout4 = `Project_2.5_Mexico_City_Assignment_Guide.out'.

LaTeX Font Info:    Trying to load font information for U+msa on input line 86.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 86.


(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
(Project_2.5_Mexico_City_Assignment_Guide.toc)
\tf@toc=\write5
\openout5 = `Project_2.5_Mexico_City_Assignment_Guide.toc'.

 [1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}]
Package hyperref Info: bookmark level for unknown lstlisting defaults to 0 on i
nput line 119.
LaTeX Font Info:    Font shape `OT1/cmtt/bx/n' in size <10> not available
(Font)              Font shape `OT1/cmtt/m/n' tried instead on input line 121.
 [2]
LaTeX Font Info:    Trying to load font information for TS1+cmtt on input line 
141.

(C:\Users\<USER>\AppData\Local\Programs\MiKTeX\tex/latex/base\ts1cmtt.fd
File: ts1cmtt.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
[3] [4] [5] [6] [7] [8] [9] [10] [11] [12] [13]
(Project_2.5_Mexico_City_Assignment_Guide.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********
Package rerunfilecheck Info: File `Project_2.5_Mexico_City_Assignment_Guide.out
' has not changed.
(rerunfilecheck)             Checksum: EFE03D7F4C17D7998B3AECCBDD43AEF1;3859.
 ) 
Here is how much of TeX's memory you used:
 23799 strings out of 474486
 431055 string characters out of 5743755
 2117542 words of memory out of 5000000
 45274 multiletter control sequences out of 15000+600000
 563763 words of font info for 58 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 83i,12n,87p,455b,2190s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tctt
1000.pk> <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi60
0\tcrm1200.pk><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type1/public/
amsfonts/cm/cmbx12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fonts/type
1/public/amsfonts/cm/cmr12.pfb><C:/Users/<USER>/AppData/Local/Programs/MiKTeX/fo
nts/type1/public/amsfonts/cm/cmtt10.pfb>
Output written on Project_2.5_Mexico_City_Assignment_Guide.pdf (13 pages, 19030
7 bytes).
PDF statistics:
 687 PDF objects out of 1000 (max. 8388607)
 464 named destinations out of 1000 (max. 500000)
 149 words of extra memory for PDF output out of 10000 (max. 10000000)

