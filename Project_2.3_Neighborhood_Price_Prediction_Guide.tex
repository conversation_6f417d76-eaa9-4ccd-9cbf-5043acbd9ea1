\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 2.3: Neighborhood Price Prediction}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 2.3: Predicting Price with Neighborhood} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Working with Categorical Features}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: From Numbers to Names}

Welcome to Project 2.3! In Project 2.2, we used latitude and longitude (numbers) to represent location. Now we'll use neighborhood names (text) instead. This is more intuitive for people, but creates new challenges for our machine learning models.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item How to work with multiple data files using simple patterns
    \item Converting neighborhood names into numbers (one-hot encoding)
    \item Why too many features can hurt model performance (overfitting)
    \item Using Ridge regression to make models more robust
    \item Reading model results to get business insights
\end{itemize}

\begin{concept}
\textbf{Categorical vs Numerical Features:}
\begin{itemize}
    \item \textbf{Numerical:} Latitude = -34.6118, Longitude = -58.3960
    \item \textbf{Categorical:} Neighborhood = "Palermo"
\end{itemize}
Both represent location, but categorical features are easier for humans to understand and often capture socioeconomic factors better than raw coordinates.
\end{concept}

\section{Task 2.3.1: Finding Multiple Files}

Real-world data often comes in multiple files. Let's learn to find them automatically.

\begin{lstlisting}[caption=Finding Files with Patterns]
# Import what we need
import warnings
from glob import glob
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from category_encoders import OneHotEncoder
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_absolute_error
from sklearn.pipeline import make_pipeline

warnings.simplefilter(action="ignore", category=FutureWarning)

# Find all Buenos Aires real estate files
files = glob("data/*buenos-aires-real*.csv")

print(f"Found {len(files)} files:")
for file in files:
    print(f"  {file}")
\end{lstlisting}

\begin{learningtip}
\textbf{What is glob?} 
Think of glob as a "smart search" for files. The pattern \texttt{"*buenos-aires-real*.csv"} means:
\begin{itemize}
    \item \texttt{*} = any characters before "buenos-aires-real"
    \item \texttt{*} = any characters after "buenos-aires-real"  
    \item \texttt{.csv} = must end with .csv
\end{itemize}
This finds all CSV files with "buenos-aires-real" in the name, no matter what comes before or after.
\end{learningtip}

\section{Task 2.3.2: Processing Files One by One}

Now let's clean each file using our familiar wrangling function, but with a new twist - extracting neighborhood names.

\begin{lstlisting}[caption=Simple File Processing with Neighborhoods]
def wrangle(filepath):
    """Clean one CSV file and extract neighborhood information."""
    
    # Read the data
    df = pd.read_csv(filepath)
    
    # Apply our usual filters
    mask_ba = df["place_with_parent_names"].str.contains("Capital Federal", na=False)
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 400_000
    df = df[mask_ba & mask_apt & mask_price]
    
    # Remove outliers in surface area
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df = df[mask_area]
    
    # Parse latitude and longitude
    df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
    df.drop(columns="lat-lon", inplace=True)
    
    # NEW: Extract neighborhood name
    # The location string looks like: "|Argentina|Capital Federal|Palermo|"
    # We want just the neighborhood name: "Palermo"
    df["neighborhood"] = (df["place_with_parent_names"]
                         .str.strip("|")           # Remove | from ends
                         .str.split("|")           # Split into parts
                         .str[-1])                 # Take the last part
    
    # Clean up
    df = df.drop(columns="place_with_parent_names")
    
    return df

# Process each file
frames = []
for file in files:
    print(f"Processing {file}...")
    df = wrangle(file)
    frames.append(df)
    print(f"  Found {len(df)} apartments")

print(f"\nSuccessfully processed {len(frames)} files!")
\end{lstlisting}

\begin{learningtip}
\textbf{String Processing Step by Step:}
\begin{enumerate}
    \item Original: \texttt{"|Argentina|Capital Federal|Palermo|"}
    \item After \texttt{.str.strip("|")}: \texttt{"Argentina|Capital Federal|Palermo"}
    \item After \texttt{.str.split("|")}: \texttt{["Argentina", "Capital Federal", "Palermo"]}
    \item After \texttt{.str[-1]}: \texttt{"Palermo"}
\end{enumerate}
This is a common pattern for extracting information from hierarchical location data.
\end{learningtip}

\section{Task 2.3.3: Combining All Data}

Let's stack all our DataFrames into one big dataset.

\begin{lstlisting}[caption=Combining Multiple DataFrames]
# Combine all DataFrames into one
df = pd.concat(frames, ignore_index=True)

print(f"Combined Dataset Summary:")
print(f"  Total apartments: {len(df):,}")
print(f"  Number of neighborhoods: {df['neighborhood'].nunique()}")
print(f"  Price range: ${df['price_aprox_usd'].min():,.0f} - ${df['price_aprox_usd'].max():,.0f}")

# Look at the first few rows
print(f"\nFirst 5 rows:")
print(df[['neighborhood', 'price_aprox_usd', 'surface_covered_in_m2']].head())
\end{lstlisting}

\begin{concept}
\textbf{pd.concat() Explained:}
This function stacks DataFrames like pancakes. With \texttt{ignore\_index=True}, it creates fresh row numbers (0, 1, 2, ...) instead of keeping the original numbers from each file. This prevents duplicate index numbers.
\end{concept}

\section{Task 2.3.4: Understanding Our Neighborhoods}

Before building models, let's explore what neighborhoods we have and their characteristics.

\begin{lstlisting}[caption=Simple Neighborhood Analysis]
# Count apartments by neighborhood
neighborhood_counts = df['neighborhood'].value_counts()

print("Most common neighborhoods:")
print(neighborhood_counts.head(10))

print(f"\nDataset statistics:")
print(f"  Total neighborhoods: {len(neighborhood_counts)}")
print(f"  Largest: {neighborhood_counts.index[0]} ({neighborhood_counts.iloc[0]} apartments)")
print(f"  Smallest: {neighborhood_counts.iloc[-1]} apartments")

# Calculate average prices by neighborhood
avg_prices = df.groupby('neighborhood')['price_aprox_usd'].mean().sort_values(ascending=False)

print(f"\nMost expensive neighborhoods (average price):")
for i in range(5):
    neighborhood = avg_prices.index[i]
    price = avg_prices.iloc[i]
    print(f"  {neighborhood}: ${price:,.0f}")

print(f"\nMost affordable neighborhoods (average price):")
for i in range(-5, 0):
    neighborhood = avg_prices.index[i]
    price = avg_prices.iloc[i]
    print(f"  {neighborhood}: ${price:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Exploratory Data Analysis (EDA):} 
Before building any model, always explore your data first! This helps you understand:
\begin{itemize}
    \item How many categories you have (57 neighborhoods)
    \item Whether categories are balanced (some neighborhoods have many listings, others just a few)
    \item The range of your target variable (prices from \$50K to \$400K)
\end{itemize}
This information guides your modeling decisions.
\end{learningtip}

\section{Task 2.3.5: Preparing Features and Target}

Let's set up our machine learning problem.

\begin{lstlisting}[caption=Setting Up X and y]
# Define our feature and target
target = "price_aprox_usd"
features = ["neighborhood"]

# Create X (input) and y (output)
X_train = df[features].copy()
y_train = df[target].copy()

print(f"Feature matrix (X): {X_train.shape}")
print(f"Target vector (y): {y_train.shape}")
print(f"Feature type: {X_train.dtypes[0]}")

print(f"\nSample data:")
print(f"X (neighborhoods): {list(X_train['neighborhood'].head())}")
print(f"y (prices): {list(y_train.head())}")
\end{lstlisting}

\begin{concept}
\textbf{The Machine Learning Setup:}
\begin{itemize}
    \item \textbf{X} = Features (what we know): Neighborhood names
    \item \textbf{y} = Target (what we want to predict): Apartment prices
    \item \textbf{Goal:} Learn a function that maps X to y: \texttt{price = f(neighborhood)}
\end{itemize}
\end{concept}

\section{Task 2.3.7: The Categorical Challenge}

Here's the problem: machine learning algorithms need numbers, but we have text!

\begin{lstlisting}[caption=Why We Can't Use Text Directly]
# Try to see the problem
print("Our neighborhood data looks like this:")
print(X_train['neighborhood'].head())
print(f"Data type: {X_train['neighborhood'].dtype}")

print(f"\nProblem: How do we do math with words?")
print(f"We can't calculate: 'Palermo' + 'Recoleta' = ?")
print(f"We need to convert text to numbers first!")

# Bad solution: just number them
bad_encoding = pd.Categorical(X_train['neighborhood']).codes
print(f"\nBad solution - just assign numbers:")
print(f"Palermo = {bad_encoding[0]}, Recoleta = {bad_encoding[1]}")
print(f"Problem: This implies Recoleta > Palermo, which doesn't make sense!")
\end{lstlisting}

\begin{learningtip}
\textbf{Why Simple Numbering Doesn't Work:}
If we assign Palermo=1, Recoleta=2, Villa Crespo=3, we're telling the model that:
\begin{itemize}
    \item Recoleta is "twice as much" as Palermo
    \item Villa Crespo is "three times" as much as Palermo
    \item There's an order: Palermo < Recoleta < Villa Crespo
\end{itemize}
But neighborhoods don't have a natural order! They're just different categories.
\end{learningtip}

\section{One-Hot Encoding: The Right Solution}

One-hot encoding creates a separate yes/no column for each neighborhood.

\begin{lstlisting}[caption=Understanding One-Hot Encoding]
# Let's see how one-hot encoding works with a simple example
simple_data = pd.DataFrame({
    'neighborhood': ['Palermo', 'Recoleta', 'Palermo', 'Villa Crespo']
})

print("Before encoding:")
print(simple_data)

# Apply one-hot encoding
ohe = OneHotEncoder(use_cat_names=True)
ohe.fit(simple_data)
encoded = ohe.transform(simple_data)

print(f"\nAfter one-hot encoding:")
print(encoded)
print(f"\nColumns: {list(ohe.get_feature_names_out())}")
\end{lstlisting}

\begin{concept}
\textbf{How One-Hot Encoding Works:}
\begin{itemize}
    \item Create one binary column per neighborhood
    \item For each apartment, put 1 in its neighborhood column, 0 everywhere else
    \item Example: "Palermo" becomes [1, 0, 0] if we have 3 neighborhoods
    \item This preserves the categorical nature without implying order
\end{itemize}
It's called "one-hot" because exactly one column is "hot" (=1) per row.
\end{concept}

\section{Task 2.3.8: Building Our First Model}

Let's create a complete pipeline that handles encoding and modeling automatically.

\begin{lstlisting}[caption=Creating a Simple Pipeline]
# Create a pipeline: encoding + linear regression
model = make_pipeline(
    OneHotEncoder(use_cat_names=True),  # Step 1: Convert text to numbers
    LinearRegression()                   # Step 2: Learn to predict prices
)

print("Created pipeline with 2 steps:")
print("  1. OneHotEncoder: neighborhood names -> binary features")
print("  2. LinearRegression: binary features -> price predictions")

# Train the model
print(f"\nTraining model on {len(X_train)} apartments...")
model.fit(X_train, y_train)
print("Training complete!")

# Check what happened inside
encoder = model.named_steps['onehotencoder']
n_features = len(encoder.get_feature_names_out())
print(f"\nEncoding results:")
print(f"  Original features: 1 (neighborhood)")
print(f"  After encoding: {n_features} binary features")
print(f"  Feature expansion: 1 -> {n_features}")
\end{lstlisting}

\begin{learningtip}
\textbf{Pipelines Make Life Easier:}
A pipeline automatically applies each step in order:
\begin{enumerate}
    \item When training: \texttt{fit()} runs OneHotEncoder.fit() then LinearRegression.fit()
    \item When predicting: \texttt{predict()} runs OneHotEncoder.transform() then LinearRegression.predict()
\end{enumerate}
This prevents mistakes and keeps your code clean!
\end{learningtip}

\section{Task 2.3.9: Evaluating Our Model}

Let's see how well our model performs.

\begin{lstlisting}[caption=Simple Model Evaluation]
# Make predictions on training data
y_pred = model.predict(X_train)

# Calculate Mean Absolute Error
mae = mean_absolute_error(y_train, y_pred)

# Compare to a simple baseline (always predict the average)
average_price = y_train.mean()
baseline_predictions = [average_price] * len(y_train)
mae_baseline = mean_absolute_error(y_train, baseline_predictions)

print(f"Model Performance:")
print(f"  Our model MAE: ${mae:,.0f}")
print(f"  Baseline MAE (always predict average): ${mae_baseline:,.0f}")
print(f"  Improvement: ${mae_baseline - mae:,.0f}")
print(f"  Percentage better: {((mae_baseline - mae) / mae_baseline) * 100:.1f}%")

# Show some example predictions
print(f"\nExample predictions:")
for i in range(5):
    actual = y_train.iloc[i]
    predicted = y_pred[i]
    neighborhood = X_train.iloc[i]['neighborhood']
    error = abs(actual - predicted)
    print(f"  {neighborhood}: Actual=${actual:,.0f}, Predicted=${predicted:,.0f}, Error=${error:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Understanding Mean Absolute Error (MAE):}
MAE tells you the average prediction error in dollars. If MAE = \$40,000:
\begin{itemize}
    \item On average, predictions are off by \$40,000
    \item Some predictions might be perfect, others off by \$80,000
    \item Lower MAE = better model
\end{itemize}
Always compare to a simple baseline to see if your model is actually learning something useful!
\end{learningtip}

\section{Task 2.3.11: Looking Inside the Model}

Let's extract the coefficients to understand what the model learned.

\begin{lstlisting}[caption=Extracting Model Coefficients]
# Get the trained linear regression model
regressor = model.named_steps['linearregression']

# Extract coefficients and intercept
coefficients = regressor.coef_
intercept = regressor.intercept_
feature_names = encoder.get_feature_names_out()

print(f"Model internals:")
print(f"  Intercept (base price): ${intercept:,.0f}")
print(f"  Number of coefficients: {len(coefficients)}")

# Create a series for easy analysis
coef_series = pd.Series(coefficients, index=feature_names)

print(f"\nTop 5 most expensive neighborhoods:")
top_5 = coef_series.nlargest(5)
for feature, coef in top_5.items():
    neighborhood = feature.replace('neighborhood_', '')
    print(f"  {neighborhood}: +${coef:,.0f}")

print(f"\nTop 5 most affordable neighborhoods:")
bottom_5 = coef_series.nsmallest(5)
for feature, coef in bottom_5.items():
    neighborhood = feature.replace('neighborhood_', '')
    print(f"  {neighborhood}: ${coef:,.0f}")
\end{lstlisting}

\begin{concept}
\textbf{Reading Coefficients:}
\begin{itemize}
    \item \textbf{Intercept:} The baseline price (when all neighborhood features = 0)
    \item \textbf{Positive coefficient:} This neighborhood adds to the base price
    \item \textbf{Negative coefficient:} This neighborhood reduces the base price
    \item \textbf{Example:} If Palermo has coefficient +\$50,000, apartments in Palermo cost \$50,000 more than the baseline
\end{itemize}
\end{concept}

\section{Task 2.3.12: Visualizing Results}

Let's create a simple chart to visualize neighborhood effects.

\begin{lstlisting}[caption=Simple Neighborhood Effects Chart]
# Get the 10 most extreme coefficients (5 highest, 5 lowest)
top_positive = coef_series.nlargest(5)
top_negative = coef_series.nsmallest(5)
extreme_coefs = pd.concat([top_positive, top_negative])

# Clean up names
clean_names = [name.replace('neighborhood_', '') for name in extreme_coefs.index]

# Create bar chart
plt.figure(figsize=(10, 6))
colors = ['green' if x > 0 else 'red' for x in extreme_coefs.values]
bars = plt.barh(range(len(extreme_coefs)), extreme_coefs.values, color=colors, alpha=0.7)

plt.yticks(range(len(extreme_coefs)), clean_names)
plt.xlabel('Price Effect (USD)')
plt.title('Neighborhood Effects on Apartment Prices')
plt.axvline(x=0, color='black', linestyle='-', alpha=0.8)
plt.grid(axis='x', alpha=0.3)

# Add value labels
for i, (bar, value) in enumerate(zip(bars, extreme_coefs.values)):
    plt.text(value + (2000 if value > 0 else -2000), i, f'${value:,.0f}', 
             va='center', ha='left' if value > 0 else 'right', fontweight='bold')

plt.tight_layout()
plt.show()

print("Chart interpretation:")
print("  Green bars = Premium neighborhoods (above average price)")
print("  Red bars = Affordable neighborhoods (below average price)")
print("  Bar length = Size of price difference")
\end{lstlisting}

\section{Task 2.3.14: The Overfitting Problem}

Our model has 57 features (one per neighborhood). Is this too many?

\begin{lstlisting}[caption=Checking for Overfitting Risk]
n_samples = len(X_train)
n_features = len(coefficients)
samples_per_feature = n_samples / n_features

print(f"Overfitting Analysis:")
print(f"  Training samples: {n_samples:,}")
print(f"  Model features: {n_features}")
print(f"  Samples per feature: {samples_per_feature:.1f}")

if samples_per_feature < 10:
    risk_level = "HIGH"
    explanation = "Very few samples per feature - model might memorize rather than learn"
elif samples_per_feature < 20:
    risk_level = "MODERATE"
    explanation = "Somewhat few samples per feature - some overfitting possible"
else:
    risk_level = "LOW"
    explanation = "Good ratio of samples to features"

print(f"  Overfitting risk: {risk_level}")
print(f"  Explanation: {explanation}")
\end{lstlisting}

\begin{concept}
\textbf{What is Overfitting?}
Imagine studying for an exam by memorizing specific questions instead of understanding concepts:
\begin{itemize}
    \item \textbf{Memorizing:} You ace the practice test but fail on new questions
    \item \textbf{Understanding:} You do well on both practice and new questions
\end{itemize}
In ML, overfitting happens when a model has too many features relative to training data. It "memorizes" the training examples instead of learning general patterns.
\end{concept}

\section{Task 2.3.15: Ridge Regression to the Rescue}

Ridge regression adds a penalty to prevent overfitting.

\begin{lstlisting}[caption=Simple Ridge Regression]
# Create a Ridge regression model (adds penalty for complex models)
model_ridge = make_pipeline(
    OneHotEncoder(use_cat_names=True),
    Ridge(alpha=1.0)  # alpha controls penalty strength
)

print("Training Ridge regression model...")
model_ridge.fit(X_train, y_train)

# Evaluate Ridge model
y_pred_ridge = model_ridge.predict(X_train)
mae_ridge = mean_absolute_error(y_train, y_pred_ridge)

print(f"\nModel comparison:")
print(f"  Linear Regression MAE: ${mae:,.0f}")
print(f"  Ridge Regression MAE: ${mae_ridge:,.0f}")

if mae_ridge > mae:
    print(f"  Ridge has higher training error (this is expected!)")
    print(f"  Ridge trades training accuracy for better generalization")
    print(f"  Difference: +${mae_ridge - mae:,.0f}")
else:
    print(f"  Ridge performs better even on training data")
\end{lstlisting}

\begin{learningtip}
\textbf{How Ridge Works:}
Ridge adds a penalty term that punishes large coefficients:
\begin{itemize}
    \item \textbf{alpha = 0:} No penalty (same as Linear Regression)
    \item \textbf{Small alpha:} Light penalty
    \item \textbf{Large alpha:} Strong penalty (simpler model)
\end{itemize}
This forces the model to use smaller, more stable coefficients, reducing overfitting.
\end{learningtip}

\section{Task 2.3.16: Comparing Ridge vs Linear Coefficients}

Let's see how Ridge changes our neighborhood coefficients.

\begin{lstlisting}[caption=Ridge vs Linear Coefficient Comparison]
# Extract Ridge coefficients
ridge_regressor = model_ridge.named_steps['ridge']
ridge_coefs = pd.Series(ridge_regressor.coef_, index=feature_names)

# Compare top 5 neighborhoods
print("Coefficient Comparison (Top 5 Most Expensive Neighborhoods):")
print(f"{'Neighborhood':<15} {'Linear':<12} {'Ridge':<12} {'Change'}")
print("-" * 55)

top_neighborhoods = coef_series.nlargest(5)
for feature, linear_coef in top_neighborhoods.items():
    ridge_coef = ridge_coefs[feature]
    change = ridge_coef - linear_coef
    neighborhood = feature.replace('neighborhood_', '')[:14]
    print(f"{neighborhood:<15} ${linear_coef:<11,.0f} ${ridge_coef:<11,.0f} ${change:+,.0f}")

print(f"\nObservation: Ridge coefficients are 'shrunk' toward zero")
print(f"This makes the model less extreme and more robust")

# Calculate overall shrinkage
avg_shrinkage = (coef_series.abs() - ridge_coefs.abs()).mean()
print(f"Average coefficient shrinkage: ${avg_shrinkage:,.0f}")
\end{lstlisting}

\section{Summary: What We Learned}

\subsection{Key Technical Skills}
\begin{enumerate}
    \item \textbf{File Processing:} Used glob patterns to work with multiple CSV files
    \item \textbf{String Processing:} Extracted neighborhood names from hierarchical location data
    \item \textbf{One-Hot Encoding:} Converted categorical features to numerical format
    \item \textbf{Pipeline Creation:} Combined preprocessing and modeling in one workflow
    \item \textbf{Overfitting Detection:} Recognized when models have too many features
    \item \textbf{Regularization:} Applied Ridge regression to improve model robustness
\end{enumerate}

\subsection{Business Insights}
\begin{itemize}
    \item \textbf{Location Matters:} Neighborhood can change apartment price by \$50,000+
    \item \textbf{Market Segmentation:} Clear distinction between premium and affordable areas
    \item \textbf{Investment Guidance:} Quantified neighborhood premiums for decision-making
\end{itemize}

\subsection{Key Concepts to Remember}
\begin{itemize}
    \item \textbf{Categorical Features:} Text categories need special encoding for ML
    \item \textbf{One-Hot Encoding:} Creates binary yes/no features for each category
    \item \textbf{Feature Explosion:} One categorical feature can become many binary features
    \item \textbf{Overfitting:} Too many features relative to data leads to memorization
    \item \textbf{Regularization:} Penalties help models generalize better
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Change the Ridge alpha parameter to 0.1 and 10.0. How do coefficients change?
    \item Find which neighborhood has the most apartments in our dataset
    \item Calculate the price difference between the most and least expensive neighborhoods
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Add surface area as a second feature alongside neighborhood
    \item Create a simple function that predicts price given a neighborhood name
    \item Make a scatter plot of actual vs predicted prices
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Try Lasso regression (L1 regularization) instead of Ridge (L2)
    \item Combine neighborhood with latitude/longitude in one model
    \item Research and implement cross-validation to tune the alpha parameter
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Ridge alpha parameter analysis}
\begin{lstlisting}[caption=Ridge Alpha Parameter Analysis]
from sklearn.linear_model import Ridge

print("RIDGE ALPHA PARAMETER ANALYSIS")
print("=" * 40)

# Test different alpha values
alpha_values = [0.0, 0.1, 1.0, 10.0, 100.0]
models = {}
results = {}

for alpha in alpha_values:
    print(f"\nTraining Ridge with alpha = {alpha}")
    
    # Create and train model
    model_alpha = make_pipeline(
        OneHotEncoder(use_cat_names=True),
        Ridge(alpha=alpha)
    )
    model_alpha.fit(X_train, y_train)
    
    # Evaluate
    y_pred_alpha = model_alpha.predict(X_train)
    mae_alpha = mean_absolute_error(y_train, y_pred_alpha)
    
    # Store results
    models[alpha] = model_alpha
    results[alpha] = {
        'mae': mae_alpha,
        'coefficients': pd.Series(model_alpha.named_steps['ridge'].coef_, index=feature_names)
    }
    
    print(f"  MAE: ${mae_alpha:,.0f}")

# Analyze coefficient changes
print(f"\nCOEFFICIENT ANALYSIS:")
print(f"{'Alpha':<8} {'Max Coef':<12} {'Min Coef':<12} {'Avg |Coef|'}")
print("-" * 50)

for alpha in alpha_values:
    coeffs = results[alpha]['coefficients']
    max_coef = coeffs.max()
    min_coef = coeffs.min()
    avg_abs_coef = coeffs.abs().mean()
    
    print(f"{alpha:<8} ${max_coef:<11,.0f} ${min_coef:<11,.0f} ${avg_abs_coef:,.0f}")

# Visualize alpha effects
plt.figure(figsize=(15, 10))

# Performance vs alpha
plt.subplot(2, 3, 1)
alphas = list(results.keys())
maes = [results[alpha]['mae'] for alpha in alphas]
plt.semilogx(alphas[1:], maes[1:], 'bo-', linewidth=2, markersize=8)  # Skip alpha=0
plt.xlabel("Alpha (log scale)")
plt.ylabel("Mean Absolute Error")
plt.title("Model Performance vs Regularization")
plt.grid(True, alpha=0.3)

# Coefficient shrinkage for top neighborhoods
plt.subplot(2, 3, 2)
top_neighborhoods = results[0.0]['coefficients'].nlargest(5).index
for i, neighborhood in enumerate(top_neighborhoods):
    coef_values = [results[alpha]['coefficients'][neighborhood] for alpha in alpha_values]
    plt.plot(alpha_values, coef_values, 'o-', label=neighborhood.replace('neighborhood_', '')[:10])

plt.xlabel("Alpha")
plt.ylabel("Coefficient Value")
plt.title("Coefficient Shrinkage")
plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
plt.grid(True, alpha=0.3)

# Coefficient magnitude distribution
plt.subplot(2, 3, 3)
for alpha in [0.1, 1.0, 10.0]:
    coeffs = results[alpha]['coefficients']
    plt.hist(coeffs.values, bins=20, alpha=0.6, label=f'α={alpha}', density=True)

plt.xlabel("Coefficient Value")
plt.ylabel("Density")
plt.title("Coefficient Distributions")
plt.legend()
plt.grid(True, alpha=0.3)

# Number of "important" coefficients (abs > threshold)
plt.subplot(2, 3, 4)
threshold = 5000  # $5,000 threshold
important_counts = []
for alpha in alpha_values:
    coeffs = results[alpha]['coefficients']
    important_count = sum(coeffs.abs() > threshold)
    important_counts.append(important_count)

plt.plot(alpha_values, important_counts, 'ro-', linewidth=2, markersize=8)
plt.xlabel("Alpha")
plt.ylabel(f"Features with |coef| > ${threshold:,}")
plt.title("Feature Selection Effect")
plt.grid(True, alpha=0.3)

# Bias-variance tradeoff illustration
plt.subplot(2, 3, 5)
# Simulate complexity vs error (illustrative)
complexity = [1/alpha if alpha > 0 else 100 for alpha in alpha_values[1:]]
plt.plot(complexity, maes[1:], 'go-', linewidth=2, markersize=8)
plt.xlabel("Model Complexity (1/alpha)")
plt.ylabel("Training Error")
plt.title("Complexity vs Error")
plt.grid(True, alpha=0.3)

# Summary insights
plt.subplot(2, 3, 6)
best_alpha = min(alpha_values[1:], key=lambda a: results[a]['mae'])
insights_text = f"""
ALPHA PARAMETER INSIGHTS:

Effect of Increasing Alpha:
• Coefficients shrink toward zero
• Model becomes simpler
• Training error may increase
• Reduces overfitting risk

Best Alpha: {best_alpha}
• MAE: ${results[best_alpha]['mae']:,.0f}
• Balances fit and simplicity

General Guidelines:
• α=0: No regularization
• α=0.1: Light regularization  
• α=1.0: Moderate regularization
• α=10+: Strong regularization

Choose based on validation performance!
"""

plt.text(0.05, 0.95, insights_text, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=10, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 2: Most popular neighborhoods}
\begin{lstlisting}[caption=Neighborhood Popularity Analysis]
print("NEIGHBORHOOD POPULARITY ANALYSIS")
print("=" * 40)

# Count apartments per neighborhood
neighborhood_counts = df['neighborhood'].value_counts()

print(f"Total neighborhoods: {len(neighborhood_counts)}")
print(f"Total apartments: {len(df)}")
print(f"Average apartments per neighborhood: {len(df)/len(neighborhood_counts):.1f}")

print(f"\nTOP 10 MOST POPULAR NEIGHBORHOODS:")
print(f"{'Rank':<4} {'Neighborhood':<25} {'Count':<6} {'Percentage'}")
print("-" * 60)

for i, (neighborhood, count) in enumerate(neighborhood_counts.head(10).items()):
    percentage = (count / len(df)) * 100
    print(f"{i+1:<4} {neighborhood[:24]:<25} {count:<6} {percentage:.1f}%")

print(f"\nTOP 5 LEAST POPULAR NEIGHBORHOODS:")
for i, (neighborhood, count) in enumerate(neighborhood_counts.tail(5).items()):
    percentage = (count / len(df)) * 100
    print(f"{neighborhood[:30]}: {count} apartments ({percentage:.1f}%)")

# Visualize neighborhood distribution
plt.figure(figsize=(15, 10))

# Top neighborhoods bar chart
plt.subplot(2, 2, 1)
top_10 = neighborhood_counts.head(10)
bars = plt.bar(range(len(top_10)), top_10.values, color='skyblue', edgecolor='black')
plt.xlabel("Neighborhood Rank")
plt.ylabel("Number of Apartments")
plt.title("Top 10 Most Popular Neighborhoods")
plt.xticks(range(len(top_10)), [f"{i+1}" for i in range(len(top_10))])
plt.grid(axis='y', alpha=0.3)

# Add value labels
for i, (bar, count) in enumerate(zip(bars, top_10.values)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5,
             f'{count}', ha='center', va='bottom')

# Distribution of neighborhood sizes
plt.subplot(2, 2, 2)
plt.hist(neighborhood_counts.values, bins=20, color='lightgreen', edgecolor='black', alpha=0.7)
plt.xlabel("Apartments per Neighborhood")
plt.ylabel("Number of Neighborhoods")
plt.title("Distribution of Neighborhood Sizes")
plt.grid(axis='y', alpha=0.3)

# Cumulative percentage
plt.subplot(2, 2, 3)
cumulative_pct = (neighborhood_counts.cumsum() / len(df)) * 100
plt.plot(range(1, len(cumulative_pct)+1), cumulative_pct.values, 'b-', linewidth=2)
plt.xlabel("Neighborhood Rank (by size)")
plt.ylabel("Cumulative Percentage of Apartments")
plt.title("Cumulative Distribution")
plt.grid(True, alpha=0.3)

# Add 80/20 rule lines
plt.axhline(80, color='red', linestyle='--', alpha=0.7, label='80%')
plt.axvline(len(cumulative_pct)*0.2, color='red', linestyle='--', alpha=0.7, label='Top 20%')
plt.legend()

# Summary statistics
plt.subplot(2, 2, 4)
summary_stats = f"""
NEIGHBORHOOD STATISTICS:

Size Distribution:
• Largest: {neighborhood_counts.max()} apartments
• Smallest: {neighborhood_counts.min()} apartments
• Median: {neighborhood_counts.median():.0f} apartments
• Mean: {neighborhood_counts.mean():.1f} apartments

Concentration:
• Top 10 neighborhoods: {(neighborhood_counts.head(10).sum()/len(df)*100):.1f}% of apartments
• Top 25%: {(neighborhood_counts.head(len(neighborhood_counts)//4).sum()/len(df)*100):.1f}% of apartments

Data Quality:
• Well-distributed dataset
• No extreme concentration
• Good representation across areas
"""

plt.text(0.05, 0.95, summary_stats, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=11, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Business insights
most_popular = neighborhood_counts.index[0]
least_popular = neighborhood_counts.index[-1]

print(f"\nBUSINESS INSIGHTS:")
print(f"• Most popular: {most_popular} ({neighborhood_counts[most_popular]} apartments)")
print(f"• This neighborhood has {neighborhood_counts[most_popular]/neighborhood_counts[least_popular]:.1f}x more apartments than least popular")
print(f"• Top 10 neighborhoods represent {(neighborhood_counts.head(10).sum()/len(df)*100):.1f}% of all apartments")
print(f"• Market is reasonably well-distributed across neighborhoods")

# Check if popular neighborhoods are expensive
if 'price_aprox_usd' in df.columns:
    print(f"\nPOPULARITY vs PRICE ANALYSIS:")
    for neighborhood in neighborhood_counts.head(5).index:
        neighborhood_prices = df[df['neighborhood'] == neighborhood]['price_aprox_usd']
        avg_price = neighborhood_prices.mean()
        count = len(neighborhood_prices)
        print(f"{neighborhood}: {count} apartments, avg price ${avg_price:,.0f}")
\end{lstlisting}

\textbf{Exercise 3: Price difference between neighborhoods}
\begin{lstlisting}[caption=Neighborhood Price Range Analysis]
print("NEIGHBORHOOD PRICE RANGE ANALYSIS")
print("=" * 45)

# Calculate average price per neighborhood
neighborhood_prices = df.groupby('neighborhood')['price_aprox_usd'].agg(['mean', 'count', 'std']).reset_index()
neighborhood_prices.columns = ['neighborhood', 'avg_price', 'count', 'std_price']

# Filter neighborhoods with sufficient data (at least 3 apartments)
neighborhood_prices = neighborhood_prices[neighborhood_prices['count'] >= 3]

# Sort by average price
neighborhood_prices = neighborhood_prices.sort_values('avg_price', ascending=False)

print(f"Analysis based on {len(neighborhood_prices)} neighborhoods with 3+ apartments")

# Most and least expensive
most_expensive = neighborhood_prices.iloc[0]
least_expensive = neighborhood_prices.iloc[-1]

price_difference = most_expensive['avg_price'] - least_expensive['avg_price']
price_ratio = most_expensive['avg_price'] / least_expensive['avg_price']

print(f"\nEXTREME NEIGHBORHOODS:")
print(f"Most expensive: {most_expensive['neighborhood']}")
print(f"  Average price: ${most_expensive['avg_price']:,.0f}")
print(f"  Number of apartments: {most_expensive['count']}")

print(f"\nLeast expensive: {least_expensive['neighborhood']}")
print(f"  Average price: ${least_expensive['avg_price']:,.0f}")
print(f"  Number of apartments: {least_expensive['count']}")

print(f"\nPRICE DIFFERENCE:")
print(f"Absolute difference: ${price_difference:,.0f}")
print(f"Price ratio: {price_ratio:.1f}x")
print(f"Percentage difference: {((most_expensive['avg_price']/least_expensive['avg_price'])-1)*100:.0f}%")

# Top 10 most expensive neighborhoods
print(f"\nTOP 10 MOST EXPENSIVE NEIGHBORHOODS:")
print(f"{'Rank':<4} {'Neighborhood':<25} {'Avg Price':<12} {'Count'}")
print("-" * 65)

for i, row in neighborhood_prices.head(10).iterrows():
    print(f"{len(neighborhood_prices)-i:<4} {row['neighborhood'][:24]:<25} ${row['avg_price']:<11,.0f} {row['count']}")

print(f"\nTOP 10 MOST AFFORDABLE NEIGHBORHOODS:")
print(f"{'Rank':<4} {'Neighborhood':<25} {'Avg Price':<12} {'Count'}")
print("-" * 65)

for i, row in neighborhood_prices.tail(10).iterrows():
    rank = len(neighborhood_prices) - list(neighborhood_prices.index).index(i)
    print(f"{rank:<4} {row['neighborhood'][:24]:<25} ${row['avg_price']:<11,.0f} {row['count']}")

# Visualize price ranges
plt.figure(figsize=(15, 12))

# Top and bottom neighborhoods
plt.subplot(3, 2, 1)
extreme_neighborhoods = pd.concat([neighborhood_prices.head(5), neighborhood_prices.tail(5)])
colors = ['red']*5 + ['green']*5
bars = plt.barh(range(len(extreme_neighborhoods)), extreme_neighborhoods['avg_price'], color=colors, alpha=0.7)

plt.yticks(range(len(extreme_neighborhoods)), 
          [name[:20] for name in extreme_neighborhoods['neighborhood']])
plt.xlabel("Average Price (USD)")
plt.title("Most Expensive vs Most Affordable")
plt.grid(axis='x', alpha=0.3)

# Price distribution across all neighborhoods
plt.subplot(3, 2, 2)
plt.hist(neighborhood_prices['avg_price'], bins=20, color='skyblue', edgecolor='black', alpha=0.7)
plt.xlabel("Average Price (USD)")
plt.ylabel("Number of Neighborhoods")
plt.title("Distribution of Neighborhood Prices")
plt.axvline(neighborhood_prices['avg_price'].mean(), color='red', linestyle='--', 
           label=f"Overall mean: ${neighborhood_prices['avg_price'].mean():,.0f}")
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Price vs apartment count
plt.subplot(3, 2, 3)
plt.scatter(neighborhood_prices['count'], neighborhood_prices['avg_price'], 
           alpha=0.6, s=50, color='purple')
plt.xlabel("Number of Apartments")
plt.ylabel("Average Price")
plt.title("Price vs Apartment Count")
plt.grid(True, alpha=0.3)

# Add correlation
correlation = neighborhood_prices['count'].corr(neighborhood_prices['avg_price'])
plt.text(0.05, 0.95, f'Correlation: {correlation:.3f}', transform=plt.gca().transAxes,
         bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

# Price standard deviation
plt.subplot(3, 2, 4)
# Remove neighborhoods with NaN std (single apartment)
valid_std = neighborhood_prices.dropna(subset=['std_price'])
plt.scatter(valid_std['avg_price'], valid_std['std_price'], alpha=0.6, s=50, color='orange')
plt.xlabel("Average Price")
plt.ylabel("Price Standard Deviation")
plt.title("Price Variability within Neighborhoods")
plt.grid(True, alpha=0.3)

# Neighborhood price tiers
plt.subplot(3, 2, 5)
# Create price tiers
neighborhood_prices['tier'] = pd.cut(neighborhood_prices['avg_price'], 
                                   bins=5, labels=['Budget', 'Affordable', 'Mid-range', 'Premium', 'Luxury'])
tier_counts = neighborhood_prices['tier'].value_counts()

plt.pie(tier_counts.values, labels=tier_counts.index, autopct='%1.1f%%', 
        colors=['lightgreen', 'yellow', 'orange', 'red', 'purple'])
plt.title("Neighborhood Price Tiers")

# Summary statistics
plt.subplot(3, 2, 6)
summary_stats = f"""
NEIGHBORHOOD PRICE ANALYSIS:

Price Range:
• Highest: ${most_expensive['avg_price']:,.0f}
• Lowest: ${least_expensive['avg_price']:,.0f}
• Difference: ${price_difference:,.0f}
• Ratio: {price_ratio:.1f}x

Statistics:
• Mean price: ${neighborhood_prices['avg_price'].mean():,.0f}
• Median price: ${neighborhood_prices['avg_price'].median():,.0f}
• Std deviation: ${neighborhood_prices['avg_price'].std():,.0f}

Market Insights:
• Wide price variation across city
• Location premium can exceed ${price_difference//1000:.0f}K
• Clear market segmentation
• Investment opportunities in undervalued areas
"""

plt.text(0.05, 0.95, summary_stats, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=11, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Check against model coefficients
print(f"\nMODEL COEFFICIENT VALIDATION:")
if 'coef_series' in globals():
    most_exp_coef = coef_series.get(f'neighborhood_{most_expensive["neighborhood"]}', 0)
    least_exp_coef = coef_series.get(f'neighborhood_{least_expensive["neighborhood"]}', 0)
    
    print(f"Most expensive neighborhood coefficient: ${most_exp_coef:,.0f}")
    print(f"Least expensive neighborhood coefficient: ${least_exp_coef:,.0f}")
    print(f"Model coefficient difference: ${most_exp_coef - least_exp_coef:,.0f}")
    print(f"Actual price difference: ${price_difference:,.0f}")
    print(f"Model captures {abs(most_exp_coef - least_exp_coef)/price_difference*100:.1f}% of actual difference")
\end{lstlisting}

Congratulations! You've successfully learned to work with categorical features and handle overfitting. These are essential skills for real-world machine learning projects.

\end{document} 