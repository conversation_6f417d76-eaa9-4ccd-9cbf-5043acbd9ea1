\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 1.4: Location vs Size Analysis}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 1.4: Location vs Size Analysis} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large What Really Drives Property Prices?}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: The Great Real Estate Mystery!}

Welcome to our investigation into what really drives property prices! Is it location, location, location - or does size matter more? Today we'll become data detectives, using our Mexico real estate data to solve this mystery through systematic analysis.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item Asking specific research questions and testing them with data
    \item Comparing prices across different locations (states)
    \item Understanding the relationship between property size and price
    \item Creating meaningful comparisons using groupby operations
    \item Visualizing findings with bar charts and scatter plots
    \item Drawing evidence-based conclusions about real estate markets
\end{itemize}

\begin{concept}
\textbf{Research Questions in Data Science:}
Great data analysis starts with great questions:
\begin{itemize}
    \item \textbf{Specific:} "Which state has highest prices?" not "Are prices different?"
    \item \textbf{Testable:} Can be answered with available data
    \item \textbf{Relevant:} Matters for decision-making
    \item \textbf{Comparable:} Can be measured consistently across groups
\end{itemize}
\end{concept}

\section{Task 1.4.1: Setting Up Our Investigation}

Let's load our cleaned data and prepare for analysis.

\begin{lstlisting}[caption=Loading Our Data]
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Load our cleaned Mexico real estate data
df = pd.read_csv("data/mexico-real-estate-clean.csv")

print(f"Dataset shape: {df.shape}")
print(f"Columns available: {list(df.columns)}")

# Quick overview of our data
print("\nFirst few rows:")
print(df.head())

# Basic statistics
print(f"\nDataset summary:")
print(f"- {len(df):,} properties")
print(f"- {df['state'].nunique()} different states")
print(f"- Price range: ${df['price_usd'].min():,.0f} to ${df['price_usd'].max():,.0f}")
print(f"- Size range: {df['area_sqm'].min():.0f} to {df['area_sqm'].max():.0f} sqm")
\end{lstlisting}

\begin{learningtip}
\textbf{Always Start with Data Overview:}
\begin{itemize}
    \item Check the data shape to understand how much data you have
    \item Look at column names to see what variables are available
    \item Review the first few rows to understand data format
    \item Get basic statistics to understand data ranges
\end{itemize}
\end{learningtip}

\section{Task 1.4.2: Research Question 1 - Which State Has the Most Expensive Real Estate?}

Let's investigate our first research question about location and prices.

\begin{lstlisting}[caption=Analyzing Prices by State]
# Group by state and calculate mean prices
state_prices = df.groupby('state')['price_usd'].agg(['mean', 'median', 'count'])

# Sort by mean price (descending)
state_prices_sorted = state_prices.sort_values('mean', ascending=False)

print("Top 10 Most Expensive States (by average price):")
print(state_prices_sorted.head(10))

# Look at the most and least expensive states
most_expensive = state_prices_sorted.index[0]
least_expensive = state_prices_sorted.index[-1]

print(f"\nMost expensive state: {most_expensive}")
print(f"Average price: ${state_prices_sorted.loc[most_expensive, 'mean']:,.0f}")

print(f"\nLeast expensive state: {least_expensive}")
print(f"Average price: ${state_prices_sorted.loc[least_expensive, 'mean']:,.0f}")

# Calculate the price difference
price_ratio = state_prices_sorted.loc[most_expensive, 'mean'] / state_prices_sorted.loc[least_expensive, 'mean']
print(f"\nPrice difference: {price_ratio:.1f}x more expensive")
\end{lstlisting}

\begin{concept}
\textbf{Groupby Operations:}
The \texttt{groupby()} function is like organizing data into folders:
\begin{itemize}
    \item \textbf{Split:} Divide data into groups (by state)
    \item \textbf{Apply:} Calculate statistics for each group (mean, median)
    \item \textbf{Combine:} Put results back together in a summary table
    \item \textbf{Use cases:} Any time you want to compare categories
\end{itemize}
\end{concept}

\section{Task 1.4.3: Visualizing State Price Differences}

Let's create a visual comparison of state prices.

\begin{lstlisting}[caption=Creating State Price Comparison Chart]
# Create a bar chart of top 10 most expensive states
plt.figure(figsize=(12, 6))

top_10_states = state_prices_sorted.head(10)
bars = plt.bar(range(len(top_10_states)), top_10_states['mean'], 
               color='skyblue', edgecolor='navy', alpha=0.7)

plt.title('Average Property Prices by State (Top 10)', fontsize=16, pad=20)
plt.xlabel('State', fontsize=12)
plt.ylabel('Average Price (USD)', fontsize=12)
plt.xticks(range(len(top_10_states)), top_10_states.index, rotation=45, ha='right')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1000,
             f'${height:,.0f}', ha='center', va='bottom', fontsize=10)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.show()

# Also create a comparison with sample sizes
print("\nSample sizes matter! Let's check how many properties per state:")
print("State data with sample sizes:")
for state in top_10_states.index[:5]:
    count = top_10_states.loc[state, 'count']
    mean_price = top_10_states.loc[state, 'mean']
    print(f"{state}: {count} properties, avg ${mean_price:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Effective Bar Chart Design:}
\begin{itemize}
    \item Use clear, descriptive titles and axis labels
    \item Add value labels on bars for exact numbers
    \item Use consistent colors and proper spacing
    \item Always check sample sizes - averages based on few data points can be misleading
    \item Consider both mean and median for skewed data
\end{itemize}
\end{learningtip}

\section{Task 1.4.4: Price Per Square Meter Analysis}

Raw prices can be misleading because properties have different sizes. Let's create a fair comparison.

\begin{lstlisting}[caption=Calculating Price Per Square Meter]
# Calculate price per square meter if not already present
if 'price_per_sqm' not in df.columns:
    df['price_per_sqm'] = df['price_usd'] / df['area_sqm']

# Analyze price per sqm by state
state_price_per_sqm = df.groupby('state')['price_per_sqm'].agg(['mean', 'median', 'count'])
state_price_per_sqm_sorted = state_price_per_sqm.sort_values('mean', ascending=False)

print("Top 10 States by Price per Square Meter:")
print(state_price_per_sqm_sorted.head(10))

# Compare this ranking with our previous ranking
print("\nComparison: Total Price vs Price per SqM")
print("State Rankings Changed:")
for i, state in enumerate(state_price_per_sqm_sorted.head(5).index):
    old_rank = list(state_prices_sorted.index).index(state) + 1
    new_rank = i + 1
    print(f"{state}: Total price rank #{old_rank} -> Price/sqm rank #{new_rank}")
\end{lstlisting}

\begin{concept}
\textbf{Price Normalization:}
Price per square meter is a "normalized" metric:
\begin{itemize}
    \item \textbf{Fair comparison:} Controls for property size differences
    \item \textbf{Market value:} Shows true cost of space in each location
    \item \textbf{Investment insight:} Reveals which areas are truly expensive
    \item \textbf{Example:} A $100,000 house that's 200 sqm vs 50 sqm tells very different stories
\end{itemize}
\end{concept}

\section{Task 1.4.5: Research Question 2 - How Does Property Size Influence Price?}

Now let's investigate the relationship between property size and price.

\begin{lstlisting}[caption=Analyzing Size-Price Relationship]
# Create a scatter plot of size vs price
plt.figure(figsize=(12, 5))

# Plot 1: Regular scale
plt.subplot(1, 2, 1)
plt.scatter(df['area_sqm'], df['price_usd'], alpha=0.6, color='coral')
plt.title('Property Size vs Price')
plt.xlabel('Area (Square Meters)')
plt.ylabel('Price (USD)')
plt.grid(True, alpha=0.3)

# Plot 2: Log scale for better visualization
plt.subplot(1, 2, 2)
plt.scatter(df['area_sqm'], df['price_usd'], alpha=0.6, color='lightgreen')
plt.title('Property Size vs Price (Log Scale)')
plt.xlabel('Area (Square Meters)')
plt.ylabel('Price (USD)')
plt.xscale('log')
plt.yscale('log')
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Calculate correlation
correlation = df['area_sqm'].corr(df['price_usd'])
print(f"\nCorrelation between size and price: {correlation:.3f}")

# Interpret the correlation
if correlation > 0.7:
    strength = "strong"
elif correlation > 0.3:
    strength = "moderate"
else:
    strength = "weak"

print(f"This indicates a {strength} positive relationship.")
print("Meaning: larger properties tend to have higher prices.")
\end{lstlisting}

\begin{learningtip}
\textbf{Understanding Correlation:}
\begin{itemize}
    \item \textbf{Scale:} -1 to +1 (negative to positive relationship)
    \item \textbf{Strong:} 0.7+ (variables move together predictably)
    \item \textbf{Moderate:} 0.3-0.7 (noticeable relationship)
    \item \textbf{Weak:} Below 0.3 (little relationship)
    \item \textbf{Remember:} Correlation doesn't prove causation!
\end{itemize}
\end{learningtip}

\section{Task 1.4.6: Deep Dive - Analyzing Individual States}

Let's examine the size-price relationship in specific states to see if it varies by location.

\begin{lstlisting}[caption=State-Specific Analysis]
# Focus on states with sufficient data
state_counts = df['state'].value_counts()
states_with_data = state_counts[state_counts >= 50].index[:4]  # Top 4 states with most data

print(f"Analyzing states with most data: {list(states_with_data)}")

# Create subplots for each state
fig, axes = plt.subplots(2, 2, figsize=(15, 10))
axes = axes.flatten()

correlations = {}

for i, state in enumerate(states_with_data):
    # Filter data for this state
    state_data = df[df['state'] == state]
    
    # Create scatter plot
    axes[i].scatter(state_data['area_sqm'], state_data['price_usd'], 
                   alpha=0.6, color=plt.cm.tab10(i))
    axes[i].set_title(f'{state} (n={len(state_data)})')
    axes[i].set_xlabel('Area (sqm)')
    axes[i].set_ylabel('Price (USD)')
    axes[i].grid(True, alpha=0.3)
    
    # Calculate correlation for this state
    correlation = state_data['area_sqm'].corr(state_data['price_usd'])
    correlations[state] = correlation
    
    # Add correlation to plot
    axes[i].text(0.05, 0.95, f'r = {correlation:.3f}', 
                transform=axes[i].transAxes, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

plt.tight_layout()
plt.show()

# Compare correlations across states
print("\nSize-Price Correlations by State:")
for state, corr in correlations.items():
    print(f"{state}: {corr:.3f}")
\end{lstlisting}

\section{Task 1.4.7: Price Category Analysis}

Let's create price categories to better understand market segments.

\begin{lstlisting}[caption=Creating Price Categories]
# Define price categories based on percentiles
price_percentiles = df['price_usd'].quantile([0.25, 0.5, 0.75])
print("Price percentiles:")
print(f"25th percentile (Low): ${price_percentiles[0.25]:,.0f}")
print(f"50th percentile (Mid): ${price_percentiles[0.5]:,.0f}")
print(f"75th percentile (High): ${price_percentiles[0.75]:,.0f}")

# Create price categories
def categorize_price(price):
    if price <= price_percentiles[0.25]:
        return "Budget"
    elif price <= price_percentiles[0.5]:
        return "Mid-Range"
    elif price <= price_percentiles[0.75]:
        return "Premium"
    else:
        return "Luxury"

df['price_category'] = df['price_usd'].apply(categorize_price)

# Analyze categories
category_analysis = df.groupby('price_category').agg({
    'price_usd': ['mean', 'count'],
    'area_sqm': 'mean',
    'price_per_sqm': 'mean'
}).round(0)

print("\nPrice Category Analysis:")
print(category_analysis)

# Visualize price categories
plt.figure(figsize=(12, 4))

plt.subplot(1, 2, 1)
category_counts = df['price_category'].value_counts()
plt.pie(category_counts.values, labels=category_counts.index, autopct='%1.1f%%')
plt.title('Distribution of Price Categories')

plt.subplot(1, 2, 2)
category_avg_size = df.groupby('price_category')['area_sqm'].mean()
plt.bar(category_avg_size.index, category_avg_size.values, color='lightcoral')
plt.title('Average Size by Price Category')
plt.ylabel('Average Area (sqm)')
plt.xticks(rotation=45)

plt.tight_layout()
plt.show()
\end{lstlisting}

\begin{concept}
\textbf{Market Segmentation:}
Creating categories helps understand different market segments:
\begin{itemize}
    \item \textbf{Budget:} Entry-level properties (bottom 25\%)
    \item \textbf{Mid-Range:} Typical market (25-50\%)
    \item \textbf{Premium:} Above average (50-75\%)
    \item \textbf{Luxury:} High-end market (top 25\%)
\end{itemize}
This reveals different buyer segments and their preferences.
\end{concept}

\section{Task 1.4.8: Final Insights and Recommendations}

Let's summarize our findings and draw conclusions.

\begin{lstlisting}[caption=Drawing Conclusions from Our Analysis]
# Create a comprehensive summary
print("MEXICO REAL ESTATE ANALYSIS - KEY FINDINGS")
print("=" * 60)

# Finding 1: Location impact
most_expensive_state = state_prices_sorted.index[0]
least_expensive_state = state_prices_sorted.index[-1]
location_impact = state_prices_sorted.loc[most_expensive_state, 'mean'] / state_prices_sorted.loc[least_expensive_state, 'mean']

print(f"\n1. LOCATION IMPACT:")
print(f"   - Most expensive state: {most_expensive_state}")
print(f"   - Least expensive state: {least_expensive_state}")
print(f"   - Price difference: {location_impact:.1f}x")
print(f"   - Conclusion: Location has MAJOR impact on prices")

# Finding 2: Size relationship
overall_correlation = df['area_sqm'].corr(df['price_usd'])
print(f"\n2. SIZE RELATIONSHIP:")
print(f"   - Overall correlation: {overall_correlation:.3f}")
print(f"   - Average price per sqm: ${df['price_per_sqm'].mean():.0f}")
print(f"   - Conclusion: Size moderately influences price")

# Finding 3: Market segments
luxury_avg_size = df[df['price_category'] == 'Luxury']['area_sqm'].mean()
budget_avg_size = df[df['price_category'] == 'Budget']['area_sqm'].mean()

print(f"\n3. MARKET SEGMENTS:")
print(f"   - Luxury properties average: {luxury_avg_size:.0f} sqm")
print(f"   - Budget properties average: {budget_avg_size:.0f} sqm")
print(f"   - Size difference: {luxury_avg_size/budget_avg_size:.1f}x")

# Investment recommendations
print(f"\n4. INVESTMENT INSIGHTS:")
best_value_state = state_price_per_sqm_sorted.index[-1]  # Lowest price per sqm
print(f"   - Best value state: {best_value_state}")
print(f"   - Price per sqm: ${state_price_per_sqm_sorted.loc[best_value_state, 'mean']:.0f}")
print(f"   - Recommendation: Consider {best_value_state} for value investments")

# Answer our research questions
print(f"\nRESEARCH QUESTIONS ANSWERED:")
print(f"Q1: Which state is most expensive? A: {most_expensive_state}")
print(f"Q2: Does size matter? A: Yes, correlation = {overall_correlation:.3f}")
print(f"Q3: What matters more? A: Location (up to {location_impact:.1f}x difference)")
\end{lstlisting}

\section{Summary: What We Discovered}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Groupby Operations:} Comparing categories and calculating group statistics
    \item \textbf{Data Visualization:} Creating bar charts and scatter plots for analysis
    \item \textbf{Correlation Analysis:} Measuring relationships between variables
    \item \textbf{Data Categorization:} Creating meaningful segments from continuous data
    \item \textbf{Comparative Analysis:} Ranking and comparing different groups
    \item \textbf{Research Questions:} Formulating and testing hypotheses with data
\end{enumerate}

\subsection{Key Insights About Real Estate}
\begin{itemize}
    \item \textbf{Location Dominates:} State location has the biggest impact on prices
    \item \textbf{Size Matters:} Property size moderately correlates with price
    \item \textbf{Normalization Reveals Truth:} Price per sqm shows true market values
    \item \textbf{Market Segments:} Different price tiers have different characteristics
    \item \textbf{Investment Opportunities:} Data reveals undervalued markets
\end{itemize}

\subsection{Real-World Applications}
\begin{itemize}
    \item Real estate investment decision-making
    \item Market analysis for property developers
    \item Pricing strategies for real estate agents
    \item Government policy analysis for housing affordability
    \item Personal home buying decisions
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Find the state with the median number of properties in our dataset
    \item Calculate the average property size in the most expensive state
    \item Create a simple histogram showing the distribution of property sizes
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Analyze which states have the most consistent pricing (lowest price variability)
    \item Create size categories (Small, Medium, Large) and analyze price differences
    \item Compare rural vs urban areas if location data is detailed enough
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Create a "value score" combining price, size, and location factors
    \item Perform regression analysis to predict prices based on size and state
    \item Identify outlier properties that don't fit typical patterns
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Find the state with the median number of properties}
\begin{lstlisting}[caption=Finding Median State by Property Count]
# Get property counts by state
state_counts = df['state'].value_counts().sort_values()
median_position = len(state_counts) // 2
median_state = state_counts.index[median_position]

print(f"State with median number of properties: {median_state}")
print(f"Number of properties: {state_counts[median_state]}")
\end{lstlisting}

\textbf{Exercise 2: Calculate average property size in most expensive state}
\begin{lstlisting}[caption=Average Size in Most Expensive State]
most_expensive_state = state_prices_sorted.index[0]
avg_size_expensive = df[df['state'] == most_expensive_state]['area_sqm'].mean()

print(f"Average property size in {most_expensive_state}: {avg_size_expensive:.0f} sqm")
\end{lstlisting}

\textbf{Exercise 3: Create histogram of property sizes}
\begin{lstlisting}[caption=Property Size Distribution]
plt.figure(figsize=(10, 6))
plt.hist(df['area_sqm'], bins=30, color='skyblue', edgecolor='black', alpha=0.7)
plt.title('Distribution of Property Sizes')
plt.xlabel('Area (Square Meters)')
plt.ylabel('Frequency')
plt.grid(axis='y', alpha=0.3)
plt.show()

print(f"Average size: {df['area_sqm'].mean():.0f} sqm")
print(f"Median size: {df['area_sqm'].median():.0f} sqm")
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Find states with most consistent pricing}
\begin{lstlisting}[caption=Price Consistency Analysis]
# Calculate coefficient of variation (std/mean) for each state
price_consistency = df.groupby('state')['price_usd'].agg(['mean', 'std', 'count'])
price_consistency['cv'] = price_consistency['std'] / price_consistency['mean']

# Filter states with sufficient data
consistent_states = price_consistency[price_consistency['count'] >= 20].sort_values('cv')

print("Most consistent pricing (lowest variability):")
print(consistent_states[['mean', 'cv']].head())
\end{lstlisting}

\textbf{Exercise 2: Create size categories and analyze price differences}
\begin{lstlisting}[caption=Size Category Analysis]
# Create size categories
def size_category(area):
    if area < 75:
        return "Small"
    elif area < 150:
        return "Medium"
    else:
        return "Large"

df['size_cat'] = df['area_sqm'].apply(size_category)

# Analyze price differences
size_analysis = df.groupby('size_cat')['price_usd'].agg(['count', 'mean', 'median'])
print("Price analysis by size category:")
print(size_analysis)

# Visualize
plt.figure(figsize=(10, 6))
plt.bar(size_analysis.index, size_analysis['mean'], color=['lightblue', 'orange', 'lightgreen'])
plt.title('Average Price by Size Category')
plt.ylabel('Average Price (USD)')
plt.show()
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Create a value score combining multiple factors}
\begin{lstlisting}[caption=Multi-Factor Value Score]
# Normalize metrics to 0-1 scale
from sklearn.preprocessing import MinMaxScaler

scaler = MinMaxScaler()
df_scaled = df.copy()

# Scale key metrics (lower price = higher score, higher size = higher score)
df_scaled['price_score'] = 1 - scaler.fit_transform(df[['price_usd']])[:, 0]
df_scaled['size_score'] = scaler.fit_transform(df[['area_sqm']])[:, 0]
df_scaled['price_per_sqm_score'] = 1 - scaler.fit_transform(df[['price_per_sqm']])[:, 0]

# Create composite value score
df_scaled['value_score'] = (df_scaled['price_score'] * 0.4 + 
                           df_scaled['size_score'] * 0.3 + 
                           df_scaled['price_per_sqm_score'] * 0.3)

# Find best value properties
best_values = df_scaled.nlargest(10, 'value_score')[['state', 'price_usd', 'area_sqm', 'value_score']]
print("Top 10 best value properties:")
print(best_values)
\end{lstlisting}

\textbf{Exercise 2: Identify outlier properties}
\begin{lstlisting}[caption=Outlier Detection]
# Statistical outlier detection using IQR method
Q1 = df['price_per_sqm'].quantile(0.25)
Q3 = df['price_per_sqm'].quantile(0.75)
IQR = Q3 - Q1

# Define outlier bounds
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

# Find outliers
outliers = df[(df['price_per_sqm'] < lower_bound) | (df['price_per_sqm'] > upper_bound)]

print(f"Found {len(outliers)} outlier properties:")
print("Extremely cheap properties (potential deals or data errors):")
print(outliers[outliers['price_per_sqm'] < lower_bound][['state', 'price_usd', 'area_sqm', 'price_per_sqm']].head())

print("\nExtremely expensive properties (luxury or data errors):")
print(outliers[outliers['price_per_sqm'] > upper_bound][['state', 'price_usd', 'area_sqm', 'price_per_sqm']].head())
\end{lstlisting}

\section{Next Steps in Your Data Journey}

\textbf{Coming up in future projects:}
\begin{itemize}
    \item \textbf{Project 1.5:} Advanced multi-dataset analysis with Brazilian data
    \item \textbf{Project 2.1:} Introduction to machine learning and prediction
    \item \textbf{Advanced topics:} Time series analysis, geographic mapping
    \item \textbf{Specializations:} Deep dive into specific industries
\end{itemize}

Congratulations! You've successfully conducted a comprehensive real estate market analysis, answering specific research questions with data-driven evidence. You can now formulate hypotheses, test them systematically, and draw meaningful conclusions from data.

\end{document} 