{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Project 1.5: Housing in Brazil 🇧🇷\n", "\n", "## Learning Objectives\n", "By the end of this lesson, you will be able to:\n", "- Import and clean real estate data from CSV files\n", "- Handle geographic coordinate data (latitude/longitude)\n", "- Extract state information from hierarchical location strings\n", "- Transform string price data to numerical format\n", "- Analyze regional differences in real estate markets\n", "- Create interactive maps using plotly\n", "- Investigate price-size relationships across different regions\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px\n", "import numpy as np\n", "import seaborn as sns\n", "\n", "# Set visualization styles\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Data Import and Initial Exploration\n", "\n", "We'll work with Brazilian real estate data from two CSV files. Let's start by importing and examining the first dataset."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# Task 1.5.1: Import the first CSV file\n", "df1 = pd.read_csv(\"data/brasil-real-estate-1.csv\")\n", "\n", "# Display basic information about the dataset\n", "print(\"Dataset Shape:\", df1.shape)\n", "print(\"\\nColumn Information:\")\n", "df1.info()\n", "\n", "print(\"\\nFirst few rows:\")\n", "df1.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# Check for missing values and data types\n", "print(\"Missing Values:\")\n", "print(df1.isnull().sum())\n", "\n", "print(\"\\nUnique values in key columns:\")\n", "print(f\"Property types: {df1['property_type'].unique()}\")\n", "print(f\"Regions: {df1['region'].unique()}\")\n", "print(f\"Price format example: {df1['price_usd'].iloc[0]}\")"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## Data Cleaning and Transformation\n", "\n", "### Step 1: Remove Missing Values"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["# Task 1.5.2: Drop all rows with NaN values\n", "print(f\"Rows before dropping NaN: {len(df1)}\")\n", "df1.dropna(inplace=True)\n", "print(f\"Rows after dropping NaN: {len(df1)}\")\n", "\n", "# Verify no missing values remain\n", "print(f\"\\nRemaining missing values: {df1.isnull().sum().sum()}\")"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["### Step 2: Extract Latitude and Longitude"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# Task 1.5.3: Create separate lat and lon columns\n", "print(\"Sample lat-lon values:\")\n", "print(df1[\"lat-lon\"].head())\n", "\n", "# Split the lat-lon column and convert to float\n", "df1[[\"lat\", \"lon\"]] = df1[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "\n", "print(\"\\nAfter splitting:\")\n", "print(df1[[\"lat-lon\", \"lat\", \"lon\"]].head())\n", "\n", "# Verify data types\n", "print(f\"\\nLatitude data type: {df1['lat'].dtype}\")\n", "print(f\"Longitude data type: {df1['lon'].dtype}\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### Step 3: Extract State Information"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# Task 1.5.4: Create state column from place_with_parent_names\n", "print(\"Sample place_with_parent_names values:\")\n", "print(df1[\"place_with_parent_names\"].head())\n", "\n", "# Split the location string to extract state (always after |Brasil|)\n", "location_parts = df1[\"place_with_parent_names\"].str.split(\"|\", expand=True)\n", "print(\"\\nLocation parts:\")\n", "print(location_parts.head())\n", "\n", "# Extract state (column 2 after splitting)\n", "df1[\"state\"] = location_parts[2]\n", "\n", "print(\"\\nUnique states in dataset:\")\n", "print(df1[\"state\"].unique())\n", "print(f\"\\nNumber of unique states: {df1['state'].nunique()}\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["### Step 4: Clean Price Data"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# Task 1.5.5: Transform price_usd to float\n", "print(\"Sample price values before cleaning:\")\n", "print(df1[\"price_usd\"].head())\n", "\n", "# Remove dollar sign and commas, then convert to float\n", "df1[\"price_usd\"] = df1[\"price_usd\"].str.replace(\"$\", \"\", regex=False).str.replace(\",\", \"\").astype(float)\n", "\n", "print(\"\\nAfter cleaning:\")\n", "print(df1[\"price_usd\"].head())\n", "\n", "# Check data type and summary statistics\n", "print(f\"\\nPrice data type: {df1['price_usd'].dtype}\")\n", "print(\"\\nPrice summary statistics:\")\n", "print(df1[\"price_usd\"].describe())"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["### Step 5: Remove Unnecessary Columns"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# Task 1.5.6: Drop lat-lon and place_with_parent_names columns\n", "print(f\"Columns before dropping: {list(df1.columns)}\")\n", "\n", "df1.drop(columns=[\"lat-lon\", \"place_with_parent_names\"], inplace=True)\n", "\n", "print(f\"\\nColumns after dropping: {list(df1.columns)}\")\n", "print(f\"\\nFinal dataset shape: {df1.shape}\")\n", "\n", "# Display cleaned dataset\n", "print(\"\\nCleaned dataset:\")\n", "df1.head()"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## Import and Clean Second Dataset\n", "\n", "Now let's import and clean the second CSV file using the same process."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["# Import second dataset\n", "df2 = pd.read_csv(\"data/brasil-real-estate-2.csv\")\n", "\n", "print(f\"Dataset 2 shape: {df2.shape}\")\n", "print(\"\\nDataset 2 info:\")\n", "df2.info()\n", "\n", "# Apply same cleaning steps\n", "print(f\"\\nRows before cleaning: {len(df2)}\")\n", "\n", "# Remove missing values\n", "df2.dropna(inplace=True)\n", "\n", "# Extract lat/lon\n", "df2[[\"lat\", \"lon\"]] = df2[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "\n", "# Extract state\n", "df2[\"state\"] = df2[\"place_with_parent_names\"].str.split(\"|\", expand=True)[2]\n", "\n", "# Clean price\n", "df2[\"price_usd\"] = df2[\"price_usd\"].str.replace(\"$\", \"\", regex=False).str.replace(\",\", \"\").astype(float)\n", "\n", "# Drop unnecessary columns\n", "df2.drop(columns=[\"lat-lon\", \"place_with_parent_names\"], inplace=True)\n", "\n", "print(f\"Rows after cleaning: {len(df2)}\")\n", "print(f\"Final shape: {df2.shape}\")"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["## Combine Datasets\n", "\n", "Let's combine both cleaned datasets for our analysis."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# Combine datasets\n", "df = pd.concat([df1, df2], ignore_index=True)\n", "\n", "print(f\"Combined dataset shape: {df.shape}\")\n", "print(f\"\\nTotal unique states: {df['state'].nunique()}\")\n", "print(f\"States: {sorted(df['state'].unique())}\")\n", "\n", "# Summary statistics\n", "print(\"\\nCombined dataset summary:\")\n", "df.describe()"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["## Regional Analysis\n", "\n", "### Property Distribution by Region"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["# Analyze property distribution by region\n", "region_counts = df['region'].value_counts()\n", "print(\"Properties by region:\")\n", "print(region_counts)\n", "\n", "# Create visualization\n", "plt.figure(figsize=(10, 6))\n", "region_counts.plot(kind='bar', color='skyblue')\n", "plt.title('Number of Properties by Region')\n", "plt.xlabel('Region')\n", "plt.ylabel('Number of Properties')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Property types by region\n", "print(\"\\nProperty types by region:\")\n", "property_region = pd.crosstab(df['region'], df['property_type'])\n", "print(property_region)"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["### Price Analysis by Region"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["# Price statistics by region\n", "price_by_region = df.groupby('region')['price_usd'].agg(['mean', 'median', 'std', 'count'])\n", "print(\"Price statistics by region:\")\n", "print(price_by_region.round(2))\n", "\n", "# Create box plot\n", "plt.figure(figsize=(12, 8))\n", "df.boxplot(column='price_usd', by='region', figsize=(12, 8))\n", "plt.title('Price Distribution by Region')\n", "plt.suptitle('')  # Remove default title\n", "plt.xlabel('Region')\n", "plt.ylabel('Price (USD)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Area statistics by region\n", "area_by_region = df.groupby('region')['area_m2'].agg(['mean', 'median', 'std'])\n", "print(\"\\nArea statistics by region:\")\n", "print(area_by_region.round(2))"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["## Geographic Visualization\n", "\n", "Let's create an interactive map to visualize the geographic distribution of properties."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["# Create interactive map with plotly\n", "fig = px.scatter_mapbox(\n", "    df,\n", "    lat=\"lat\",\n", "    lon=\"lon\",\n", "    color=\"region\",\n", "    size=\"price_usd\",\n", "    hover_data=[\"state\", \"property_type\", \"area_m2\", \"price_usd\"],\n", "    mapbox_style=\"open-street-map\",\n", "    title=\"Brazilian Real Estate Properties by Region\",\n", "    width=1000,\n", "    height=600\n", ")\n", "\n", "fig.update_layout(\n", "    mapbox=dict(\n", "        center=dict(lat=-15.7942, lon=-47.8822),  # Brazil center\n", "        zoom=4\n", "    )\n", ")\n", "\n", "fig.show()\n", "\n", "# Summary by state\n", "state_summary = df.groupby('state').agg({\n", "    'price_usd': ['mean', 'count'],\n", "    'area_m2': 'mean'\n", "}).round(2)\n", "\n", "print(\"\\nProperty summary by state:\")\n", "print(state_summary)"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["## Southern Brazil Analysis\n", "\n", "Let's focus on southern Brazil to analyze the relationship between home size and price."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["# Filter for southern Brazil\n", "south_region = df[df['region'] == 'South']\n", "\n", "print(f\"Properties in South region: {len(south_region)}\")\n", "print(f\"States in South region: {south_region['state'].unique()}\")\n", "\n", "# Basic statistics for South region\n", "print(\"\\nSouth region summary:\")\n", "print(south_region[['price_usd', 'area_m2']].describe())\n", "\n", "# Calculate correlation\n", "correlation = south_region['price_usd'].corr(south_region['area_m2'])\n", "print(f\"\\nCorrelation between price and area in South: {correlation:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "28", "metadata": {}, "outputs": [], "source": ["# Create scatter plot for South region\n", "plt.figure(figsize=(12, 8))\n", "plt.scatter(south_region['area_m2'], south_region['price_usd'], alpha=0.6, color='coral')\n", "plt.xlabel('Area (m²)')\n", "plt.ylabel('Price (USD)')\n", "plt.title('Price vs Area in Southern Brazil')\n", "\n", "# Add trend line\n", "z = np.polyfit(south_region['area_m2'], south_region['price_usd'], 1)\n", "p = np.poly1d(z)\n", "plt.plot(south_region['area_m2'], p(south_region['area_m2']), \"r--\", alpha=0.8)\n", "\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Analysis by state in South region\n", "south_by_state = south_region.groupby('state').agg({\n", "    'price_usd': ['mean', 'count'],\n", "    'area_m2': 'mean'\n", "}).round(2)\n", "\n", "print(\"\\nSouth region by state:\")\n", "print(south_by_state)"]}, {"cell_type": "markdown", "id": "29", "metadata": {}, "source": ["## Comparative Analysis Across Regions\n", "\n", "Let's compare price-size relationships across all regions."]}, {"cell_type": "code", "execution_count": null, "id": "30", "metadata": {}, "outputs": [], "source": ["# Calculate correlations by region\n", "correlations_by_region = df.groupby('region').apply(\n", "    lambda x: x['price_usd'].corr(x['area_m2'])\n", ")\n", "\n", "print(\"Correlation between price and area by region:\")\n", "print(correlations_by_region.round(3))\n", "\n", "# Create subplots for each region\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.ravel()\n", "\n", "regions = df['region'].unique()\n", "colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold', 'plum']\n", "\n", "for i, region in enumerate(regions):\n", "    region_data = df[df['region'] == region]\n", "    \n", "    axes[i].scatter(region_data['area_m2'], region_data['price_usd'], \n", "                   alpha=0.6, color=colors[i % len(colors)])\n", "    \n", "    # Add trend line\n", "    if len(region_data) > 1:\n", "        z = np.polyfit(region_data['area_m2'], region_data['price_usd'], 1)\n", "        p = np.poly1d(z)\n", "        axes[i].plot(region_data['area_m2'], p(region_data['area_m2']), \"r--\", alpha=0.8)\n", "    \n", "    axes[i].set_title(f'{region} (r={correlations_by_region[region]:.3f})')\n", "    axes[i].set_xlabel('Area (m²)')\n", "    axes[i].set_ylabel('Price (USD)')\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "# Hide unused subplot\n", "if len(regions) < len(axes):\n", "    axes[-1].set_visible(False)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["## Key Insights and Conclusions\n", "\n", "Let's summarize our findings from the Brazil housing market analysis."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["# Summary statistics table\n", "summary_table = df.groupby('region').agg({\n", "    'price_usd': ['count', 'mean', 'median'],\n", "    'area_m2': ['mean', 'median'],\n", "}).round(2)\n", "\n", "summary_table.columns = ['Properties', 'Mean_Price', 'Median_Price', 'Mean_Area', 'Median_Area']\n", "\n", "# Add price per square meter\n", "summary_table['Price_per_m2'] = (summary_table['Mean_Price'] / summary_table['Mean_Area']).round(2)\n", "\n", "print(\"Regional Market Summary:\")\n", "print(\"=\" * 80)\n", "print(summary_table)\n", "\n", "print(\"\\n\\nKey Findings:\")\n", "print(\"=\" * 40)\n", "print(f\"1. Total properties analyzed: {len(df):,}\")\n", "print(f\"2. Regions covered: {', '.join(sorted(df['region'].unique()))}\")\n", "print(f\"3. States included: {df['state'].nunique()} states\")\n", "\n", "# Find region with strongest correlation\n", "strongest_corr_region = correlations_by_region.abs().idxmax()\n", "strongest_corr_value = correlations_by_region[strongest_corr_region]\n", "\n", "print(f\"4. Strongest price-area correlation: {strongest_corr_region} (r={strongest_corr_value:.3f})\")\n", "\n", "# Price range analysis\n", "print(f\"5. Price range: ${df['price_usd'].min():,.0f} - ${df['price_usd'].max():,.0f}\")\n", "print(f\"6. Area range: {df['area_m2'].min():.0f} - {df['area_m2'].max():,.0f} m²\")\n", "\n", "# Regional differences\n", "most_expensive = summary_table['Mean_Price'].idxmax()\n", "least_expensive = summary_table['Mean_Price'].idxmin()\n", "\n", "print(f\"7. Most expensive region: {most_expensive}\")\n", "print(f\"8. Least expensive region: {least_expensive}\")"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["## Practice Exercises\n", "\n", "Try these exercises to deepen your understanding:\n", "\n", "### Exercise 1: Property Type Analysis\n", "Compare apartments vs houses across different regions. Which property type is more common in each region?\n", "\n", "### Exercise 2: State-Level Analysis\n", "Create a detailed analysis for a specific state of your choice. Include price distributions, area analysis, and geographic visualization.\n", "\n", "### Exercise 3: Market Efficiency\n", "Calculate price per square meter for each region and identify which regions offer the best value for money.\n", "\n", "### Exercise 4: Geographic Clustering\n", "Use the latitude and longitude data to identify geographic clusters of properties and analyze their characteristics.\n", "\n", "### Exercise 5: Advanced Visualization\n", "Create an interactive dashboard using plotly that allows users to filter by region, property type, and price range."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}