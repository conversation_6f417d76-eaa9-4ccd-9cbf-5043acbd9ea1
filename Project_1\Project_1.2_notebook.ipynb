{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project 1.2: Preparing Mexico Data\n", "## WorldQuant University - Applied Data Science Lab\n", "\n", "### Learning Objectives\n", "- Learn data preparation and cleaning techniques\n", "- Import and clean multiple CSV files\n", "- Handle missing values and data type issues\n", "- Split and manipulate string data\n", "- Concatenate DataFrames\n", "- Save cleaned data for analysis\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction to Data Preparation\n", "\n", "The first part of any data science project is **preparing your data**, which means making sure it's in the right place and format for you to conduct your analysis. The first step of any data preparation is importing your raw data and cleaning it.\n", "\n", "In this project, we'll work with three CSV files containing Mexico real estate data:\n", "- `mexico-real-estate-1.csv`\n", "- `mexico-real-estate-2.csv` \n", "- `mexico-real-estate-3.csv`\n", "\n", "Each file has different data quality issues that we'll need to address."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import pandas as pd\n", "import numpy as np\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Data\n", "\n", "Let's start by importing our three datasets to see what we're working with."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.1: Read CSV Files\n", "\n", "Read the three CSV files into separate DataFrames named df1, df2, and df3.\n", "\n", "**Learning Goals:**\n", "- Read CSV files into DataFrames using pandas\n", "- Understand different data structures"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load CSV files into DataFrames\n", "df1 = pd.read_csv('data/mexico-real-estate-1.csv')\n", "df2 = pd.read_csv('data/mexico-real-estate-2.csv')\n", "df3 = pd.read_csv('data/mexico-real-estate-3.csv')\n", "\n", "# Print object type and shape for DataFrames\n", "print(\"df1 type:\", type(df1))\n", "print(\"df1 shape:\", df1.shape)\n", "print()\n", "print(\"df2 type:\", type(df2))\n", "print(\"df2 shape:\", df2.shape)\n", "print()\n", "print(\"df3 type:\", type(df3))\n", "print(\"df3 shape:\", df3.shape)\n", "\n", "# Display first few rows of df1\n", "print(\"\\nFirst 5 rows of df1:\")\n", "df1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean df1\n", "\n", "Now let's inspect and clean the first DataFrame. We'll look at data types, missing values, and any formatting issues."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.2: Inspect df1\n", "\n", "Inspect df1 by looking at its shape, data types, and missing values.\n", "\n", "**Learning Goal:** Inspect a DataFrame using shape, info, and head methods in pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print df1 shape\n", "print(\"df1 shape:\", df1.shape)\n", "\n", "# Print df1 info\n", "print(\"\\ndf1 info:\")\n", "print(df1.info())\n", "\n", "# Get output of df1 head\n", "print(\"\\nFirst 5 rows of df1:\")\n", "df1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Analysis of df1:**\n", "- There are missing values (NaN) in the \"lat\" and \"lon\" columns\n", "- The \"price_usd\" column has data type 'object' instead of 'float' (due to $ and , characters)\n", "- We need to clean these issues before analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.3: Clean df1\n", "\n", "Clean df1 by:\n", "1. Dropping rows with NaN values\n", "2. Removing \"$\" and \",\" characters from \"price_usd\" \n", "3. Converting \"price_usd\" to float data type\n", "\n", "**Learning Goals:**\n", "- Drop rows with missing values from a DataFrame\n", "- Replace string characters in a column \n", "- Recast a column as a different data type"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Drop null values from df1\n", "df1.dropna(inplace=True)\n", "\n", "# Clean \"price_usd\" column in df1\n", "df1[\"price_usd\"] = (df1[\"price_usd\"]\n", "                   .str.replace(\"$\", \"\", regex=False)\n", "                   .str.replace(\",\", \"\")\n", "                   .astype(float)\n", "                  )\n", "\n", "# Print updated information\n", "print(\"df1 type:\", type(df1))\n", "print(\"df1 shape:\", df1.shape)\n", "print(\"\\nUpdated df1 info:\")\n", "print(df1.info())\n", "print(\"\\nCleaned df1 sample:\")\n", "df1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean df2\n", "\n", "Now let's tackle df2. This dataset has a different challenge: prices are in Mexican pesos instead of US dollars."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect df2\n", "print(\"df2 shape:\", df2.shape)\n", "print(\"\\ndf2 info:\")\n", "print(df2.info())\n", "print(\"\\ndf2 sample:\")\n", "df2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.4: Clean df2\n", "\n", "Clean df2 by:\n", "1. Dropping rows with NaN values\n", "2. Converting \"price_mxn\" to \"price_usd\" (1 USD = 19 MXN in 2014)\n", "3. Dropping the \"price_mxn\" column\n", "\n", "**Learning Goals:**\n", "- Create new columns derived from existing columns\n", "- Drop columns from a DataFrame\n", "- Handle currency conversion"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Drop null values from df2\n", "df2.dropna(inplace=True)\n", "\n", "# Create \"price_usd\" column for df2 (19 pesos to the dollar in 2014)\n", "df2[\"price_usd\"] = (df2[\"price_mxn\"] / 19).round(2)\n", "\n", "# Drop \"price_mxn\" column from df2\n", "df2.drop(columns=[\"price_mxn\"], inplace=True)\n", "\n", "# Print updated information\n", "print(\"df2 type:\", type(df2))\n", "print(\"df2 shape:\", df2.shape)\n", "print(\"\\nUpdated df2 info:\")\n", "print(df2.info())\n", "print(\"\\nCleaned df2 sample:\")\n", "df2.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Clean df3\n", "\n", "The third dataset has the most complex issues:\n", "- Combined \"lat-lon\" column instead of separate columns\n", "- \"place_with_parent_names\" column instead of \"state\" column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Inspect df3\n", "print(\"df3 shape:\", df3.shape)\n", "print(\"\\ndf3 info:\")\n", "print(df3.info())\n", "print(\"\\ndf3 sample:\")\n", "df3.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.5: Split Lat-Lon Column\n", "\n", "Clean df3 by:\n", "1. Dropping rows with NaN values\n", "2. Splitting \"lat-lon\" column into separate \"lat\" and \"lon\" columns\n", "\n", "**Learning Goals:**\n", "- Split strings in one column to create multiple columns\n", "- Handle complex data formatting issues"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Drop null values from df3\n", "df3.dropna(inplace=True)\n", "\n", "# Create \"lat\" and \"lon\" columns for df3\n", "df3[[\"lat\", \"lon\"]] = df3[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "\n", "# Drop the original \"lat-lon\" column\n", "df3.drop(columns=[\"lat-lon\"], inplace=True)\n", "\n", "# Print updated information\n", "print(\"df3 type:\", type(df3))\n", "print(\"df3 shape:\", df3.shape)\n", "print(\"\\nUpdated df3 info:\")\n", "print(df3.info())\n", "print(\"\\ndf3 after lat-lon split:\")\n", "df3.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.6: Extract State Information\n", "\n", "Extract the state name from \"place_with_parent_names\" column and create a \"state\" column.\n", "\n", "**Learning Goals:**\n", "- Advanced string manipulation and splitting\n", "- Extract specific parts of structured text data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's examine the structure of place_with_parent_names\n", "print(\"Sample place_with_parent_names values:\")\n", "print(df3[\"place_with_parent_names\"].head())\n", "\n", "# The state name appears after \"México|\" in each string\n", "# Let's split and extract the state\n", "print(\"\\nSplit example:\")\n", "sample_split = df3[\"place_with_parent_names\"].str.split(\"|\", expand=True)\n", "print(\"Columns after split:\")\n", "print(sample_split.head())\n", "print(\"\\nState column (index 2):\")\n", "print(sample_split[2].head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create \"state\" column for df3\n", "df3[\"state\"] = df3[\"place_with_parent_names\"].str.split(\"|\", expand=True)[2]\n", "\n", "# Drop \"place_with_parent_names\" column\n", "df3.drop(columns=[\"place_with_parent_names\"], inplace=True)\n", "\n", "# Print final information\n", "print(\"df3 type:\", type(df3))\n", "print(\"df3 shape:\", df3.shape)\n", "print(\"\\nFinal df3 info:\")\n", "print(df3.info())\n", "print(\"\\nFinal cleaned df3:\")\n", "df3.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Concatenate DataFrames\n", "\n", "Now that we have three clean DataFrames with consistent column structure, let's combine them into a single dataset."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.7: Combine All DataFrames\n", "\n", "Use pd.concat to concatenate df1, df2, df3 into a new DataFrame named df.\n", "\n", "**Learning Goal:** Concatenate multiple DataFrames using pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Concatenate df1, df2, and df3\n", "df = pd.concat([df1, df2, df3], ignore_index=True)\n", "\n", "# Print information about combined dataset\n", "print(\"df type:\", type(df))\n", "print(\"df shape:\", df.shape)\n", "print(\"\\nCombined dataset info:\")\n", "print(df.info())\n", "print(\"\\nCombined dataset sample:\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Quality Verification\n", "\n", "Let's verify our cleaned dataset and check for any remaining issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for missing values\n", "print(\"=== MISSING VALUES CHECK ===\")\n", "print(\"Missing values per column:\")\n", "print(df.isnull().sum())\n", "\n", "# Check data types\n", "print(\"\\n=== DATA TYPES CHECK ===\")\n", "print(df.dtypes)\n", "\n", "# Basic statistics\n", "print(\"\\n=== BASIC STATISTICS ===\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check unique values in categorical columns\n", "print(\"=== CATEGORICAL DATA CHECK ===\")\n", "print(f\"Property types: {df['property_type'].unique()}\")\n", "print(f\"\\nNumber of unique states: {df['state'].nunique()}\")\n", "print(\"\\nStates in dataset:\")\n", "print(df['state'].value_counts().head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Geographic coverage check\n", "print(\"=== GEOGRAPHIC COVERAGE ===\")\n", "print(f\"Latitude range: {df['lat'].min():.2f} to {df['lat'].max():.2f}\")\n", "print(f\"Longitude range: {df['lon'].min():.2f} to {df['lon'].max():.2f}\")\n", "\n", "# Price analysis\n", "print(\"\\n=== PRICE ANALYSIS ===\")\n", "print(f\"Price range: ${df['price_usd'].min():,.2f} to ${df['price_usd'].max():,.2f}\")\n", "print(f\"Average price: ${df['price_usd'].mean():,.2f}\")\n", "print(f\"Median price: ${df['price_usd'].median():,.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Clean Data\n", "\n", "Now that our data is clean and combined, let's save it for future analysis."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.2.8: Save DataFrame to CSV\n", "\n", "Save the cleaned DataFrame to a CSV file for use in exploratory data analysis.\n", "\n", "**Learning Goal:** Save a DataFrame as a CSV file using pandas"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save df as CSV file\n", "output_file = 'data/mexico-real-estate-clean.csv'\n", "df.to_csv(output_file, index=False)\n", "\n", "print(f\"✅ Clean dataset saved to: {output_file}\")\n", "print(f\"📊 Dataset contains {len(df):,} properties\")\n", "print(f\"🏛️ Across {df['state'].nunique()} states in Mexico\")\n", "print(f\"💰 Price range: ${df['price_usd'].min():,.0f} - ${df['price_usd'].max():,.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Preparation Summary\n", "\n", "Let's create a summary of our data preparation process."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a summary of what we accomplished\n", "print(\"=== DATA PREPARATION SUMMARY ===\")\n", "print()\n", "print(\"📋 ORIGINAL DATA:\")\n", "print(\"  • 3 separate CSV files with different formats\")\n", "print(\"  • Missing values in location data\")\n", "print(\"  • Inconsistent price currencies (USD vs MXN)\")\n", "print(\"  • Combined lat-lon columns\")\n", "print(\"  • Inconsistent column names\")\n", "print()\n", "print(\"🧹 CLEANING STEPS PERFORMED:\")\n", "print(\"  1. Removed rows with missing values\")\n", "print(\"  2. Cleaned price data (removed $, commas)\")\n", "print(\"  3. Converted data types (string → float)\")\n", "print(\"  4. Currency conversion (MXN → USD)\")\n", "print(\"  5. Split combined columns (lat-lon → lat, lon)\")\n", "print(\"  6. Extracted state names from location strings\")\n", "print(\"  7. Standardized column names across datasets\")\n", "print(\"  8. Combined all datasets into single DataFrame\")\n", "print()\n", "print(\"✅ FINAL CLEAN DATASET:\")\n", "print(f\"  • {len(df):,} properties\")\n", "print(f\"  • {df.shape[1]} consistent columns\")\n", "print(f\"  • {df['state'].nunique()} Mexican states represented\")\n", "print(f\"  • All prices in USD\")\n", "print(f\"  • No missing values\")\n", "print(f\"  • Ready for analysis! 🎉\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "🎉 **Congratulations!** You've successfully prepared and cleaned a complex real-world dataset!\n", "\n", "### What You've Learned:\n", "\n", "1. **Data Import Skills:**\n", "   - Reading multiple CSV files\n", "   - Handling different data formats and structures\n", "   - Understanding data types and their importance\n", "\n", "2. **Data Cleaning Techniques:**\n", "   - **Missing Value Handling:** Identifying and removing incomplete records\n", "   - **String Cleaning:** Removing unwanted characters ($, commas)\n", "   - **Data Type Conversion:** Converting strings to appropriate numeric types\n", "   - **Currency Conversion:** Standardizing financial data across currencies\n", "\n", "3. **Advanced Data Manipulation:**\n", "   - **String Splitting:** Breaking combined data into separate columns\n", "   - **Pattern Extraction:** Using structured text to extract meaningful information\n", "   - **Column Operations:** Creating, renaming, and dropping columns\n", "   - **DataFrame Concatenation:** Combining multiple datasets\n", "\n", "4. **Data Quality Assurance:**\n", "   - Systematic inspection of data structure and content\n", "   - Verification of cleaning results\n", "   - Documentation of data preparation process\n", "\n", "5. **Professional Workflow:**\n", "   - Step-by-step approach to complex data problems\n", "   - Saving intermediate and final results\n", "   - Creating reproducible data preparation pipelines\n", "\n", "### Best Practices Applied:\n", "- **Always inspect data first** before cleaning\n", "- **Handle missing values appropriately** for your analysis goals\n", "- **Standardize formats** across all datasets\n", "- **Verify results** after each cleaning step\n", "- **Document your process** for reproducibility\n", "- **Save clean data** for efficient future analysis\n", "\n", "### Next Steps:\n", "With your clean dataset, you're ready to:\n", "- Perform exploratory data analysis (EDA)\n", "- Create visualizations to understand patterns\n", "- Build statistical models\n", "- Answer business questions with data\n", "\n", "**Your data is now analysis-ready!** 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}