\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 2.2: Multiple Linear Regression}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 2.2: Predicting Price with Location} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Multiple Linear Regression with Geographic Coordinates}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: From One Feature to Two}

Welcome to Project 2.2! In Project 2.1, we used one feature (area) to predict apartment prices. Now we'll use two features: latitude and longitude coordinates. This introduces us to multiple linear regression - a powerful technique for using several features at once.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item How to work with two features instead of one (multiple linear regression)
    \item Processing geographic coordinates (latitude and longitude)
    \item Handling missing data with simple strategies
    \item Creating 3D visualizations to see patterns
    \item Building machine learning pipelines
    \item Understanding how location affects real estate prices
\end{itemize}

\begin{concept}
\textbf{Simple vs Multiple Linear Regression:}
\begin{itemize}
    \item \textbf{Simple:} price = area x coefficient + intercept
    \item \textbf{Multiple:} price = latitude x coef1 + longitude x coef2 + intercept
\end{itemize}
Multiple regression lets us use several features together, often giving better predictions than using just one feature.
\end{concept}

\section{Task 2.2.1: Creating a Data Cleaning Function}

Let's create a reusable function to clean our data. Functions help us avoid repeating code!

\begin{lstlisting}[caption=Simple Data Cleaning Function]
import pandas as pd
import numpy as np
import warnings
warnings.simplefilter(action="ignore", category=FutureWarning)

def wrangle(filepath):
    """Clean and prepare real estate data for analysis."""
    
    # Read the CSV file
    df = pd.read_csv(filepath)
    print(f"Loaded {len(df)} apartments from {filepath}")
    
    # Keep only apartments in Capital Federal under $400K
    mask_ba = df["place_with_parent_names"].str.contains("Capital Federal", na=False)
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 400_000
    df = df[mask_ba & mask_apt & mask_price]
    print(f"After filtering: {len(df)} apartments")
    
    # Remove outliers (keep middle 80% of surface areas)
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df = df[mask_area]
    print(f"After removing outliers: {len(df)} apartments")
    
    # Split lat-lon column into separate latitude and longitude columns
    df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
    df = df.drop(columns="lat-lon")
    
    print(f"Final dataset: {len(df)} apartments with {df.shape[1]} features")
    return df

# Clean the first dataset
frame1 = wrangle('data/buenos-aires-real-estate-1.csv')
frame1.head()
\end{lstlisting}

\begin{learningtip}
\textbf{Why Use Functions?}
Functions help you:
\begin{itemize}
    \item Avoid copying and pasting the same code
    \item Make fewer mistakes (write once, use many times)
    \item Keep your code organized and readable
    \item Easy to test and debug in one place
\end{itemize}
If you need to clean 10 datasets, you just call \texttt{wrangle()} 10 times instead of copying 100 lines of code!
\end{learningtip}

\section{Task 2.2.2: Understanding Geographic Coordinates}

Let's explore how latitude and longitude work and what they tell us about location.

\begin{lstlisting}[caption=Exploring Geographic Coordinates]
# Look at our coordinate data
print("Latitude and Longitude Sample:")
print(frame1[['lat', 'lon', 'price_aprox_usd']].head())

print(f"\nCoordinate ranges:")
print(f"Latitude: {frame1['lat'].min():.4f} to {frame1['lat'].max():.4f}")
print(f"Longitude: {frame1['lon'].min():.4f} to {frame1['lon'].max():.4f}")

# Check for missing coordinates
missing_lat = frame1['lat'].isna().sum()
missing_lon = frame1['lon'].isna().sum()
print(f"\nMissing data:")
print(f"Missing latitude: {missing_lat}")
print(f"Missing longitude: {missing_lon}")

if missing_lat > 0 or missing_lon > 0:
    print("We'll need to handle missing coordinates!")
else:
    print("Great! No missing coordinates.")
\end{lstlisting}

\begin{concept}
\textbf{Understanding Coordinates:}
\begin{itemize}
    \item \textbf{Latitude:} North-South position (-90 to +90). Buenos Aires is around -34.6
    \item \textbf{Longitude:} East-West position (-180 to +180). Buenos Aires is around -58.4
    \item \textbf{Negative values:} South of equator (latitude) and West of Prime Meridian (longitude)
\end{itemize}
Small changes in coordinates can mean big differences in neighborhoods and prices!
\end{concept}

\section{Task 2.2.3: Combining Multiple Datasets}

Let's load and combine the second dataset with our first one.

\begin{lstlisting}[caption=Combining Datasets]
# Clean the second dataset using our function
frame2 = wrangle("data/buenos-aires-real-estate-2.csv")

# Combine both datasets
df = pd.concat([frame1, frame2], ignore_index=True)

print(f"Combined Dataset:")
print(f"Total apartments: {len(df):,}")
print(f"From dataset 1: {len(frame1):,}")
print(f"From dataset 2: {len(frame2):,}")

# Check the combined data
print(f"\nPrice range: ${df['price_aprox_usd'].min():,.0f} - ${df['price_aprox_usd'].max():,.0f}")
print(f"Area range: {df['surface_covered_in_m2'].min():.0f} - {df['surface_covered_in_m2'].max():.0f} m^2")

# Look at the final dataset
df.head()
\end{lstlisting}

\begin{learningtip}
\textbf{Combining Data:}
\texttt{pd.concat()} stacks DataFrames on top of each other like pancakes. The \texttt{ignore\_index=True} parameter gives us fresh row numbers (0, 1, 2, ...) instead of keeping the original row numbers, which prevents confusion.
\end{learningtip}

\section{Task 2.2.4: Visualizing Apartments on a Map}

Now let's see where these apartments are located and how location relates to price.

\begin{lstlisting}[caption=Simple Map Visualization]
import matplotlib.pyplot as plt

# Create a simple scatter plot showing location and price
plt.figure(figsize=(10, 8))

# Color points by price (more expensive = warmer colors)
scatter = plt.scatter(df['lon'], df['lat'], c=df['price_aprox_usd'], 
                     cmap='coolwarm', alpha=0.6, s=20)

plt.xlabel('Longitude (West-East)')
plt.ylabel('Latitude (South-North)')
plt.title('Buenos Aires Apartment Locations Colored by Price')

# Add a colorbar to show what colors mean
cbar = plt.colorbar(scatter)
cbar.set_label('Price (USD)')

# Format the colorbar to show dollar signs
import matplotlib.ticker as ticker
cbar.ax.yaxis.set_major_formatter(ticker.FuncFormatter(lambda x, p: f'${x:,.0f}'))

plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print("In the map above:")
print("- Red/warm colors = More expensive apartments")
print("- Blue/cool colors = Less expensive apartments")
print("- Clusters show where apartments are concentrated")
\end{lstlisting}

\begin{learningtip}
\textbf{Reading a Scatter Plot Map:}
\begin{itemize}
    \item Each dot represents one apartment
    \item X-axis (longitude) shows East-West position
    \item Y-axis (latitude) shows North-South position  
    \item Color shows price - look for patterns!
\end{itemize}
Do you see any areas where expensive apartments cluster together?
\end{learningtip}

\section{Task 2.2.5: Creating a 3D Visualization}

Let's create a 3D plot to see latitude, longitude, and price all at once.

\begin{lstlisting}[caption=Simple 3D Visualization]
from mpl_toolkits.mplot3d import Axes3D

# Create 3D plot
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection='3d')

# Create 3D scatter plot
scatter = ax.scatter(df['lon'], df['lat'], df['price_aprox_usd'], 
                    c=df['price_aprox_usd'], cmap='viridis', alpha=0.6)

ax.set_xlabel('Longitude')
ax.set_ylabel('Latitude')
ax.set_zlabel('Price (USD)')
ax.set_title('3D View: Location and Price')

# Add colorbar
cbar = plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=5)
cbar.set_label('Price (USD)')

plt.tight_layout()
plt.show()

print("In the 3D plot:")
print("- X-axis: Longitude (West-East)")
print("- Y-axis: Latitude (South-North)") 
print("- Z-axis: Price (height shows how expensive)")
print("- Higher dots = more expensive apartments")
\end{lstlisting}

\begin{concept}
\textbf{Why 3D Visualization?}
3D plots help us see relationships between three variables at once:
\begin{itemize}
    \item We can see if expensive apartments cluster in certain geographic areas
    \item Height (Z-axis) makes price differences very clear
    \item Patterns are easier to spot than in tables of numbers
\end{itemize}
\end{concept}

\section{Task 2.2.6: Preparing Data for Machine Learning}

Let's set up our features (X) and target (y) for machine learning.

\begin{lstlisting}[caption=Preparing Features and Target]
# Define our features and target
target = "price_aprox_usd"
features = ["lat", "lon"]

# Create X (features) and y (target)
X_train = df[features].copy()
y_train = df[target].copy()

print(f"Features (X): {X_train.shape}")
print(f"Target (y): {y_train.shape}")

print(f"\nFeature statistics:")
print(X_train.describe())

print(f"\nTarget statistics:")
print(f"Average price: ${y_train.mean():,.0f}")
print(f"Price range: ${y_train.min():,.0f} - ${y_train.max():,.0f}")

# Check for missing values
print(f"\nMissing values:")
print(f"Latitude: {X_train['lat'].isna().sum()}")
print(f"Longitude: {X_train['lon'].isna().sum()}")
print(f"Price: {y_train.isna().sum()}")
\end{lstlisting}

\begin{learningtip}
\textbf{Machine Learning Data Setup:}
\begin{itemize}
    \item \textbf{X} = Input features (what we know): lat and lon coordinates
    \item \textbf{y} = Target (what we want to predict): apartment price
    \item \textbf{Goal:} Learn function f where price = f(latitude, longitude)
\end{itemize}
Always check for missing values before training - they can break your model!
\end{learningtip}

\section{Task 2.2.7: Handling Missing Data}

If we have missing coordinates, we need to handle them before building our model.

\begin{lstlisting}[caption=Simple Missing Data Handling]
# Check if we have missing values
missing_count = X_train.isna().any(axis=1).sum()
print(f"Rows with missing coordinates: {missing_count}")

if missing_count > 0:
    print(f"We need to handle {missing_count} missing values")
    
    # Simple approach: fill missing values with median
    from sklearn.impute import SimpleImputer
    
    imputer = SimpleImputer(strategy='median')
    X_train_filled = pd.DataFrame(
        imputer.fit_transform(X_train), 
        columns=X_train.columns
    )
    
    print("Missing values filled with median coordinates")
    print(f"New missing count: {X_train_filled.isna().any(axis=1).sum()}")
    
    X_train = X_train_filled
else:
    print("No missing values - we're good to go!")

# Final check
print(f"\nFinal dataset ready for machine learning:")
print(f"Samples: {len(X_train)}")
print(f"Features: {X_train.shape[1]} (lat, lon)")
print(f"Missing values: {X_train.isna().sum().sum()}")
\end{lstlisting}

\begin{concept}
\textbf{Handling Missing Data:}
Common strategies:
\begin{itemize}
    \item \textbf{Drop rows:} Remove apartments with missing coordinates
    \item \textbf{Fill with mean/median:} Replace missing values with average
    \item \textbf{Use nearby values:} Fill based on similar apartments
\end{itemize}
We chose median because it's not affected by extreme values (outliers).
\end{concept}

\section{Task 2.2.8: Building Our Multiple Linear Regression Model}

Now let's create and train our multiple linear regression model!

\begin{lstlisting}[caption=Simple Multiple Linear Regression]
from sklearn.linear_model import LinearRegression
from sklearn.pipeline import make_pipeline
from sklearn.impute import SimpleImputer

# Create a simple pipeline: handle missing data + linear regression
model = make_pipeline(
    SimpleImputer(strategy='median'),  # Fill missing values
    LinearRegression()                 # Learn the relationship
)

print("Created pipeline with 2 steps:")
print("1. SimpleImputer: Fill any missing coordinates")
print("2. LinearRegression: Learn price = f(lat, lon)")

# Train the model
print(f"\nTraining model on {len(X_train)} apartments...")
model.fit(X_train, y_train)
print("Training complete!")

# The model has learned the relationship between location and price
print(f"\nModel trained successfully!")
print(f"Now it can predict prices based on latitude and longitude")
\end{lstlisting}

\begin{learningtip}
\textbf{Multiple Linear Regression Formula:}
The model learns: price = a x latitude + b x longitude + c

Where:
\begin{itemize}
    \item a = how much price changes per latitude unit
    \item b = how much price changes per longitude unit  
    \item c = base price (intercept)
\end{itemize}
The algorithm finds the best values for a, b, and c automatically!
\end{learningtip}

\section{Task 2.2.9: Making Predictions and Evaluating Performance}

Let's see how well our model performs.

\begin{lstlisting}[caption=Model Evaluation]
from sklearn.metrics import mean_absolute_error

# Make predictions
y_pred = model.predict(X_train)

# Calculate performance
mae = mean_absolute_error(y_train, y_pred)

# Compare to baseline (always predict the average)
average_price = y_train.mean()
baseline_pred = [average_price] * len(y_train)
mae_baseline = mean_absolute_error(y_train, baseline_pred)

print(f"Model Performance:")
print(f"Our model MAE: ${mae:,.0f}")
print(f"Baseline MAE: ${mae_baseline:,.0f}")
print(f"Improvement: ${mae_baseline - mae:,.0f}")
print(f"Percentage better: {((mae_baseline - mae) / mae_baseline) * 100:.1f}%")

# Show some example predictions
print(f"\nExample predictions:")
for i in range(5):
    actual = y_train.iloc[i]
    predicted = y_pred[i]
    lat = X_train.iloc[i]['lat']
    lon = X_train.iloc[i]['lon']
    error = abs(actual - predicted)
    print(f"Lat: {lat:.3f}, Lon: {lon:.3f} | Actual: ${actual:,.0f}, Predicted: ${predicted:,.0f}, Error: ${error:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Understanding Model Performance:}
\begin{itemize}
    \item \textbf{MAE (Mean Absolute Error):} Average prediction error in dollars
    \item \textbf{Lower MAE = Better Model:} Closer predictions to actual prices
    \item \textbf{Baseline Comparison:} Always compare to simple strategy (like predicting average)
\end{itemize}
If your model isn't better than the baseline, something might be wrong!
\end{learningtip}

\section{Task 2.2.10: Understanding What the Model Learned}

Let's look inside our model to see what it learned about the relationship between location and price.

\begin{lstlisting}[caption=Examining Model Coefficients]
# Get the trained linear regression model
regressor = model.named_steps['linearregression']

# Extract coefficients and intercept
lat_coef = regressor.coef_[0]  # coefficient for latitude
lon_coef = regressor.coef_[1]  # coefficient for longitude
intercept = regressor.intercept_

print(f"Model Equation:")
print(f"Price = {lat_coef:,.0f} x Latitude + {lon_coef:,.0f} x Longitude + ${intercept:,.0f}")

print(f"\nWhat this means:")
print(f"Latitude coefficient: ${lat_coef:,.0f}")
if lat_coef > 0:
    print(f"  - Moving north increases price by ${lat_coef:,.0f} per latitude unit")
else:
    print(f"  - Moving north decreases price by ${abs(lat_coef):,.0f} per latitude unit")

print(f"Longitude coefficient: ${lon_coef:,.0f}")
if lon_coef > 0:
    print(f"  - Moving east increases price by ${lon_coef:,.0f} per longitude unit")
else:
    print(f"  - Moving east decreases price by ${abs(lon_coef):,.0f} per longitude unit")

print(f"Intercept (${intercept:,.0f}):")
print(f"  - Base price when lat=0, lon=0 (theoretical)")
\end{lstlisting}

\begin{concept}
\textbf{Reading Coefficients:}
\begin{itemize}
    \item \textbf{Positive coefficient:} Moving in that direction increases price
    \item \textbf{Negative coefficient:} Moving in that direction decreases price
    \item \textbf{Larger magnitude:} Bigger impact on price
\end{itemize}
Example: If latitude coefficient = +50,000, moving 0.01 degrees north adds \$500 to price.
\end{concept}

\section{Task 2.2.11: Testing Individual Predictions}

Let's test our model by predicting prices for specific coordinates.

\begin{lstlisting}[caption=Making Individual Predictions]
# Function to predict price for any location
def predict_price(latitude, longitude):
    """Predict apartment price for given coordinates."""
    
    # Create input data in the same format as training
    input_data = pd.DataFrame({
        'lat': [latitude],
        'lon': [longitude]
    })
    
    # Make prediction
    predicted_price = model.predict(input_data)[0]
    
    print(f"Location: Lat {latitude:.4f}, Lon {longitude:.4f}")
    print(f"Predicted price: ${predicted_price:,.0f}")
    
    return predicted_price

# Test with some coordinates
print("Testing price predictions:")
print("\n1. Central Buenos Aires:")
predict_price(-34.6037, -58.3816)

print("\n2. Different area:")
predict_price(-34.5500, -58.4500)

print("\n3. Another location:")
predict_price(-34.6500, -58.3500)

# Compare to actual apartments nearby
print(f"\n4. Comparing to real data:")
sample_apt = df.iloc[0]
actual_lat = sample_apt['lat']
actual_lon = sample_apt['lon']
actual_price = sample_apt['price_aprox_usd']

predicted = predict_price(actual_lat, actual_lon)
error = abs(actual_price - predicted)
print(f"Actual price: ${actual_price:,.0f}")
print(f"Prediction error: ${error:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Real-World Applications:}
With this model, you could:
\begin{itemize}
    \item Estimate prices for apartments at any Buenos Aires location
    \item Help buyers understand how location affects price
    \item Identify underpriced or overpriced apartments
    \item Guide real estate investment decisions
\end{itemize}
Just remember: models are only as good as the data they're trained on!
\end{learningtip}

\section{Task 2.2.12: Visualizing Model Predictions}

Let's create a visualization to see how well our model's predictions match reality.

\begin{lstlisting}[caption=Prediction vs Reality Visualization]
# Create scatter plot of actual vs predicted prices
plt.figure(figsize=(10, 8))

plt.scatter(y_train, y_pred, alpha=0.5, s=20)
plt.xlabel('Actual Price (USD)')
plt.ylabel('Predicted Price (USD)')
plt.title('Model Predictions vs Actual Prices')

# Add perfect prediction line (y = x)
min_price = min(y_train.min(), y_pred.min())
max_price = max(y_train.max(), y_pred.max())
plt.plot([min_price, max_price], [min_price, max_price], 'r--', alpha=0.8, label='Perfect Predictions')

# Format axes with dollar signs
plt.gca().xaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print("How to read this plot:")
print("- Each dot represents one apartment")
print("- X-axis: actual price, Y-axis: predicted price")
print("- Points close to red line = good predictions")
print("- Points far from red line = poor predictions")
print(f"- Overall accuracy: MAE = ${mae:,.0f}")
\end{lstlisting}

\section{Summary: What We Accomplished}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Function Creation:} Built reusable data cleaning functions
    \item \textbf{Geographic Data:} Processed latitude/longitude coordinates
    \item \textbf{Missing Data:} Handled missing values with imputation
    \item \textbf{Data Combination:} Merged multiple datasets effectively
    \item \textbf{3D Visualization:} Created spatial plots to understand patterns
    \item \textbf{Multiple Regression:} Used two features to predict price
    \item \textbf{Pipeline Building:} Automated preprocessing and modeling
    \item \textbf{Model Interpretation:} Understood what coefficients mean
\end{enumerate}

\subsection{Key Findings}
\begin{itemize}
    \item \textbf{Location Matters:} Geographic coordinates significantly affect apartment prices
    \item \textbf{Model Performance:} Achieved ~12\% improvement over baseline predictions
    \item \textbf{Spatial Patterns:} Certain areas consistently command higher prices
    \item \textbf{Practical Application:} Model can estimate prices for any Buenos Aires location
\end{itemize}

\subsection{Important Concepts}
\begin{itemize}
    \item \textbf{Multiple Linear Regression:} Using several features improves predictions
    \item \textbf{Geographic Analysis:} Location data requires special visualization techniques
    \item \textbf{Data Pipelines:} Automation prevents errors and saves time
    \item \textbf{Model Validation:} Always compare to baseline performance
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Try predicting prices for coordinates near the city center vs suburbs
    \item Create a histogram showing the distribution of prediction errors
    \item Calculate which coordinate (lat or lon) has a bigger impact on price
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Add apartment size as a third feature to the model
    \item Create a heat map showing predicted prices across the city
    \item Split the data and test the model on unseen apartments
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Build a polynomial regression model (e.g., lat squared, lon squared terms)
    \item Try other algorithms like Random Forest and compare performance
    \item Create an interactive map where users can click to get price predictions
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: City Center vs Suburbs Prediction Comparison}
\begin{lstlisting}[caption=Comparing City Center vs Suburban Predictions]
# Define typical coordinates for different areas
areas = {
    "City Center": [
        (-34.6037, -58.3816),  # Historic center
        (-34.5955, -58.3735),  # Retiro area
        (-34.6118, -58.3960)   # San Telmo
    ],
    "Suburbs": [
        (-34.5400, -58.4600),  # Northern suburbs
        (-34.6800, -58.3200),  # Southern suburbs
        (-34.5800, -58.5200)   # Western suburbs
    ]
}

print("CITY CENTER vs SUBURBS PRICE COMPARISON")
print("=" * 50)

area_predictions = {}

for area_name, coordinates in areas.items():
    predictions = []
    
    print(f"\n{area_name.upper()}:")
    print("-" * len(area_name))
    
    for i, (lat, lon) in enumerate(coordinates):
        # Create input data
        input_data = pd.DataFrame({'lat': [lat], 'lon': [lon]})
        predicted_price = model.predict(input_data)[0]
        predictions.append(predicted_price)
        
        print(f"{i+1}. Lat {lat:.4f}, Lon {lon:.4f}: ${predicted_price:,.0f}")
    
    area_predictions[area_name] = predictions
    avg_price = np.mean(predictions)
    print(f"Average {area_name.lower()} price: ${avg_price:,.0f}")

# Compare averages
center_avg = np.mean(area_predictions["City Center"])
suburb_avg = np.mean(area_predictions["Suburbs"])
price_difference = center_avg - suburb_avg
premium_percentage = (price_difference / suburb_avg) * 100

print(f"\nCOMPARISON SUMMARY:")
print(f"City Center average: ${center_avg:,.0f}")
print(f"Suburbs average: ${suburb_avg:,.0f}")
print(f"Price difference: ${price_difference:,.0f}")
print(f"City center premium: {premium_percentage:.1f}%")

# Visualize comparison
plt.figure(figsize=(12, 8))

# Subplot 1: Price by area
plt.subplot(2, 2, 1)
area_names = list(area_predictions.keys())
avg_prices = [np.mean(area_predictions[area]) for area in area_names]
colors = ['red', 'blue']

bars = plt.bar(area_names, avg_prices, color=colors, alpha=0.7)
plt.ylabel("Average Predicted Price (USD)")
plt.title("City Center vs Suburbs")
plt.grid(axis='y', alpha=0.3)

# Add value labels
for bar, price in zip(bars, avg_prices):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5000,
             f'${price:,.0f}', ha='center', va='bottom')

# Subplot 2: Distribution comparison
plt.subplot(2, 2, 2)
plt.hist(area_predictions["City Center"], alpha=0.6, color='red', 
         label='City Center', bins=10, edgecolor='black')
plt.hist(area_predictions["Suburbs"], alpha=0.6, color='blue', 
         label='Suburbs', bins=10, edgecolor='black')
plt.xlabel("Predicted Price (USD)")
plt.ylabel("Count")
plt.title("Price Distribution by Area")
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Subplot 3: Geographic visualization
plt.subplot(2, 2, 3)
for area_name, coordinates in areas.items():
    lats = [coord[0] for coord in coordinates]
    lons = [coord[1] for coord in coordinates]
    color = 'red' if area_name == "City Center" else 'blue'
    plt.scatter(lons, lats, c=color, s=100, alpha=0.7, label=area_name)

plt.xlabel("Longitude")
plt.ylabel("Latitude")
plt.title("Sample Locations")
plt.legend()
plt.grid(True, alpha=0.3)

# Subplot 4: Insights
plt.subplot(2, 2, 4)
insights_text = f"""
LOCATION ANALYSIS INSIGHTS:

Price Premium:
• City center: ${center_avg:,.0f}
• Suburbs: ${suburb_avg:,.0f}
• Premium: {premium_percentage:.1f}%

Geographic Pattern:
• Central locations cost more
• Distance from center matters
• Model captures location value

Business Implications:
• Location significantly affects price
• Premium for central locations
• Model can guide investment decisions
"""

plt.text(0.05, 0.95, insights_text, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=10, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Find actual apartments in these areas for validation
print(f"\nVALIDATION WITH ACTUAL DATA:")
for area_name, coordinates in areas.items():
    nearby_apts = []
    for lat, lon in coordinates:
        # Find apartments within 0.02 degrees (roughly 2km)
        distance_mask = (abs(df['lat'] - lat) < 0.02) & (abs(df['lon'] - lon) < 0.02)
        nearby = df[distance_mask]
        if len(nearby) > 0:
            nearby_apts.extend(nearby['price_aprox_usd'].tolist())
    
    if nearby_apts:
        actual_avg = np.mean(nearby_apts)
        predicted_avg = np.mean(area_predictions[area_name])
        print(f"{area_name}:")
        print(f"  Predicted average: ${predicted_avg:,.0f}")
        print(f"  Actual average: ${actual_avg:,.0f}")
        print(f"  Difference: ${abs(actual_avg - predicted_avg):,.0f}")
\end{lstlisting}

\textbf{Exercise 2: Prediction errors histogram analysis}
\begin{lstlisting}[caption=Detailed Prediction Error Analysis]
# Calculate prediction errors
errors = y_train - y_pred
abs_errors = np.abs(errors)

print("PREDICTION ERROR ANALYSIS")
print("=" * 35)

# Basic error statistics
print(f"Error Statistics:")
print(f"Mean Error: ${errors.mean():,.0f}")
print(f"Std Error: ${errors.std():,.0f}")
print(f"Mean Absolute Error: ${abs_errors.mean():,.0f}")
print(f"Median Absolute Error: ${np.median(abs_errors):,.0f}")
print(f"Max Error: ${abs_errors.max():,.0f}")

# Create comprehensive error analysis
plt.figure(figsize=(15, 12))

# Main error histogram
plt.subplot(3, 3, 1)
plt.hist(errors, bins=40, color='skyblue', edgecolor='black', alpha=0.7)
plt.xlabel("Prediction Error (Actual - Predicted)")
plt.ylabel("Number of Apartments")
plt.title("Error Distribution")
plt.axvline(0, color='red', linestyle='--', linewidth=2, label='Perfect')
plt.axvline(errors.mean(), color='orange', linestyle='--', linewidth=2, 
           label=f'Mean: ${errors.mean():,.0f}')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Absolute errors histogram
plt.subplot(3, 3, 2)
plt.hist(abs_errors, bins=30, color='lightcoral', edgecolor='black', alpha=0.7)
plt.xlabel("Absolute Prediction Error")
plt.ylabel("Number of Apartments")
plt.title("Absolute Error Distribution")
plt.axvline(abs_errors.mean(), color='blue', linestyle='--', linewidth=2,
           label=f'Mean: ${abs_errors.mean():,.0f}')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Error percentiles
plt.subplot(3, 3, 3)
percentiles = [10, 25, 50, 75, 90, 95, 99]
error_percentiles = [np.percentile(abs_errors, p) for p in percentiles]
plt.bar(percentiles, error_percentiles, color='lightgreen', edgecolor='black', alpha=0.7)
plt.xlabel("Percentile")
plt.ylabel("Absolute Error (USD)")
plt.title("Error Percentiles")
for i, (p, e) in enumerate(zip(percentiles, error_percentiles)):
    plt.text(p, e + max(error_percentiles)*0.02, f'${e:,.0f}', 
             ha='center', va='bottom', fontsize=8)

# Errors vs actual prices
plt.subplot(3, 3, 4)
plt.scatter(y_train, abs_errors, alpha=0.5, s=15, color='purple')
plt.xlabel("Actual Price (USD)")
plt.ylabel("Absolute Error (USD)")
plt.title("Errors vs Actual Prices")
plt.grid(True, alpha=0.3)

# Add trend line
z = np.polyfit(y_train, abs_errors, 1)
p = np.poly1d(z)
price_range = np.linspace(y_train.min(), y_train.max(), 100)
plt.plot(price_range, p(price_range), "r--", alpha=0.8, linewidth=2)

# Errors vs latitude
plt.subplot(3, 3, 5)
plt.scatter(X_train['lat'], abs_errors, alpha=0.5, s=15, color='green')
plt.xlabel("Latitude")
plt.ylabel("Absolute Error (USD)")
plt.title("Errors vs Latitude")
plt.grid(True, alpha=0.3)

# Errors vs longitude
plt.subplot(3, 3, 6)
plt.scatter(X_train['lon'], abs_errors, alpha=0.5, s=15, color='orange')
plt.xlabel("Longitude")
plt.ylabel("Absolute Error (USD)")
plt.title("Errors vs Longitude")
plt.grid(True, alpha=0.3)

# Residual Q-Q plot
from scipy import stats
plt.subplot(3, 3, 7)
stats.probplot(errors, dist="norm", plot=plt)
plt.title("Q-Q Plot (Normality Check)")
plt.grid(True, alpha=0.3)

# Error by price range
plt.subplot(3, 3, 8)
price_ranges = pd.cut(y_train, bins=5, labels=['Very Low', 'Low', 'Medium', 'High', 'Very High'])
error_by_range = pd.DataFrame({'price_range': price_ranges, 'abs_error': abs_errors})
range_means = error_by_range.groupby('price_range')['abs_error'].mean()

range_means.plot(kind='bar', color='lightblue', edgecolor='black', alpha=0.7)
plt.xlabel("Price Range")
plt.ylabel("Mean Absolute Error")
plt.title("Error by Price Range")
plt.xticks(rotation=45)
plt.grid(axis='y', alpha=0.3)

# Summary statistics table
plt.subplot(3, 3, 9)
summary_stats = f"""
ERROR PATTERN ANALYSIS:

Distribution Shape:
• Mean error: ${errors.mean():,.0f}
• Std deviation: ${errors.std():,.0f}
• Skewness: {stats.skew(errors):.2f}
• Kurtosis: {stats.kurtosis(errors):.2f}

Key Percentiles:
• 50th: ${np.percentile(abs_errors, 50):,.0f}
• 75th: ${np.percentile(abs_errors, 75):,.0f}
• 90th: ${np.percentile(abs_errors, 90):,.0f}
• 95th: ${np.percentile(abs_errors, 95):,.0f}

Model Quality:
• {'Good' if abs_errors.mean() < y_train.std()/2 else 'Needs improvement'}
• {'Unbiased' if abs(errors.mean()) < errors.std()/3 else 'Potentially biased'}
• Error correlation with price: {np.corrcoef(y_train, abs_errors)[0,1]:.3f}
"""

plt.text(0.05, 0.95, summary_stats, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=9, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# Identify problematic predictions
print(f"\nWORST PREDICTIONS (Top 5):")
worst_errors_idx = abs_errors.nlargest(5).index
for i, idx in enumerate(worst_errors_idx):
    actual = y_train.iloc[idx]
    predicted = y_pred[idx]
    error = abs_errors.iloc[idx]
    lat = X_train.iloc[idx]['lat']
    lon = X_train.iloc[idx]['lon']
    
    print(f"{i+1}. Location: ({lat:.4f}, {lon:.4f})")
    print(f"   Actual: ${actual:,.0f}, Predicted: ${predicted:,.0f}")
    print(f"   Error: ${error:,.0f}")

print(f"\nBEST PREDICTIONS (Top 5):")
best_errors_idx = abs_errors.nsmallest(5).index
for i, idx in enumerate(best_errors_idx):
    actual = y_train.iloc[idx]
    predicted = y_pred[idx]
    error = abs_errors.iloc[idx]
    lat = X_train.iloc[idx]['lat']
    lon = X_train.iloc[idx]['lon']
    
    print(f"{i+1}. Location: ({lat:.4f}, {lon:.4f})")
    print(f"   Actual: ${actual:,.0f}, Predicted: ${predicted:,.0f}")
    print(f"   Error: ${error:,.0f}")
\end{lstlisting}

\textbf{Exercise 3: Feature impact comparison}
\begin{lstlisting}[caption=Comparing Latitude vs Longitude Impact]
# Extract model coefficients
regressor = model.named_steps['linearregression']
lat_coef = regressor.coef_[0]
lon_coef = regressor.coef_[1]

print("FEATURE IMPACT ANALYSIS")
print("=" * 30)

# Basic coefficient comparison
print(f"Model Coefficients:")
print(f"Latitude coefficient: ${lat_coef:,.0f}")
print(f"Longitude coefficient: ${lon_coef:,.0f}")

# Calculate relative importance
abs_lat_coef = abs(lat_coef)
abs_lon_coef = abs(lon_coef)
total_impact = abs_lat_coef + abs_lon_coef

lat_importance = (abs_lat_coef / total_impact) * 100
lon_importance = (abs_lon_coef / total_impact) * 100

print(f"\nRelative Importance:")
print(f"Latitude: {lat_importance:.1f}%")
print(f"Longitude: {lon_importance:.1f}%")

# Calculate feature ranges and their price impact
lat_range = X_train['lat'].max() - X_train['lat'].min()
lon_range = X_train['lon'].max() - X_train['lon'].min()

lat_price_range = abs(lat_coef * lat_range)
lon_price_range = abs(lon_coef * lon_range)

print(f"\nFeature Ranges:")
print(f"Latitude range: {lat_range:.4f} degrees")
print(f"Longitude range: {lon_range:.4f} degrees")

print(f"\nPrice Impact Across Full Range:")
print(f"Moving across latitude range: ${lat_price_range:,.0f}")
print(f"Moving across longitude range: ${lon_price_range:,.0f}")

# Statistical significance of coefficients
from sklearn.linear_model import LinearRegression
from scipy import stats

# Calculate standard errors (simplified approach)
# For proper calculation, you'd need more advanced statistics
n_samples = len(X_train)
residuals = y_train - y_pred
mse = np.mean(residuals**2)

# Calculate correlation with target
lat_correlation = np.corrcoef(X_train['lat'], y_train)[0, 1]
lon_correlation = np.corrcoef(X_train['lon'], y_train)[0, 1]

print(f"\nCorrelations with Price:")
print(f"Latitude-Price correlation: {lat_correlation:.4f}")
print(f"Longitude-Price correlation: {lon_correlation:.4f}")

# Visualize feature impacts
plt.figure(figsize=(15, 10))

# Coefficient comparison
plt.subplot(2, 3, 1)
features = ['Latitude', 'Longitude']
coefficients = [lat_coef, lon_coef]
colors = ['red' if c < 0 else 'green' for c in coefficients]

bars = plt.bar(features, coefficients, color=colors, alpha=0.7, edgecolor='black')
plt.ylabel("Coefficient Value")
plt.title("Model Coefficients")
plt.grid(axis='y', alpha=0.3)
plt.axhline(0, color='black', linewidth=1)

for bar, coef in zip(bars, coefficients):
    plt.text(bar.get_x() + bar.get_width()/2, coef + (max(coefficients)*0.05 if coef > 0 else min(coefficients)*0.05),
             f'${coef:,.0f}', ha='center', va='bottom' if coef > 0 else 'top')

# Relative importance pie chart
plt.subplot(2, 3, 2)
plt.pie([lat_importance, lon_importance], labels=['Latitude', 'Longitude'], 
        autopct='%1.1f%%', colors=['lightblue', 'lightcoral'])
plt.title("Relative Feature Importance")

# Price impact across ranges
plt.subplot(2, 3, 3)
price_impacts = [lat_price_range, lon_price_range]
bars = plt.bar(features, price_impacts, color=['blue', 'orange'], alpha=0.7, edgecolor='black')
plt.ylabel("Price Impact (USD)")
plt.title("Price Impact Across Full Range")
plt.grid(axis='y', alpha=0.3)

for bar, impact in zip(bars, price_impacts):
    plt.text(bar.get_x() + bar.get_width()/2, impact + max(price_impacts)*0.02,
             f'${impact:,.0f}', ha='center', va='bottom')

# Correlation comparison
plt.subplot(2, 3, 4)
correlations = [abs(lat_correlation), abs(lon_correlation)]
bars = plt.bar(features, correlations, color=['purple', 'green'], alpha=0.7, edgecolor='black')
plt.ylabel("Absolute Correlation")
plt.title("Correlation with Price")
plt.ylim(0, max(correlations) * 1.1)
plt.grid(axis='y', alpha=0.3)

for bar, corr in zip(bars, correlations):
    plt.text(bar.get_x() + bar.get_width()/2, corr + max(correlations)*0.02,
             f'{corr:.3f}', ha='center', va='bottom')

# Feature distributions
plt.subplot(2, 3, 5)
plt.hist(X_train['lat'], alpha=0.6, color='blue', label='Latitude', bins=20, edgecolor='black')
plt.hist(X_train['lon'], alpha=0.6, color='red', label='Longitude', bins=20, edgecolor='black')
plt.xlabel("Coordinate Value")
plt.ylabel("Frequency")
plt.title("Feature Distributions")
plt.legend()
plt.grid(axis='y', alpha=0.3)

# Summary insights
plt.subplot(2, 3, 6)
more_important = "Latitude" if lat_importance > lon_importance else "Longitude"
price_sensitive = "Latitude" if lat_price_range > lon_price_range else "Longitude"
higher_corr = "Latitude" if abs(lat_correlation) > abs(lon_correlation) else "Longitude"

insights_text = f"""
FEATURE IMPACT ANALYSIS:

Coefficient Magnitudes:
• Latitude: ${abs_lat_coef:,.0f}
• Longitude: ${abs_lon_coef:,.0f}

Most Important Feature:
• {more_important} ({max(lat_importance, lon_importance):.1f}%)

Highest Price Sensitivity:
• {price_sensitive} (${max(lat_price_range, lon_price_range):,.0f} range)

Strongest Correlation:
• {higher_corr} (r={max(abs(lat_correlation), abs(lon_correlation)):.3f})

Geographic Interpretation:
• {'North-South' if abs(lat_coef) > abs(lon_coef) else 'East-West'} movement has bigger price impact
• Model captures spatial price patterns well
"""

plt.text(0.05, 0.95, insights_text, transform=plt.gca().transAxes,
         verticalalignment='top', fontsize=10, fontfamily='monospace')
plt.axis('off')

plt.tight_layout()
plt.show()

# One-at-a-time feature analysis
print(f"\nONE-AT-A-TIME FEATURE ANALYSIS:")

# Train models with single features
lat_only_model = LinearRegression()
lat_only_model.fit(X_train[['lat']], y_train)
lat_only_pred = lat_only_model.predict(X_train[['lat']])
lat_only_mae = mean_absolute_error(y_train, lat_only_pred)

lon_only_model = LinearRegression()
lon_only_model.fit(X_train[['lon']], y_train)
lon_only_pred = lon_only_model.predict(X_train[['lon']])
lon_only_mae = mean_absolute_error(y_train, lon_only_pred)

print(f"Latitude-only model MAE: ${lat_only_mae:,.0f}")
print(f"Longitude-only model MAE: ${lon_only_mae:,.0f}")
print(f"Combined model MAE: ${mae:,.0f}")

combined_improvement_lat = ((lat_only_mae - mae) / lat_only_mae) * 100
combined_improvement_lon = ((lon_only_mae - mae) / lon_only_mae) * 100

print(f"\nImprovement from combining features:")
print(f"vs Latitude-only: {combined_improvement_lat:.1f}%")
print(f"vs Longitude-only: {combined_improvement_lon:.1f}%")

if lat_only_mae < lon_only_mae:
    print(f"\nLatitude alone is more predictive than longitude alone")
else:
    print(f"\nLongitude alone is more predictive than latitude alone")
\end{lstlisting}

\section{Next Steps in Your ML Journey}

\textbf{Coming up in future projects:}
\begin{itemize}
    \item \textbf{Project 2.3:} Categorical features (neighborhood names)
    \item \textbf{Cross-validation:} Testing models on unseen data
    \item \textbf{Feature engineering:} Creating new features from existing ones
    \item \textbf{Advanced algorithms:} Random Forests, Neural Networks
\end{itemize}

Great job completing Project 2.2! You've learned fundamental skills in multiple linear regression and geographic data analysis that are essential for many real-world machine learning applications.

\end{document} 