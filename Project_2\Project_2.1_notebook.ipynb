{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Project 2.1: Predicting <PERSON> with <PERSON><PERSON> 🏠\n", "\n", "## Learning Objectives\n", "By the end of this lesson, you will be able to:\n", "- Build data import and cleaning functions for reproducibility\n", "- Apply statistical filtering techniques to remove outliers\n", "- Create and interpret scatter plots for correlation analysis\n", "- Split data into features and targets for machine learning\n", "- Build and train linear regression models using scikit-learn\n", "- Evaluate model performance using various metrics\n", "- Make predictions on new data\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "from sklearn.utils.validation import check_is_fitted\n", "import seaborn as sns\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "\n", "# Set visualization style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Data Preparation Function\n", "\n", "We'll create a reusable function to import and clean our real estate data. This ensures consistency and reproducibility."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["# Task 2.1.1: Write a wrangle function\n", "def wrangle(filepath):\n", "    \"\"\"\n", "    Import and clean real estate data from Buenos Aires.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filepath : str\n", "        Path to the CSV file to import\n", "        \n", "    Returns:\n", "    --------\n", "    pd.DataFrame\n", "        Cleaned DataFrame with Buenos Aires apartments under $400,000\n", "    \"\"\"\n", "    # Import data\n", "    df = pd.read_csv(filepath)\n", "    \n", "    # Create masks for filtering\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_cost = df[\"price_aprox_usd\"] < 400_000\n", "    \n", "    # Apply initial filters\n", "    df = df[mask_ba & mask_apt & mask_cost]\n", "    \n", "    # Remove outliers by surface area (keep 80% of data between 10th and 90th percentiles)\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "    \n", "    return df\n", "\n", "# Test the function\n", "print(\"Wrangle function created successfully!\")\n", "print(\"Function will filter for:\")\n", "print(\"- Buenos Aires apartments (Capital Federal)\")\n", "print(\"- Price < $400,000 USD\")\n", "print(\"- Surface area between 10th and 90th percentiles\")"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# Task 2.1.2: Use wrangle function to create DataFrame\n", "df = wrangle(\"data/buenos-aires-real-estate-1.csv\")\n", "\n", "print(f\"DataFrame shape: {df.shape}\")\n", "print(f\"\\nColumns: {list(df.columns)}\")\n", "\n", "# Display first few rows\n", "print(\"\\nFirst 5 rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# Verify our filtering worked correctly\n", "print(\"Data validation:\")\n", "print(f\"All properties in Capital Federal: {df['place_with_parent_names'].str.contains('Capital Federal').all()}\")\n", "print(f\"All apartments: {(df['property_type'] == 'apartment').all()}\")\n", "print(f\"All prices under $400k: {(df['price_aprox_usd'] < 400000).all()}\")\n", "\n", "print(f\"\\nPrice range: ${df['price_aprox_usd'].min():.0f} - ${df['price_aprox_usd'].max():.0f}\")\n", "print(f\"Area range: {df['surface_covered_in_m2'].min():.0f} - {df['surface_covered_in_m2'].max():.0f} m²\")\n", "\n", "# Check for missing values\n", "print(f\"\\nMissing values in key columns:\")\n", "print(f\"Price: {df['price_aprox_usd'].isnull().sum()}\")\n", "print(f\"Area: {df['surface_covered_in_m2'].isnull().sum()}\")"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Exploratory Data Analysis\n", "\n", "### Distribution of Apartment Sizes"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# Task 2.1.4: Create histogram of apartment sizes\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Create histogram\n", "df[\"surface_covered_in_m2\"].hist(bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Apartment Sizes\")\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Add summary statistics as text\n", "mean_area = df[\"surface_covered_in_m2\"].mean()\n", "median_area = df[\"surface_covered_in_m2\"].median()\n", "plt.axvline(mean_area, color='red', linestyle='--', label=f'Mean: {mean_area:.1f} m²')\n", "plt.axvline(median_area, color='orange', linestyle='--', label=f'Median: {median_area:.1f} m²')\n", "plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"Distribution looks much better after removing outliers!\")"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# Task 2.1.5: Calculate summary statistics\n", "print(\"Summary statistics for apartment area:\")\n", "area_stats = df[\"surface_covered_in_m2\"].describe()\n", "print(area_stats)\n", "\n", "print(\"\\nSummary statistics for apartment price:\")\n", "price_stats = df[\"price_aprox_usd\"].describe()\n", "print(price_stats)\n", "\n", "# Check data quality\n", "print(f\"\\nData quality check:\")\n", "print(f\"Area standard deviation: {area_stats['std']:.2f} m²\")\n", "print(f\"Area coefficient of variation: {(area_stats['std']/area_stats['mean'])*100:.1f}%\")\n", "print(f\"\\nThis shows much more reasonable variation after outlier removal.\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### Price vs Area Relationship"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# Task 2.1.7: Create scatter plot of price vs area\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Create scatter plot\n", "plt.scatter(df[\"surface_covered_in_m2\"], df[\"price_aprox_usd\"], \n", "           alpha=0.6, color='coral', s=30)\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price [USD]\")\n", "plt.title(\"Relationship between Apartment Size and Price\")\n", "\n", "# Add trend line\n", "z = np.polyfit(df[\"surface_covered_in_m2\"], df[\"price_aprox_usd\"], 1)\n", "p = np.poly1d(z)\n", "plt.plot(df[\"surface_covered_in_m2\"], p(df[\"surface_covered_in_m2\"]), \n", "         \"r--\", alpha=0.8, linewidth=2, label='Trend line')\n", "\n", "# Calculate and display correlation\n", "correlation = df[\"surface_covered_in_m2\"].corr(df[\"price_aprox_usd\"])\n", "plt.text(0.05, 0.95, f'Correlation: {correlation:.3f}', \n", "         transform=plt.gca().transAxes, fontsize=12, \n", "         bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"The correlation of {correlation:.3f} suggests a moderate positive relationship.\")\n", "print(\"This indicates that apartment size is a useful predictor of price.\")"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Feature and Target Preparation\n", "\n", "### Creating Feature Matrix and Target Vector"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# Task 2.1.8: Create feature matrix X_train\n", "features = [\"surface_covered_in_m2\"]\n", "X_train = df[features]\n", "\n", "print(f\"Feature matrix shape: {X_train.shape}\")\n", "print(f\"Features used: {features}\")\n", "print(\"\\nFirst 5 rows of feature matrix:\")\n", "print(X_train.head())\n", "\n", "# Verify it's 2D\n", "print(f\"\\nIs 2D: {X_train.ndim == 2}\")\n", "print(f\"Type: {type(X_train)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# Task 2.1.9: Create target vector y_train\n", "target = \"price_aprox_usd\"\n", "y_train = df[target]\n", "\n", "print(f\"Target vector shape: {y_train.shape}\")\n", "print(f\"Target variable: {target}\")\n", "print(\"\\nFirst 5 values of target vector:\")\n", "print(y_train.head())\n", "\n", "# Verify it's 1D\n", "print(f\"\\nIs 1D: {y_train.ndim == 1}\")\n", "print(f\"Type: {type(y_train)}\")\n", "\n", "# Check shapes match\n", "print(f\"\\nFeature matrix and target vector have same number of rows: {X_train.shape[0] == y_train.shape[0]}\")"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## Model Building and Training\n", "\n", "### Linear Regression Model"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# Task 2.1.10: Instantiate and train Linear Regression model\n", "model = LinearRegression()\n", "\n", "print(f\"Model created: {type(model).__name__}\")\n", "print(f\"Model parameters before training: {model.get_params()}\")\n", "\n", "# Train the model\n", "model.fit(X_train, y_train)\n", "\n", "print(\"\\nModel training completed!\")\n", "print(f\"Model is fitted: {check_is_fitted(model) is None}\")\n", "\n", "# Display model coefficients\n", "print(f\"\\nModel coefficients:\")\n", "print(f\"Intercept (β₀): ${model.intercept_:,.2f}\")\n", "print(f\"Slope (β₁): ${model.coef_[0]:,.2f} per m²\")\n", "\n", "# Interpret the model\n", "print(f\"\\nModel interpretation:\")\n", "print(f\"- Base price (for 0 m²): ${model.intercept_:,.2f}\")\n", "print(f\"- Price increase per additional m²: ${model.coef_[0]:,.2f}\")"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Model Evaluation\n", "\n", "### Making Predictions and Calculating Metrics"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["# Make predictions on training data\n", "y_pred_train = model.predict(X_train)\n", "\n", "print(f\"Predictions shape: {y_pred_train.shape}\")\n", "print(\"\\nFirst 5 predictions vs actual:\")\n", "comparison_df = pd.DataFrame({\n", "    'Actual': y_train.head(),\n", "    'Predicted': y_pred_train[:5],\n", "    'Difference': y_train.head().values - y_pred_train[:5]\n", "})\n", "print(comparison_df)\n", "\n", "# Calculate evaluation metrics\n", "mae = mean_absolute_error(y_train, y_pred_train)\n", "mse = mean_squared_error(y_train, y_pred_train)\n", "rmse = np.sqrt(mse)\n", "r2 = r2_score(y_train, y_pred_train)\n", "\n", "print(f\"\\nModel Performance Metrics:\")\n", "print(f\"Mean Absolute Error (MAE): ${mae:,.2f}\")\n", "print(f\"Root Mean Square Error (RMSE): ${rmse:,.2f}\")\n", "print(f\"R² Score: {r2:.4f}\")\n", "print(f\"\\nR² interpretation: {r2*100:.2f}% of price variation is explained by apartment size\")"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# Visualize predictions vs actual values\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Plot 1: Actual vs Predicted\n", "plt.subplot(1, 3, 1)\n", "plt.scatter(y_train, y_pred_train, alpha=0.6, color='purple')\n", "plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Price (USD)')\n", "plt.ylabel('Predicted Price (USD)')\n", "plt.title('Actual vs Predicted Prices')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Plot 2: Residuals\n", "plt.subplot(1, 3, 2)\n", "residuals = y_train - y_pred_train\n", "plt.scatter(y_pred_train, residuals, alpha=0.6, color='orange')\n", "plt.axhline(y=0, color='red', linestyle='--')\n", "plt.xlabel('Predicted Price (USD)')\n", "plt.ylabel('Residuals (USD)')\n", "plt.title('Residual Plot')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Plot 3: Model with data\n", "plt.subplot(1, 3, 3)\n", "plt.scatter(X_train, y_train, alpha=0.6, color='lightcoral', label='Actual')\n", "plt.plot(X_train, y_pred_train, color='blue', linewidth=2, label='Model')\n", "plt.xlabel('Area (m²)')\n", "plt.ylabel('Price (USD)')\n", "plt.title('Linear Regression Model')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["## Making Predictions for New Data\n", "\n", "### Practical Examples"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["# Create examples for prediction\n", "example_areas = [30, 50, 70, 100, 120]\n", "X_new = pd.DataFrame({'surface_covered_in_m2': example_areas})\n", "\n", "# Make predictions\n", "predictions = model.predict(X_new)\n", "\n", "print(\"Price predictions for different apartment sizes:\")\n", "print(\"=\" * 50)\n", "for area, price in zip(example_areas, predictions):\n", "    print(f\"{area:3d} m² → ${price:,.0f}\")\n", "\n", "# Calculate price per square meter for each example\n", "print(\"\\nPrice per square meter:\")\n", "print(\"=\" * 30)\n", "for area, price in zip(example_areas, predictions):\n", "    price_per_m2 = price / area\n", "    print(f\"{area:3d} m² → ${price_per_m2:,.0f}/m²\")\n", "\n", "# Compare with actual data statistics\n", "actual_price_per_m2 = (df['price_aprox_usd'] / df['surface_covered_in_m2']).mean()\n", "print(f\"\\nAverage price per m² in training data: ${actual_price_per_m2:,.0f}/m²\")"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["## Model Analysis and Insights\n", "\n", "### Understanding Model Performance"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["# Analyze model performance in detail\n", "print(\"Detailed Model Analysis\")\n", "print(\"=\" * 40)\n", "\n", "# Model equation\n", "print(f\"Linear Regression Equation:\")\n", "print(f\"Price = {model.intercept_:,.0f} + {model.coef_[0]:,.0f} × Area\")\n", "print()\n", "\n", "# Performance summary\n", "print(f\"Performance Summary:\")\n", "print(f\"• R² Score: {r2:.4f} ({r2*100:.2f}% variance explained)\")\n", "print(f\"• Average error: ${mae:,.0f} (MAE)\")\n", "print(f\"• RMSE: ${rmse:,.0f}\")\n", "print()\n", "\n", "# Error analysis\n", "error_percentage = (mae / y_train.mean()) * 100\n", "print(f\"Error Analysis:\")\n", "print(f\"• Mean price: ${y_train.mean():,.0f}\")\n", "print(f\"• Average error as % of mean price: {error_percentage:.1f}%\")\n", "print()\n", "\n", "# Model limitations\n", "print(f\"Model Limitations:\")\n", "print(f\"• Only uses apartment size as predictor\")\n", "print(f\"• Assumes linear relationship\")\n", "print(f\"• Doesn't account for location, condition, amenities, etc.\")\n", "print(f\"• Based on Buenos Aires market only\")"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["# Residual analysis\n", "residuals = y_train - y_pred_train\n", "\n", "print(\"Residual Analysis:\")\n", "print(\"=\" * 30)\n", "print(f\"Residual statistics:\")\n", "print(residuals.describe())\n", "\n", "# Check for patterns in residuals\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(residuals, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "plt.xlabel('Residuals (USD)')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Residuals')\n", "plt.axvline(0, color='red', linestyle='--', label='Perfect prediction')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.scatter(X_train['surface_covered_in_m2'], residuals, alpha=0.6, color='teal')\n", "plt.axhline(0, color='red', linestyle='--', label='Perfect prediction')\n", "plt.xlabel('Area (m²)')\n", "plt.ylabel('Residuals (USD)')\n", "plt.title('Residuals vs Area')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Check if residuals are roughly normally distributed\n", "residual_std = residuals.std()\n", "residuals_in_1std = ((residuals.abs() <= residual_std).sum() / len(residuals)) * 100\n", "print(f\"\\nResiduals within 1 standard deviation: {residuals_in_1std:.1f}%\")\n", "print(f\"(Normal distribution would have ~68% within 1 std)\")"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## Practice Exercises\n", "\n", "Try these exercises to deepen your understanding:\n", "\n", "### Exercise 1: Different Outlier Thresholds\n", "Modify the wrangle function to use different quantile thresholds (e.g., 0.05-0.95 or 0.25-0.75) and compare model performance.\n", "\n", "### Exercise 2: Feature Engineering\n", "Create a new feature 'price_per_m2' and analyze its distribution. How does it vary across different apartment sizes?\n", "\n", "### Exercise 3: Model Validation\n", "Import a second CSV file, apply the same wrangle function, and test your model's performance on this new data.\n", "\n", "### Exercise 4: Alternative Models\n", "Try fitting a polynomial regression (degree 2) to the same data and compare performance with linear regression.\n", "\n", "### Exercise 5: Prediction Intervals\n", "Calculate prediction intervals for your model and create visualizations showing the uncertainty in predictions."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}