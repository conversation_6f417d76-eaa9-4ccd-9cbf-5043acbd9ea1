Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
3.1. Wrangling Data with MongoDB 🇰🇪

from pprint import PrettyPrinter
​
import pandas as pd
from IPython.display import VimeoVideo
from pymongo import MongoClient
VimeoVideo("665412094", h="8334dfab2e", width=600)
VimeoVideo("665412135", h="dcff7ab83a", width=600)
Task 3.1.1: Instantiate a PrettyPrinter, and assign it to the variable pp.

Construct a PrettyPrinter instance in pprint.
pp = PrettyPrinter(indent = 2)
Prepare Data
Connect
VimeoVideo("665412155", h="1ca0dd03d0", width=600)
Task 3.1.2: Create a client that connects to the database running at localhost on port 27017.

What's a database client?
What's a database server?
Create a client object for a MongoDB instance.
client = MongoClient(host = "localhost", port = 27017)
Explore
VimeoVideo("665412176", h="6fea7c6346", width=600)
Task 3.1.3: Print a list of the databases available on client.

What's an iterator?
List the databases of a server using PyMongo.
Print output using pprint.
from sys import getsizeof 
print(getsizeof([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]))
print(getsizeof(range(1,21)))
216
48
list(client.list_databases())
[{'name': 'admin', 'sizeOnDisk': 40960, 'empty': False},
 {'name': 'air-quality', 'sizeOnDisk': 4190208, 'empty': False},
 {'name': 'config', 'sizeOnDisk': 12288, 'empty': False},
 {'name': 'local', 'sizeOnDisk': 73728, 'empty': False},
 {'name': 'wqu-abtest', 'sizeOnDisk': 585728, 'empty': False}]
pp.pprint(list(client.list_databases()))
[ {'empty': False, 'name': 'admin', 'sizeOnDisk': 40960},
  {'empty': False, 'name': 'air-quality', 'sizeOnDisk': 4190208},
  {'empty': False, 'name': 'config', 'sizeOnDisk': 12288},
  {'empty': False, 'name': 'local', 'sizeOnDisk': 73728},
  {'empty': False, 'name': 'wqu-abtest', 'sizeOnDisk': 585728}]
VimeoVideo("665412216", h="7d4027dc33", width=600)
Task 3.1.4: Assign the "air-quality" database to the variable db.

What's a MongoDB database?
Access a database using PyMongo.
db = client["air-quality"]
db
Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=True), 'air-quality')
VimeoVideo("665412231", h="89c546b00f", width=600)
Task 3.1.5: Use the list_collections method to print a list of the collections available in db.

What's a MongoDB collection?
List the collections in a database using PyMongo.
list(db.list_collections())[0]
{'name': 'lagos',
 'type': 'timeseries',
 'options': {'timeseries': {'timeField': 'timestamp',
   'metaField': 'metadata',
   'granularity': 'seconds',
   'bucketMaxSpanSeconds': 3600}},
 'info': {'readOnly': False}}
for c in db.list_collections():
    print(c["name"])
lagos
system.buckets.lagos
system.views
dar-es-salaam
system.buckets.dar-es-salaam
nairobi
system.buckets.nairobi
VimeoVideo("665412252", h="bff2abbdc0", width=600)
Task 3.1.6: Assign the "nairobi" collection in db to the variable name nairobi.

Access a collection in a database using PyMongo.
nairobi = db["nairobi"]
nairobi
Collection(Database(MongoClient(host=['localhost:27017'], document_class=dict, tz_aware=False, connect=True), 'air-quality'), 'nairobi')
VimeoVideo("665412270", h="e4a5f5c84b", width=600)
Task 3.1.7: Use the count_documents method to see how many documents are in the nairobi collection.

What's a MongoDB document?
Count the documents in a collection using PyMongo.
nairobi.count_documents({})
202212
Alternate using pandas

​
# Convert the collection to a DataFrame
df = pd.DataFrame(list(nairobi.find()))
​
# Use value_counts() to count documents
document_count = df.shape[0]  # Number of rows in the DataFrame
​
print("Number of documents:", document_count)
Number of documents: 202212
VimeoVideo("665412279", h="c2315f3be1", width=600)
Task 3.1.8: Use the find_one method to retrieve one document from the nairobi collection, and assign it to the variable name result.

What's metadata?
What's semi-structured data?
Retrieve a document from a collection using PyMongo.
result = nairobi.find_one()
pp.pprint(result)
{ '_id': ObjectId('6525d772f44bfedd842a6fcc'),
  'metadata': { 'lat': -1.3,
                'lon': 36.785,
                'measurement': 'temperature',
                'sensor_id': 58,
                'sensor_type': 'DHT22',
                'site': 29},
  'temperature': 16.5,
  'timestamp': datetime.datetime(2018, 9, 1, 0, 0, 4, 301000)}
VimeoVideo("665412306", h="e1e913dfd1", width=600)
Task 3.1.9: Use the distinct method to determine how many sensor sites are included in the nairobi collection.

Get a list of distinct values for a key among all documents using PyMongo.
nairobi.distinct("metadata.site")
[29, 6]
VimeoVideo("665412322", h="4776c6d548", width=600)
Task 3.1.10: Use the count_documents method to determine how many readings there are for each site in the nairobi collection.

Count the documents in a collection using PyMongo. WQU WorldQuant University Applied Data Science Lab QQQQ
print("Documents from site 6:", nairobi.count_documents({"metadata.site":6}))
print("Documents from site 29:", nairobi.count_documents({"metadata.site":29}))
Documents from site 6: 70360
Documents from site 29: 131852
VimeoVideo("665412344", h="d2354584cd", width=600)
Task 3.1.11: Use the aggregate method to determine how many readings there are for each site in the nairobi collection.

Perform aggregation calculations on documents using PyMongo.
site_counts = {}
for site in nairobi.distinct("metadata.site"):
    site_counts[site] = nairobi.count_documents({"metadata.site": site})
​
# Print the result
print(site_counts)
{29: 131852, 6: 70360}
Alternate code

result = nairobi.aggregate(
    [
        {"$group":{"_id":"$metadata.site","count":{"$count":{}}}}
    ]
)
pp.pprint(list(result))
[{'_id': 29, 'count': 131852}, {'_id': 6, 'count': 70360}]
VimeoVideo("665412372", h="565122c9cc", width=600)
Task 3.1.12: Use the distinct method to determine how many types of measurements have been taken in the nairobi collection.

Get a list of distinct values for a key among all documents using PyMongo.
nairobi.distinct("metadata.measurement")
['P1', 'temperature', 'humidity', 'P2']
VimeoVideo("665412380", h="f7f7a39bb3", width=600)
Task 3.1.13: Use the find method to retrieve the PM 2.5 readings from all sites. Be sure to limit your results to 3 records only.

Query a collection using PyMongo.
result = nairobi.find({"metadata.measurement":"P2"}).limit(3)
pp.pprint(list(result))
[ { 'P2': 34.43,
    '_id': ObjectId('6525d775f44bfedd842bf24d'),
    'metadata': { 'lat': -1.3,
                  'lon': 36.785,
                  'measurement': 'P2',
                  'sensor_id': 57,
                  'sensor_type': 'SDS011',
                  'site': 29},
    'timestamp': datetime.datetime(2018, 9, 1, 0, 0, 2, 472000)},
  { 'P2': 30.53,
    '_id': ObjectId('6525d775f44bfedd842bf24e'),
    'metadata': { 'lat': -1.3,
                  'lon': 36.785,
                  'measurement': 'P2',
                  'sensor_id': 57,
                  'sensor_type': 'SDS011',
                  'site': 29},
    'timestamp': datetime.datetime(2018, 9, 1, 0, 5, 3, 941000)},
  { 'P2': 22.8,
    '_id': ObjectId('6525d775f44bfedd842bf24f'),
    'metadata': { 'lat': -1.3,
                  'lon': 36.785,
                  'measurement': 'P2',
                  'sensor_id': 57,
                  'sensor_type': 'SDS011',
                  'site': 29},
    'timestamp': datetime.datetime(2018, 9, 1, 0, 10, 4, 374000)}]
VimeoVideo("665412389", h="8976ea3090", width=600)
Task 3.1.14: Use the aggregate method to calculate how many readings there are for each type ("humidity", "temperature", "P2", and "P1") in site 6.

Perform aggregation calculations on documents using PyMongo.
# Dictionary to store counts for each reading type
reading_counts = {}
# Iterate over each reading type
for reading_type in ["humidity", "temperature", "P2", "P1"]:
    # Count documents for the current reading type in site 6
    count = nairobi.count_documents({"metadata.site": 6, "metadata.measurement": reading_type})
    reading_counts[reading_type] = count
print("Readings in site 6:")
for reading_type, count in reading_counts.items():
    print(f"{reading_type}: {count}")
Readings in site 6:
humidity: 17011
temperature: 17011
P2: 18169
P1: 18169
Alternate code

result = nairobi.aggregate(
    [
        {"$match":{"metadata.site":6}},
        {"$group":{"_id":"$metadata.measurement","count":{"$count":{}}}}
    ]
)
pp.pprint(list(result))
[ {'_id': 'P1', 'count': 18169},
  {'_id': 'temperature', 'count': 17011},
  {'_id': 'humidity', 'count': 17011},
  {'_id': 'P2', 'count': 18169}]
# Query MongoDB for documents with site 6
cursor = nairobi.find({"metadata.site": 6})
​
# Convert cursor to list of dictionaries
documents = list(cursor)
​
# Dictionary to store counts for each measurement
measurement_counts = {}
​
# Iterate over each document
for document in documents:
    # Extract the measurement type from the document
    measurement_type = document["metadata"]["measurement"]
    
    # Update the count for the measurement type
    measurement_counts[measurement_type] = measurement_counts.get(measurement_type, 0) + 1
​
# Print the result
print("Measurements in site 6:")
for measurement_type, count in measurement_counts.items():
    print(f"{measurement_type}: {count}")
​
Measurements in site 6:
temperature: 17011
humidity: 17011
P1: 18169
P2: 18169
# Query MongoDB for documents with site 6
cursor = nairobi.find({"metadata.site": 6})
​
# Convert cursor to pandas DataFrame
df = pd.DataFrame(list(cursor))
​
# Group by measurement type and count occurrences
#measurement_counts = df.groupby('metadata.measurement').size().reset_index(name='count')
measurement_counts = df.groupby(df['metadata'].apply(lambda x: x.get('measurement', 'Unknown'))).size().reset_index(name='count')
​
# Print the result
print("Measurements in site 6:")
print(measurement_counts)
​
Measurements in site 6:
      metadata  count
0           P1  18169
1           P2  18169
2     humidity  17011
3  temperature  17011
VimeoVideo("665412418", h="0c4b125254", width=600)
Task 3.1.15: Use the aggregate method to calculate how many readings there are for each type ("humidity", "temperature", "P2", and "P1") in site 29.

Perform aggregation calculations on documents using PyMongo.
result = nairobi.aggregate(
    [
        {"$match":{"metadata.site":29}},
        {"$group":{"_id":"$metadata.measurement","count":{"$count":{}}}}
    ]
)
pp.pprint(list(result))
[ {'_id': 'P1', 'count': 32907},
  {'_id': 'temperature', 'count': 33019},
  {'_id': 'humidity', 'count': 33019},
  {'_id': 'P2', 'count': 32907}]
Import
VimeoVideo("665412437", h="7a436c7e7e", width=600)
Task 3.1.16: Use the find method to retrieve the PM 2.5 readings from site 29. Be sure to limit your results to 3 records only. Since we won't need the metadata for our model, use the projection argument to limit the results to the "P2" and "timestamp" keys only.

Query a collection using PyMongo.
result = nairobi.find(
    {"metadata.site":29, "metadata.measurement":"P2"}
)
pp.pprint(result.next())
{ 'P2': 34.43,
  '_id': ObjectId('6525d775f44bfedd842bf24d'),
  'metadata': { 'lat': -1.3,
                'lon': 36.785,
                'measurement': 'P2',
                'sensor_id': 57,
                'sensor_type': 'SDS011',
                'site': 29},
  'timestamp': datetime.datetime(2018, 9, 1, 0, 0, 2, 472000)}
result = nairobi.find(
    {"metadata.site":29, "metadata.measurement":"P2"},
    projection ={"P2":1, "timestamp": 1 , "_id" :0}
)
pp.pprint(result.next())
{'P2': 34.43, 'timestamp': datetime.datetime(2018, 9, 1, 0, 0, 2, 472000)}
#result = ...
#pp.pprint(result.next())
VimeoVideo("665412442", h="494636d1ea", width=600)
Task 3.1.17: Read records from your result into the DataFrame df. Be sure to set the index to "timestamp".

Create a DataFrame from a dictionary using pandas.
# Convert the cursor to a list and create a DataFrame
df = pd.DataFrame(list(result))
​
# Ensure the 'timestamp' column is of datetime type
df['timestamp'] = pd.to_datetime(df['timestamp'])
​
# Set 'timestamp' as the index
df.set_index('timestamp', inplace=True)
​
# Display the first few rows of the DataFrame
df.head()
P2
timestamp	
2018-09-01 00:05:03.941	30.53
2018-09-01 00:10:04.374	22.80
2018-09-01 00:15:04.245	13.30
2018-09-01 00:20:04.869	16.57
2018-09-01 00:25:04.659	14.07
# Check your work
assert df.shape[1] == 1, f"`df` should have only one column, not {df.shape[1]}."
assert df.columns == [
    "P2"
], f"The single column in `df` should be `'P2'`, not {df.columns[0]}."
assert isinstance(df.index, pd.DatetimeIndex), "`df` should have a `DatetimeIndex`."
Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
16
Python 3 (ipykernel) | Idle
0
031-data-wrangling-with-mongodb.ipynb
Ln 1, Col 1
Mode: Command