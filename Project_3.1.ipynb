{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# MongoDB Tasks - WorldQuant University\n", "## Air Quality Data Wrangling with Python & PyMongo"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.1: Instantiate PrettyPrinter\n", "Create a PrettyPrinter instance with indent=2\n", "\n", "**💡 Tips:**\n", "- Import from `pprint` module\n", "- Set `indent=2` for better readability\n", "- Assign to variable `pp`\n", "\n", "**📝 Example:**\n", "```python\n", "from pprint import PrettyPrinter\n", "pp = PrettyPrinter(indent=2)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.2: Create MongoDB Client\n", "Connect to MongoDB server at localhost:27017\n", "\n", "**💡 Tips:**\n", "- Import `MongoClient` from `pymongo`\n", "- Default port is 27017\n", "- Use `localhost` for local connection\n", "\n", "**📝 Example:**\n", "```python\n", "from pymongo import MongoClient\n", "client = MongoClient(host=\"localhost\", port=27017)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.3: List Available Databases\n", "Use client.list_databases() to see all databases\n", "\n", "**💡 Tips:**\n", "- Use `client.list_databases()` method\n", "- Convert to list for display\n", "- Use PrettyPrinter for nice formatting\n", "\n", "**📝 Example:**\n", "```python\n", "databases = list(client.list_databases())\n", "pp.pprint(databases)\n", "```\n", "\n", "**🎯 Expected:** You should see databases like 'admin', 'air-quality', 'config', etc."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.4: Access Specific Database\n", "Access the air-quality database\n", "\n", "**💡 Tips:**\n", "- Use dictionary-style access\n", "- Store in variable `db`\n", "- Print to verify connection\n", "\n", "**📝 Example:**\n", "```python\n", "db = client[\"air-quality\"]\n", "print(db)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.5: List Collections\n", "List all collections in the air-quality database\n", "\n", "**💡 Tips:**\n", "- Use `db.list_collections()` method\n", "- Iterate to print collection names\n", "- Look for city names (lagos, nairobi, dar-es-salaam)\n", "\n", "**📝 Example:**\n", "```python\n", "for collection in db.list_collections():\n", "    print(collection[\"name\"])\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.6: Access Collection\n", "Access the nairobi collection\n", "\n", "**💡 Tips:**\n", "- Use dictionary-style access\n", "- Store in variable `nairobi`\n", "- This contains air quality data from Nairobi, Kenya\n", "\n", "**📝 Example:**\n", "```python\n", "nairobi = db[\"nairobi\"]\n", "print(nairobi)\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.7: Count Documents\n", "Count total documents in nairobi collection\n", "\n", "**💡 Tips:**\n", "- Use `count_documents({})` with empty filter\n", "- Empty filter `{}` means count all documents\n", "- Should return around 202,212 documents\n", "\n", "**📝 Example:**\n", "```python\n", "total_docs = nairobi.count_documents({})\n", "print(f\"Total documents: {total_docs}\")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.8: Retrieve Sample Document\n", "Get one document to examine structure\n", "\n", "**💡 Tips:**\n", "- Use `find_one()` method\n", "- Use PrettyPrinter for readable output\n", "- Look for metadata, timestamp, and measurement fields\n", "\n", "**📝 Example:**\n", "```python\n", "sample_doc = nairobi.find_one()\n", "pp.pprint(sample_doc)\n", "```\n", "\n", "**🔍 What to observe:** _id, metadata (lat, lon, sensor info), measurement value, timestamp"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.9: Find Distinct Sites\n", "Find unique sensor sites in the data\n", "\n", "**💡 Tips:**\n", "- Use `distinct()` method\n", "- Field path: \"metadata.site\"\n", "- Should return [29, 6] - two sensor locations\n", "\n", "**📝 Example:**\n", "```python\n", "sites = nairobi.distinct(\"metadata.site\")\n", "print(f\"Sensor sites: {sites}\")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.10: Count by Site\n", "Count documents for each sensor site\n", "\n", "**💡 Tips:**\n", "- Use `count_documents()` with filter\n", "- Filter format: `{\"metadata.site\": site_number}`\n", "- Count for both sites 6 and 29\n", "\n", "**📝 Example:**\n", "```python\n", "site_6_count = nairobi.count_documents({\"metadata.site\": 6})\n", "site_29_count = nairobi.count_documents({\"metadata.site\": 29})\n", "print(f\"Site 6: {site_6_count}, Site 29: {site_29_count}\")\n", "```\n", "\n", "**🎯 Expected:** Site 6: ~70,360, Site 29: ~131,852"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.11: Aggregate Site Counts\n", "Use aggregation pipeline to count by site\n", "\n", "**💡 Tips:**\n", "- Use `aggregate()` method\n", "- Use `$group` stage with `$count` operator\n", "- More efficient than multiple count_documents calls\n", "\n", "**📝 Example:**\n", "```python\n", "pipeline = [\n", "    {\"$group\": {\"_id\": \"$metadata.site\", \"count\": {\"$count\": {}}}}\n", "]\n", "result = nairobi.aggregate(pipeline)\n", "pp.pprint(list(result))\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.12: Measurement Types\n", "Find distinct measurement types\n", "\n", "**💡 Tips:**\n", "- Use `distinct()` on \"metadata.measurement\"\n", "- Should find 4 types: P1, P2, temperature, humidity\n", "- P1=PM10, P2=PM2.5 (air pollution particles)\n", "\n", "**📝 Example:**\n", "```python\n", "measurements = nairobi.distinct(\"metadata.measurement\")\n", "print(f\"Measurement types: {measurements}\")\n", "```\n", "\n", "**📚 What they mean:**\n", "- P1: Particles ≤ 10 micrometers\n", "- P2: Particles ≤ 2.5 micrometers\n", "- temperature: Air temperature (°C)\n", "- humidity: Relative humidity (%)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.13: Query PM2.5\n", "Query PM2.5 readings with limit\n", "\n", "**💡 Tips:**\n", "- Use `find()` with filter for P2 measurements\n", "- Use `.limit(3)` to get only 3 results\n", "- PM2.5 is important for health studies\n", "\n", "**📝 Example:**\n", "```python\n", "pm25_readings = nairobi.find({\"metadata.measurement\": \"P2\"}).limit(3)\n", "pp.pprint(list(pm25_readings))\n", "```\n", "\n", "**🏥 Health note:** PM2.5 particles can penetrate deep into lungs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.14: Count by Type (Site 6)\n", "Count measurement types for site 6\n", "\n", "**💡 Tips:**\n", "- Combine site filter with measurement filter\n", "- Count each type: P1, P2, temperature, humidity\n", "- Use loop for efficiency\n", "\n", "**📝 Example:**\n", "```python\n", "measurement_types = [\"P1\", \"P2\", \"temperature\", \"humidity\"]\n", "for measurement in measurement_types:\n", "    count = nairobi.count_documents({\n", "        \"metadata.site\": 6,\n", "        \"metadata.measurement\": measurement\n", "    })\n", "    print(f\"{measurement}: {count}\")\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.15: Count by Type (Site 29)\n", "Count measurement types for site 29\n", "\n", "**💡 Tips:**\n", "- Use aggregation for efficiency\n", "- First `$match` to filter site 29\n", "- Then `$group` by measurement type\n", "\n", "**📝 Example:**\n", "```python\n", "pipeline = [\n", "    {\"$match\": {\"metadata.site\": 29}},\n", "    {\"$group\": {\"_id\": \"$metadata.measurement\", \"count\": {\"$count\": {}}}}\n", "]\n", "result = nairobi.aggregate(pipeline)\n", "pp.pprint(list(result))\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.16: Query with Projection\n", "Use projection to limit returned fields\n", "\n", "**💡 Tips:**\n", "- Use `projection` parameter in `find()`\n", "- Include only P2 and timestamp fields\n", "- Exclude _id with `\"_id\": 0`\n", "- Saves bandwidth and memory\n", "\n", "**📝 Example:**\n", "```python\n", "result = nairobi.find(\n", "    {\"metadata.site\": 29, \"metadata.measurement\": \"P2\"},\n", "    projection={\"P2\": 1, \"timestamp\": 1, \"_id\": 0}\n", ").limit(3)\n", "pp.pprint(list(result))\n", "```\n", "\n", "**⚡ Benefits:** Faster queries, less memory usage, cleaner data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 3.1.17: Create DataFrame\n", "Convert MongoDB data to pandas DataFrame\n", "\n", "**💡 Tips:**\n", "- Query P2 data with projection\n", "- Convert cursor to list, then to DataFrame\n", "- Set timestamp as index for time-series analysis\n", "- Use `pd.to_datetime()` for proper datetime format\n", "\n", "**📝 Example:**\n", "```python\n", "import pandas as pd\n", "\n", "# Query data\n", "cursor = nairobi.find(\n", "    {\"metadata.site\": 29, \"metadata.measurement\": \"P2\"},\n", "    projection={\"P2\": 1, \"timestamp\": 1, \"_id\": 0}\n", ")\n", "\n", "# Create DataFrame\n", "df = pd.DataFrame(list(cursor))\n", "df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "df.set_index('timestamp', inplace=True)\n", "\n", "print(df.head())\n", "print(f\"DataFrame shape: {df.shape}\")\n", "```\n", "\n", "**🎯 Result:** Time-series DataFrame ready for analysis!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}