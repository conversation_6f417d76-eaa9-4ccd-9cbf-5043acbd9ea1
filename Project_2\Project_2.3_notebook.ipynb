{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Project 2.3: Neighborhood Price Prediction 🏘️\n", "\n", "## Learning Objectives\n", "- Advanced categorical variable handling\n", "- Neighborhood-based price modeling\n", "- Location feature engineering\n", "- Model interpretation for business insights\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import OneHotEncoder, LabelEncoder\n", "from sklearn.metrics import mean_absolute_error, r2_score\n", "from sklearn.model_selection import cross_val_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Data Preparation"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    df = pd.read_csv(filepath)\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_cost = df[\"price_aprox_usd\"] < 400_000\n", "    df = df[mask_ba & mask_apt & mask_cost]\n", "    \n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "    \n", "    df[\"neighborhood\"] = df[\"place_with_parent_names\"].str.split(\"|\").str[3]\n", "    return df\n", "\n", "df = wrangle(\"data/buenos-aires-real-estate-1.csv\")\n", "print(f\"Dataset shape: {df.shape}\")\n", "df.head()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Neighborhood Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# Neighborhood statistics\n", "neighborhood_stats = df.groupby('neighborhood').agg({\n", "    'price_aprox_usd': ['count', 'mean', 'std'],\n", "    'surface_covered_in_m2': 'mean'\n", "}).round(2)\n", "\n", "neighborhood_stats.columns = ['Count', 'Avg_Price', 'Price_Std', 'Avg_Area']\n", "neighborhood_stats = neighborhood_stats[neighborhood_stats['Count'] >= 20]\n", "neighborhood_stats = neighborhood_stats.sort_values('Avg_Price', ascending=False)\n", "\n", "print(\"Top neighborhoods by average price:\")\n", "print(neighborhood_stats.head(10))\n", "\n", "# Visualization\n", "plt.figure(figsize=(12, 6))\n", "top_10 = neighborhood_stats.head(10)\n", "plt.bar(range(len(top_10)), top_10['Avg_Price'], color='skyblue')\n", "plt.xlabel('Neighborhood')\n", "plt.ylabel('Average Price (USD)')\n", "plt.title('Top 10 Neighborhoods by Average Price')\n", "plt.xticks(range(len(top_10)), top_10.index, rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Model Building"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# Filter to major neighborhoods\n", "major_neighborhoods = neighborhood_stats[neighborhood_stats['Count'] >= 30].index\n", "model_data = df[df['neighborhood'].isin(major_neighborhoods)].copy()\n", "print(f\"Major neighborhoods: {len(major_neighborhoods)}\")\n", "print(f\"Model data points: {len(model_data)}\")\n", "\n", "# Baseline model (area only)\n", "X_simple = model_data[['surface_covered_in_m2']]\n", "y = model_data['price_aprox_usd']\n", "\n", "model_simple = LinearRegression()\n", "model_simple.fit(X_simple, y)\n", "y_pred_simple = model_simple.predict(X_simple)\n", "r2_simple = r2_score(y, y_pred_simple)\n", "mae_simple = mean_absolute_error(y, y_pred_simple)\n", "\n", "print(f\"\\nSimple Model (Area only):\")\n", "print(f\"R²: {r2_simple:.4f}, MAE: ${mae_simple:,.0f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# Neighborhood model\n", "encoder = OneHotEncoder(sparse_output=False, drop='first')\n", "neighborhood_encoded = encoder.fit_transform(model_data[['neighborhood']])\n", "feature_names = list(encoder.get_feature_names_out(['neighborhood']))\n", "\n", "X_with_neighborhood = np.column_stack([X_simple.values, neighborhood_encoded])\n", "all_feature_names = ['surface_covered_in_m2'] + feature_names\n", "\n", "model_neighborhood = LinearRegression()\n", "model_neighborhood.fit(X_with_neighborhood, y)\n", "y_pred_neighborhood = model_neighborhood.predict(X_with_neighborhood)\n", "r2_neighborhood = r2_score(y, y_pred_neighborhood)\n", "mae_neighborhood = mean_absolute_error(y, y_pred_neighborhood)\n", "\n", "print(f\"Neighborhood Model:\")\n", "print(f\"R²: {r2_neighborhood:.4f}, MAE: ${mae_neighborhood:,.0f}\")\n", "print(f\"Improvement: {((r2_neighborhood/r2_simple)-1)*100:.1f}% in R²\")\n", "\n", "# Feature importance\n", "feature_importance = pd.DataFrame({\n", "    'Feature': all_feature_names,\n", "    'Coefficient': model_neighborhood.coef_\n", "})\n", "feature_importance['Abs_Coef'] = np.abs(feature_importance['Coefficient'])\n", "feature_importance = feature_importance.sort_values('Abs_Coef', ascending=False)\n", "\n", "print(\"\\nTop 5 most important features:\")\n", "print(feature_importance.head())"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Model Evaluation and Insights"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# Cross-validation\n", "cv_simple = cross_val_score(LinearRegression(), X_simple, y, cv=5, scoring='r2')\n", "cv_neighborhood = cross_val_score(LinearRegression(), X_with_neighborhood, y, cv=5, scoring='r2')\n", "\n", "print(f\"Cross-validation R² scores:\")\n", "print(f\"Simple model: {cv_simple.mean():.4f} (±{cv_simple.std():.4f})\")\n", "print(f\"Neighborhood model: {cv_neighborhood.mean():.4f} (±{cv_neighborhood.std():.4f})\")\n", "\n", "# Neighborhood effects\n", "neighborhood_effects = {}\n", "for i, name in enumerate(feature_names):\n", "    clean_name = name.replace('neighborhood_', '')\n", "    neighborhood_effects[clean_name] = model_neighborhood.coef_[i+1]\n", "\n", "effects_df = pd.DataFrame(list(neighborhood_effects.items()), columns=['Neighborhood', 'Effect'])\n", "effects_df = effects_df.sort_values('Effect', ascending=False)\n", "\n", "print(\"\\nNeighborhood price effects (vs baseline):\")\n", "print(effects_df.head(10))\n", "\n", "# Visualization\n", "plt.figure(figsize=(12, 8))\n", "plt.subplot(2, 1, 1)\n", "top_effects = effects_df.head(8)\n", "colors = ['green' if x > 0 else 'red' for x in top_effects['Effect']]\n", "plt.barh(range(len(top_effects)), top_effects['Effect'], color=colors, alpha=0.7)\n", "plt.yticks(range(len(top_effects)), top_effects['Neighborhood'])\n", "plt.xlabel('Price Effect (USD)')\n", "plt.title('Top Neighborhood Effects on Price')\n", "plt.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "\n", "plt.subplot(2, 1, 2)\n", "plt.scatter(y, y_pred_simple, alpha=0.6, label='Simple Model')\n", "plt.scatter(y, y_pred_neighborhood, alpha=0.6, label='With Neighborhoods')\n", "plt.plot([y.min(), y.max()], [y.min(), y.max()], 'r--', alpha=0.8)\n", "plt.xlabel('Actual Price')\n", "plt.ylabel('Predicted Price')\n", "plt.title('Model Performance Comparison')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["## Practical Applications"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# Price predictions for different neighborhoods\n", "sample_area = 75  # 75 m² apartment\n", "baseline_price = model_simple.predict([[sample_area]])[0]\n", "\n", "print(f\"Price predictions for {sample_area} m² apartment:\")\n", "print(f\"Baseline (area only): ${baseline_price:,.0f}\")\n", "print(\"\\nBy neighborhood:\")\n", "\n", "for neighborhood in major_neighborhoods[:10]:\n", "    sample_encoded = encoder.transform([[neighborhood]])\n", "    sample_data = np.column_stack([[sample_area], sample_encoded])\n", "    pred_price = model_neighborhood.predict(sample_data)[0]\n", "    difference = pred_price - baseline_price\n", "    print(f\"{neighborhood}: ${pred_price:,.0f} ({difference:+,.0f})\")\n", "\n", "print(\"\\nModel Summary:\")\n", "print(f\"• Neighborhood information improves R² by {((r2_neighborhood/r2_simple)-1)*100:.1f}%\")\n", "print(f\"• Location accounts for ${mae_simple - mae_neighborhood:,.0f} reduction in MAE\")\n", "print(f\"• Model explains {r2_neighborhood*100:.1f}% of price variation\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}