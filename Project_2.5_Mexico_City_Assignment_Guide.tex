\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 2.5: Mexico City Assignment}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 2.5: Predicting Apartment Prices in Mexico City} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Independent Assignment - Apply Your Skills}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Your Independent Challenge}

Welcome to Project 2.5! This is your chance to demonstrate everything you've learned by applying your skills to a new city: Mexico City. Unlike previous projects with step-by-step guidance, this is more independent - you'll make many decisions yourself.

\subsection{What Makes This Different}
\begin{itemize}
    \item \textbf{New Dataset:} Mexico City instead of Buenos Aires
    \item \textbf{Different Price Range:} Apartments under \$100,000 USD
    \item \textbf{New Geography:} Different coordinate system and borough structure
    \item \textbf{Independent Work:} You choose libraries and approaches
    \item \textbf{Assessment Focus:} Your code will be automatically graded
\end{itemize}

\begin{concept}
\textbf{Assignment vs Tutorial:}
This project tests whether you can:
\begin{itemize}
    \item Apply learned techniques to new data
    \item Make appropriate data science decisions
    \item Write clean, working code independently
    \item Adapt to different datasets and constraints
\end{itemize}
Think of this as your "final exam" for the linear regression module!
\end{concept}

\section{Task 2.5.1: Strategic Data Wrangling Function}

Your first challenge: create a comprehensive wrangle function for Mexico City data. This requires adapting your Buenos Aires approach to new constraints.

\begin{lstlisting}[caption=Mexico City Data Wrangling Strategy]
# You need to import your own libraries - part of the challenge!
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.express as px
from glob import glob
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.compose import ColumnTransformer
from sklearn.metrics import mean_absolute_error
from category_encoders import OneHotEncoder
from sklearn.impute import SimpleImputer

def wrangle(filepath):
    """
    Comprehensive data cleaning for Mexico City real estate.
    
    Key differences from Buenos Aires:
    - Filter for "Distrito Federal" instead of "Capital Federal"
    - Price limit: $100,000 instead of $400,000
    - Extract "borough" instead of "neighborhood"
    - Mexico City has 16 boroughs vs Buenos Aires neighborhoods
    """
    
    # Read the data
    df = pd.read_csv(filepath)
    
    # Step 1: Basic filtering for Mexico City
    mask_mx = df["place_with_parent_names"].str.contains("Distrito Federal", na=False)
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 100_000  # Different price range!
    df = df[mask_mx & mask_apt & mask_price]
    
    # Step 2: Remove outliers (keep middle 80%)
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df = df[mask_area]
    
    # Step 3: Extract coordinates (same process)
    if "lat-lon" in df.columns:
        df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
        df.drop(columns="lat-lon", inplace=True)
    
    # Step 4: Extract borough (Mexico City structure)
    # Mexico City: Country|State|City|Borough format
    df["borough"] = df["place_with_parent_names"].str.split("|", expand=True)[1]
    df.drop(columns="place_with_parent_names", inplace=True)
    
    # Step 5: Advanced cleaning based on lessons learned
    # Drop columns with >50% null values
    thresh = len(df) / 2
    df = df.dropna(axis=1, thresh=thresh)
    
    # Drop problematic categorical columns
    cols_to_drop = ["operation", "property_type", "currency", "properati_url"]
    df = df.drop(columns=[col for col in cols_to_drop if col in df.columns])
    
    # Drop leakage columns
    leakage_cols = ["price", "price_aprox_local_currency", "price_per_m2", "price_usd_per_m2"]
    df = df.drop(columns=[col for col in leakage_cols if col in df.columns])
    
    # Drop multicollinear features
    multicollinear_cols = ["surface_total_in_m2", "floor", "rooms", "expenses"]
    df = df.drop(columns=[col for col in multicollinear_cols if col in df.columns])
    
    return df

# Test your function
test_df = wrangle("./data/mexico-city-real-estate-1.csv")
print(f"[CHECK] Test successful: {len(test_df)} Mexico City apartments loaded")
print(f"Columns: {list(test_df.columns)}")
print(f"Sample data:")
print(test_df.head())
\end{lstlisting}

\begin{learningtip}
\textbf{Adapting to New Data:}
When working with new datasets:
\begin{itemize}
    \item \textbf{Understand the structure:} How is location encoded?
    \item \textbf{Check value ranges:} Different cities have different price ranges
    \item \textbf{Verify assumptions:} Are column names the same?
    \item \textbf{Test thoroughly:} Make sure your function works
\end{itemize}
Good data scientists adapt their code to new datasets efficiently!
\end{learningtip}

\section{Task 2.5.2: Automated File Discovery}

Use your glob skills to automatically find all Mexico City data files.

\begin{lstlisting}[caption=Smart File Loading]
# Find all Mexico City files (exclude test files)
files = glob("./data/mexico-city-real-estate-*.csv")
files = [f for f in files if "test-features" not in f]

print(f"Found Mexico City datasets:")
for i, file in enumerate(files, 1):
    print(f"{i}. {file}")

print(f"\nTotal training files: {len(files)}")

# Quick validation
if len(files) == 0:
    print("ERROR: No files found! Check your data directory.")
elif len(files) < 3:
    print("WARNING: Expected more files. Check file naming.")
else:
    print("[CHECK] File discovery complete.")
\end{lstlisting}

\section{Task 2.5.3: Master Data Combination}

Combine all datasets using your learned techniques.

\begin{lstlisting}[caption=Professional Data Integration]
# Apply wrangle function to all files
print("Processing all Mexico City datasets...")

frames = []
for file in files:
    try:
        df = wrangle(file)
        frames.append(df)
        print(f"[CHECK] Processed {file}: {len(df)} apartments")
    except Exception as e:
        print(f"[ERROR] Error processing {file}: {e}")

# Combine all datasets
if frames:
    df = pd.concat(frames, ignore_index=True)
    
    print(f"\nCOMBINED DATASET SUMMARY:")
    print(f"Total apartments: {len(df):,}")
    print(f"Features: {df.shape[1]}")
    print(f"Price range: ${df['price_aprox_usd'].min():,.0f} - ${df['price_aprox_usd'].max():,.0f}")
    print(f"Unique boroughs: {df['borough'].nunique()}")
    
    # Data quality check
    missing_data = df.isnull().sum().sum()
    print(f"Missing values: {missing_data}")
    
    print(f"\nDataset ready for analysis!")
else:
    print("ERROR: No data successfully processed!")
\end{lstlisting}

\section{Task 2.5.4: Exploratory Data Analysis}

Create visualizations to understand Mexico City's real estate market.

\begin{lstlisting}[caption=Mexico City Market Analysis]
# Distribution of apartment prices
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.hist(df["price_aprox_usd"], bins=30, color='skyblue', edgecolor='black', alpha=0.7)
plt.xlabel("Price [USD]")
plt.ylabel("Count")
plt.title("Distribution of Apartment Prices")
plt.grid(axis='y', alpha=0.3)

# Add statistics
mean_price = df["price_aprox_usd"].mean()
median_price = df["price_aprox_usd"].median()
plt.axvline(mean_price, color='red', linestyle='--', label=f'Mean: ${mean_price:,.0f}')
plt.axvline(median_price, color='green', linestyle='--', label=f'Median: ${median_price:,.0f}')
plt.legend()

# Price vs Area relationship
plt.subplot(1, 2, 2)
plt.scatter(df['surface_covered_in_m2'], df['price_aprox_usd'], alpha=0.6, s=20)
plt.xlabel('Area [sq meters]')
plt.ylabel('Price [USD]')
plt.title('Mexico City: Price vs. Area')
plt.grid(True, alpha=0.3)

# Add trend line
z = np.polyfit(df['surface_covered_in_m2'], df['price_aprox_usd'], 1)
p = np.poly1d(z)
plt.plot(df['surface_covered_in_m2'], p(df['surface_covered_in_m2']), "r--", alpha=0.8)

plt.tight_layout()
plt.show()

# Market insights
print(f"MEXICO CITY MARKET INSIGHTS:")
print(f"Average price: ${df['price_aprox_usd'].mean():,.0f}")
print(f"Average size: {df['surface_covered_in_m2'].mean():.0f} sq meters")
print(f"Price per sq meter: ${(df['price_aprox_usd'] / df['surface_covered_in_m2']).mean():,.0f}")

# Compare to Buenos Aires insights from previous projects
correlation = np.corrcoef(df['surface_covered_in_m2'], df['price_aprox_usd'])[0,1]
print(f"Size-price correlation: {correlation:.3f}")

if correlation > 0.6:
    print("Strong relationship between size and price - good for modeling!")
elif correlation > 0.3:
    print("Moderate relationship - size will be helpful but not everything")
else:
    print("Weak relationship - size alone may not predict price well")
\end{lstlisting}

\section{Task 2.5.5: Advanced Visualization Challenge}

Create an optional Mapbox visualization to see geographic patterns.

\begin{lstlisting}[caption=Geographic Price Visualization]
# Optional: Create a Mapbox scatter plot showing location and price
# This requires plotly and internet connection

try:
    # Create interactive map (if you want to try this)
    fig = px.scatter_mapbox(
        df.head(1000),  # Use subset for performance
        lat="lat", 
        lon="lon", 
        color="price_aprox_usd",
        size="surface_covered_in_m2",
        hover_data=["borough"],
        color_continuous_scale="Viridis",
        size_max=15,
        zoom=10,
        mapbox_style="open-street-map",
        title="Mexico City Apartment Prices by Location"
    )
    
    fig.show()
    print("[CHECK] Interactive map created successfully!")
    
except Exception as e:
    print(f"[WARNING] Map visualization not available: {e}")
    print("This is optional - continue with the assignment")

# Alternative: Simple scatter plot of coordinates
plt.figure(figsize=(10, 8))
scatter = plt.scatter(df['lon'], df['lat'], c=df['price_aprox_usd'], 
                     cmap='coolwarm', alpha=0.6, s=30)
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.title('Mexico City: Apartment Locations by Price')
plt.colorbar(scatter, label='Price (USD)')
plt.grid(True, alpha=0.3)
plt.show()

print("[CHECK] Geographic visualization complete!")
\end{lstlisting}

\section{Task 2.5.6: Machine Learning Setup}

Prepare your data for machine learning following best practices.

\begin{lstlisting}[caption=ML-Ready Data Preparation]
# Create feature matrix and target vector
target = "price_aprox_usd"
features = [col for col in df.columns if col != target]

X_train = df[features].copy()
y_train = df[target].copy()

print(f"MACHINE LEARNING SETUP:")
print(f"Features (X): {X_train.shape}")
print(f"Target (y): {y_train.shape}")

# Analyze feature types
numerical_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
categorical_features = X_train.select_dtypes(include=['object']).columns.tolist()

print(f"\nFeature breakdown:")
print(f"Numerical: {numerical_features}")
print(f"Categorical: {categorical_features}")

# Check for missing data
missing_summary = X_train.isnull().sum()
print(f"\nMissing data check:")
for feature in X_train.columns:
    missing_count = missing_summary[feature]
    if missing_count > 0:
        percentage = (missing_count / len(X_train)) * 100
        print(f"  {feature}: {missing_count} ({percentage:.1f}%)")
    else:
        print(f"  {feature}: No missing data [OK]")

# Final validation
print(f"\nDataset validation:")
print(f"[CHECK] Features ready: {len(features)} columns")
print(f"[CHECK] Target ready: {len(y_train)} values")
print(f"[CHECK] No shape mismatches")
print(f"Ready for model training!")
\end{lstlisting}

\section{Task 2.5.7: Baseline Model Performance}

Establish baseline performance before building complex models.

\begin{lstlisting}[caption=Baseline Performance Measurement]
# Calculate baseline: always predict the mean
y_mean = y_train.mean()
y_pred_baseline = [y_mean] * len(y_train)
baseline_mae = mean_absolute_error(y_train, y_pred_baseline)

print(f"BASELINE MODEL PERFORMANCE:")
print(f"Strategy: Always predict the average price")
print(f"Average apartment price: ${y_mean:,.0f}")
print(f"Baseline MAE: ${baseline_mae:,.0f}")

# Context for baseline
price_std = y_train.std()
price_range = y_train.max() - y_train.min()

print(f"\nBaseline context:")
print(f"Price standard deviation: ${price_std:,.0f}")
print(f"Price range: ${price_range:,.0f}")
print(f"Baseline as % of std dev: {(baseline_mae/price_std)*100:.1f}%")
print(f"Baseline as % of range: {(baseline_mae/price_range)*100:.1f}%")

# Set improvement target
print(f"\nImprovement target:")
print(f"Good model: <${baseline_mae*0.8:,.0f} MAE (20% better)")
print(f"Great model: <${baseline_mae*0.6:,.0f} MAE (40% better)")
\end{lstlisting}

\section{Task 2.5.8: Professional ML Pipeline}

Build a sophisticated model using everything you've learned.

\begin{lstlisting}[caption=Advanced ML Pipeline Construction]
# Build comprehensive pipeline
model = make_pipeline(
    OneHotEncoder(use_cat_names=True),  # Handle borough names
    SimpleImputer(strategy='median'),    # Handle missing coordinates
    Ridge(alpha=1.0)                     # Regularized regression
)

print("BUILDING ADVANCED ML PIPELINE:")
print("Step 1: OneHotEncoder - Convert borough names to numerical features")
print("Step 2: SimpleImputer - Fill missing coordinate values") 
print("Step 3: Ridge Regression - Learn with regularization")

# Train the model
print(f"\nTraining on {len(X_train)} Mexico City apartments...")
model.fit(X_train, y_train)
print("[CHECK] Training complete!")

# Evaluate training performance
y_pred_train = model.predict(X_train)
train_mae = mean_absolute_error(y_train, y_pred_train)

print(f"\nTRAINING PERFORMANCE:")
print(f"Model MAE: ${train_mae:,.0f}")
print(f"Baseline MAE: ${baseline_mae:,.0f}")
improvement = baseline_mae - train_mae
improvement_pct = (improvement / baseline_mae) * 100
print(f"Improvement: ${improvement:,.0f} ({improvement_pct:.1f}%)")

# Performance assessment
if improvement_pct > 40:
    print("[EXCELLENT] Model performs much better than baseline!")
elif improvement_pct > 20:
    print("[GOOD] Model shows solid improvement over baseline")
elif improvement_pct > 5:
    print("[FAIR] Model shows some improvement")
else:
    print("[WARNING] Model barely better than baseline")

# Calculate R-squared for additional insight
from sklearn.metrics import r2_score
r2 = r2_score(y_train, y_pred_train)
print(f"R-squared: {r2:.3f} ({r2*100:.1f}% variance explained)")
\end{lstlisting}

\section{Task 2.5.9: Model Testing and Validation}

Test your model on unseen data to check for overfitting.

\begin{lstlisting}[caption=Model Validation on Test Data]
# Load test data (no target variable)
X_test = pd.read_csv('./data/mexico-city-test-features.csv')

print("TESTING ON UNSEEN DATA:")
print(f"Test set shape: {X_test.shape}")
print(f"Test features: {list(X_test.columns)}")

# Verify feature consistency
train_features = set(X_train.columns)
test_features = set(X_test.columns)

if train_features == test_features:
    print("[CHECK] Feature sets match perfectly")
else:
    missing_in_test = train_features - test_features
    extra_in_test = test_features - train_features
    
    if missing_in_test:
        print(f"[WARNING] Missing in test: {missing_in_test}")
    if extra_in_test:
        print(f"[WARNING] Extra in test: {extra_in_test}")

# Make predictions on test set
y_test_pred = model.predict(X_test)

print(f"\nTEST PREDICTIONS:")
print(f"Generated {len(y_test_pred)} predictions")
print(f"Prediction range: ${y_test_pred.min():,.0f} - ${y_test_pred.max():,.0f}")
print(f"Average prediction: ${y_test_pred.mean():,.0f}")

# Sample predictions
print(f"\nSample test predictions:")
for i in range(min(5, len(y_test_pred))):
    pred = y_test_pred[i]
    print(f"{i+1}. Predicted price: ${pred:,.0f}")

# Convert to Series for submission
y_test_pred_series = pd.Series(y_test_pred)
print(f"\nPredictions ready for submission: {len(y_test_pred_series)} values")
\end{lstlisting}

\section{Task 2.5.10: Feature Importance Analysis}

Understand what drives apartment prices in Mexico City.

\begin{lstlisting}[caption=Feature Importance Investigation]
# Extract model coefficients
ridge_model = model.named_steps['ridge']
coefficients = ridge_model.coef_

# Get feature names after encoding
encoder = model.named_steps['onehotencoder']
feature_names = encoder.get_feature_names_out()

# Create feature importance DataFrame
feat_imp = pd.Series(coefficients, index=feature_names)
feat_imp_sorted = feat_imp.reindex(feat_imp.abs().sort_values().index)

print("MEXICO CITY PRICE DRIVERS:")
print("=" * 35)

# Show most influential features
print("\nMost influential features (top 10):")
top_features = feat_imp_sorted.tail(10)
for feature, coef in top_features.items():
    direction = "increases" if coef > 0 else "decreases"
    print(f"  {feature}: ${coef:,.0f} ({direction} price)")

# Analyze by feature type
numerical_impact = {}
borough_impact = {}

for feature, coef in feat_imp.items():
    if 'borough_' in feature:
        borough_name = feature.replace('borough_', '')
        borough_impact[borough_name] = coef
    else:
        numerical_impact[feature] = coef

print(f"\nNUMERICAL FEATURES IMPACT:")
for feature, coef in numerical_impact.items():
    print(f"  {feature}: ${coef:,.0f}")

print(f"\nBOROUGH EFFECTS (top 5 positive, top 5 negative):")
borough_series = pd.Series(borough_impact)
print("Most expensive boroughs:")
for borough, effect in borough_series.nlargest(5).items():
    print(f"  {borough}: +${effect:,.0f}")

print("Least expensive boroughs:")
for borough, effect in borough_series.nsmallest(5).items():
    print(f"  {borough}: ${effect:,.0f}")

# Feature importance visualization
plt.figure(figsize=(12, 8))
top_10_features = feat_imp_sorted.tail(10)
colors = ['red' if x < 0 else 'green' for x in top_10_features.values]

plt.barh(range(len(top_10_features)), top_10_features.values, color=colors, alpha=0.7)
plt.yticks(range(len(top_10_features)), top_10_features.index)
plt.xlabel('Importance [USD]')
plt.title('Top 10 Most Important Features for Mexico City Apartment Prices')
plt.grid(axis='x', alpha=0.3)
plt.tight_layout()
plt.show()
\end{lstlisting}

\section{Summary: Independent Assignment Mastery}

\subsection{Skills Demonstrated}
\begin{enumerate}
    \item \textbf{Adaptability:} Successfully applied Buenos Aires techniques to Mexico City
    \item \textbf{Independent Problem Solving:} Made appropriate data science decisions
    \item \textbf{Code Quality:} Wrote clean, functional machine learning code
    \item \textbf{Data Understanding:} Analyzed a new market and geography
    \item \textbf{Model Building:} Created effective prediction models
    \item \textbf{Feature Analysis:} Interpreted model results meaningfully
\end{enumerate}

\subsection{Key Achievements}
\begin{itemize}
    \item Successfully adapted to new dataset and constraints
    \item Built working ML pipeline for different city/country
    \item Achieved meaningful improvement over baseline performance
    \item Demonstrated understanding of feature importance
    \item Created deployable prediction model
\end{itemize}

\subsection{Professional Growth}
\begin{itemize}
    \item \textbf{Independence:} Worked without step-by-step guidance
    \item \textbf{Decision Making:} Chose appropriate techniques and parameters
    \item \textbf{Adaptability:} Modified approaches for new data
    \item \textbf{Quality Assurance:} Validated results and checked assumptions
\end{itemize}

\section{Practice Exercises and Next Steps}

\textbf{Additional Challenges:}
\begin{enumerate}
    \item Compare Mexico City vs Buenos Aires model performance
    \item Investigate which boroughs offer the best value for money
    \item Build a model that predicts which apartments are underpriced
    \item Create visualizations comparing the two real estate markets
\end{enumerate}

\textbf{Next Learning Steps:}
\begin{enumerate}
    \item Cross-validation and proper model evaluation
    \item Advanced algorithms (Random Forest, XGBoost)
    \item Feature engineering and polynomial features
    \item Time series analysis for price trends
    \item Deep learning for complex patterns
\end{enumerate}

Congratulations! You've successfully completed an independent machine learning assignment, demonstrating your ability to apply learned skills to new problems. This represents a significant milestone in your data science journey.

\section{Sample Solutions to Additional Challenges}

\subsection{Challenge 1: Mexico City vs Buenos Aires Model Comparison}

\begin{lstlisting}[caption=Cross-City Model Performance Analysis]
# Compare performance metrics between cities
print("CROSS-CITY MODEL COMPARISON")
print("=" * 35)

# Buenos Aires model metrics (from previous projects)
ba_baseline_mae = 50000  # Example baseline from Buenos Aires
ba_model_mae = 35000     # Example model performance from Buenos Aires
ba_improvement = ((ba_baseline_mae - ba_model_mae) / ba_baseline_mae) * 100

# Mexico City current metrics
mx_baseline_mae = baseline_mae
mx_model_mae = train_mae
mx_improvement = ((mx_baseline_mae - mx_model_mae) / mx_baseline_mae) * 100

print(f"BUENOS AIRES PERFORMANCE:")
print(f"  Baseline MAE: ${ba_baseline_mae:,.0f}")
print(f"  Model MAE: ${ba_model_mae:,.0f}")
print(f"  Improvement: {ba_improvement:.1f}%")

print(f"\nMEXICO CITY PERFORMANCE:")
print(f"  Baseline MAE: ${mx_baseline_mae:,.0f}")
print(f"  Model MAE: ${mx_model_mae:,.0f}")
print(f"  Improvement: {mx_improvement:.1f}%")

# Market characteristics comparison
print(f"\nMARKET CHARACTERISTICS:")
ba_avg_price = 180000    # Example from Buenos Aires
ba_avg_size = 85         # Example from Buenos Aires
ba_price_per_sqm = ba_avg_price / ba_avg_size

mx_avg_price = df['price_aprox_usd'].mean()
mx_avg_size = df['surface_covered_in_m2'].mean()
mx_price_per_sqm = mx_avg_price / mx_avg_size

print(f"Buenos Aires: ${ba_avg_price:,.0f} avg, {ba_avg_size:.0f}sqm, ${ba_price_per_sqm:,.0f}/sqm")
print(f"Mexico City:  ${mx_avg_price:,.0f} avg, {mx_avg_size:.0f}sqm, ${mx_price_per_sqm:,.0f}/sqm")

# Relative affordability
affordability_ratio = mx_avg_price / ba_avg_price
print(f"\nAffordability: Mexico City is {affordability_ratio:.1f}x the price of Buenos Aires")

# Visualization
fig, axes = plt.subplots(1, 3, figsize=(15, 5))

# Model performance comparison
cities = ['Buenos Aires', 'Mexico City']
baselines = [ba_baseline_mae, mx_baseline_mae]
models = [ba_model_mae, mx_model_mae]

x = range(len(cities))
axes[0].bar([i-0.2 for i in x], baselines, 0.4, label='Baseline', alpha=0.7)
axes[0].bar([i+0.2 for i in x], models, 0.4, label='Model', alpha=0.7)
axes[0].set_xticks(x)
axes[0].set_xticklabels(cities)
axes[0].set_ylabel('MAE (USD)')
axes[0].set_title('Model Performance Comparison')
axes[0].legend()
axes[0].grid(axis='y', alpha=0.3)

# Price comparison
avg_prices = [ba_avg_price, mx_avg_price]
axes[1].bar(cities, avg_prices, alpha=0.7, color=['blue', 'red'])
axes[1].set_ylabel('Average Price (USD)')
axes[1].set_title('Average Apartment Prices')
axes[1].grid(axis='y', alpha=0.3)

# Price per square meter
price_per_sqm_values = [ba_price_per_sqm, mx_price_per_sqm]
axes[2].bar(cities, price_per_sqm_values, alpha=0.7, color=['green', 'orange'])
axes[2].set_ylabel('Price per sq meter (USD)')
axes[2].set_title('Price Density Comparison')
axes[2].grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

print(f"\nKEY INSIGHTS:")
if mx_improvement > ba_improvement:
    print("- Mexico City model shows better relative improvement")
else:
    print("- Buenos Aires model shows better relative improvement")

if mx_price_per_sqm < ba_price_per_sqm:
    print("- Mexico City offers better value per square meter")
else:
    print("- Buenos Aires offers better value per square meter")
\end{lstlisting}

\subsection{Challenge 2: Best Value Borough Analysis}

\begin{lstlisting}[caption=Value-for-Money Borough Investigation]
# Analyze which boroughs offer the best value for money
print("BOROUGH VALUE ANALYSIS")
print("=" * 25)

# Calculate average price and size by borough
borough_stats = df.groupby('borough').agg({
    'price_aprox_usd': ['mean', 'median', 'count'],
    'surface_covered_in_m2': 'mean'
}).round(0)

# Flatten column names
borough_stats.columns = ['avg_price', 'median_price', 'count', 'avg_size']
borough_stats['price_per_sqm'] = borough_stats['avg_price'] / borough_stats['avg_size']

# Filter boroughs with sufficient data (at least 20 apartments)
borough_stats = borough_stats[borough_stats['count'] >= 20]
borough_stats = borough_stats.sort_values('price_per_sqm')

print(f"BEST VALUE BOROUGHS (Price per sq meter):")
print("-" * 45)
for i, (borough, row) in enumerate(borough_stats.head(10).iterrows(), 1):
    print(f"{i:2d}. {borough:20s}: ${row['price_per_sqm']:6.0f}/sqm ({row['count']:3.0f} apts)")

print(f"\nMOST EXPENSIVE BOROUGHS:")
print("-" * 30)
for i, (borough, row) in enumerate(borough_stats.tail(5).iterrows(), 1):
    print(f"{i:2d}. {borough:20s}: ${row['price_per_sqm']:6.0f}/sqm ({row['count']:3.0f} apts)")

# Find undervalued apartments in good boroughs
print(f"\nUNDERVALUED OPPORTUNITIES:")
print("-" * 30)

# Get predictions for all apartments
all_predictions = model.predict(X_train)
df_with_pred = df.copy()
df_with_pred['predicted_price'] = all_predictions
df_with_pred['value_score'] = (all_predictions - df['price_aprox_usd']) / df['price_aprox_usd']

# Find undervalued properties (predicted > actual by at least 15%)
undervalued = df_with_pred[df_with_pred['value_score'] > 0.15]
undervalued_sorted = undervalued.sort_values('value_score', ascending=False)

for i, row in undervalued_sorted.head(10).iterrows():
    actual = row['price_aprox_usd']
    predicted = row['predicted_price']
    value_score = row['value_score']
    print(f"{i+1:2d}. {row['borough']:15s}: ${actual:6.0f} actual, ${predicted:6.0f} predicted (+{value_score*100:4.1f}%)")

# Visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 1. Price per square meter by borough (top 15)
top_15_boroughs = borough_stats.head(15)
axes[0,0].barh(range(len(top_15_boroughs)), top_15_boroughs['price_per_sqm'])
axes[0,0].set_yticks(range(len(top_15_boroughs)))
axes[0,0].set_yticklabels(top_15_boroughs.index, fontsize=8)
axes[0,0].set_xlabel('Price per sq meter (USD)')
axes[0,0].set_title('Most Affordable Boroughs')
axes[0,0].grid(axis='x', alpha=0.3)

# 2. Average apartment size by borough
axes[0,1].bar(range(len(top_15_boroughs)), top_15_boroughs['avg_size'])
axes[0,1].set_xticks(range(len(top_15_boroughs)))
axes[0,1].set_xticklabels(top_15_boroughs.index, rotation=45, ha='right', fontsize=8)
axes[0,1].set_ylabel('Average Size (sq meters)')
axes[0,1].set_title('Average Apartment Size')
axes[0,1].grid(axis='y', alpha=0.3)

# 3. Price vs Size scatter colored by borough value rank
price_vs_size = df.merge(borough_stats[['price_per_sqm']], left_on='borough', right_index=True)
scatter = axes[1,0].scatter(price_vs_size['surface_covered_in_m2'], 
                           price_vs_size['price_aprox_usd'],
                           c=price_vs_size['price_per_sqm'], 
                           cmap='coolwarm_r', alpha=0.6, s=20)
axes[1,0].set_xlabel('Size (sq meters)')
axes[1,0].set_ylabel('Price (USD)')
axes[1,0].set_title('Price vs Size (colored by borough value)')
plt.colorbar(scatter, ax=axes[1,0], label='Price/sqm')

# 4. Investment opportunity score distribution
axes[1,1].hist(df_with_pred['value_score'], bins=30, alpha=0.7, edgecolor='black')
axes[1,1].axvline(x=0, color='red', linestyle='--', label='Fair value')
axes[1,1].axvline(x=0.15, color='green', linestyle='--', label='15% undervalued')
axes[1,1].set_xlabel('Value Score (predicted/actual - 1)')
axes[1,1].set_ylabel('Count')
axes[1,1].set_title('Investment Opportunity Distribution')
axes[1,1].legend()
axes[1,1].grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

# Summary recommendations
best_value_borough = borough_stats.index[0]
most_undervalued = undervalued_sorted.iloc[0]

print(f"\nINVESTMENT RECOMMENDATIONS:")
print(f"Best value borough overall: {best_value_borough}")
print(f"Best individual opportunity: {most_undervalued['borough']} ")
print(f"  ${most_undervalued['price_aprox_usd']:,.0f} asking vs ${most_undervalued['predicted_price']:,.0f} predicted")
\end{lstlisting}

\subsection{Challenge 3: Underpriced Apartment Detection Model}

\begin{lstlisting}[caption=Binary Classification for Underpriced Properties]
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.metrics import roc_auc_score, roc_curve

print("UNDERPRICED APARTMENT DETECTION")
print("=" * 35)

# Create binary target: 1 if underpriced (predicted > actual by 10%), 0 otherwise
threshold = 0.10
y_underpriced = (df_with_pred['value_score'] > threshold).astype(int)

print(f"Classification target:")
print(f"  Underpriced apartments (>{threshold*100:.0f}% undervalued): {y_underpriced.sum()} ({y_underpriced.mean()*100:.1f}%)")
print(f"  Fairly priced apartments: {(~y_underpriced.astype(bool)).sum()} ({(1-y_underpriced.mean())*100:.1f}%)")

# Prepare features (remove price-related features to avoid leakage)
X_features = df[['surface_covered_in_m2', 'lat', 'lon', 'borough']].copy()

# Encode borough
from sklearn.preprocessing import LabelEncoder
le = LabelEncoder()
X_features_encoded = X_features.copy()
X_features_encoded['borough_encoded'] = le.fit_transform(X_features['borough'])
X_features_final = X_features_encoded[['surface_covered_in_m2', 'lat', 'lon', 'borough_encoded']]

# Train classification model
clf_model = LogisticRegression(random_state=42)
clf_model.fit(X_features_final, y_underpriced)

# Make predictions
y_pred_proba = clf_model.predict_proba(X_features_final)[:, 1]
y_pred_binary = clf_model.predict(X_features_final)

# Evaluate model
auc_score = roc_auc_score(y_underpriced, y_pred_proba)
print(f"\nCLASSIFICATION PERFORMANCE:")
print(f"AUC Score: {auc_score:.3f}")

print(f"\nClassification Report:")
print(classification_report(y_underpriced, y_pred_binary))

# Feature importance for classification
feature_names = ['Size', 'Latitude', 'Longitude', 'Borough']
coefficients = clf_model.coef_[0]

print(f"\nFEATURE IMPORTANCE FOR DETECTING UNDERPRICED APARTMENTS:")
for name, coef in zip(feature_names, coefficients):
    direction = "increases" if coef > 0 else "decreases"
    print(f"  {name}: {coef:.4f} ({direction} underpricing probability)")

# Find high-probability underpriced apartments
high_prob_underpriced = df[y_pred_proba > 0.7].copy()
high_prob_underpriced['underpriced_probability'] = y_pred_proba[y_pred_proba > 0.7]
high_prob_underpriced = high_prob_underpriced.sort_values('underpriced_probability', ascending=False)

print(f"\nHIGH PROBABILITY UNDERPRICED APARTMENTS:")
print("-" * 45)
for i, row in high_prob_underpriced.head(10).iterrows():
    prob = row['underpriced_probability']
    price = row['price_aprox_usd']
    size = row['surface_covered_in_m2']
    borough = row['borough']
    print(f"{i+1:2d}. {borough:15s}: ${price:6.0f}, {size:3.0f}sqm, {prob*100:5.1f}% probability")

# Visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# 1. ROC Curve
fpr, tpr, _ = roc_curve(y_underpriced, y_pred_proba)
axes[0,0].plot(fpr, tpr, label=f'ROC Curve (AUC = {auc_score:.3f})')
axes[0,0].plot([0, 1], [0, 1], 'k--', label='Random')
axes[0,0].set_xlabel('False Positive Rate')
axes[0,0].set_ylabel('True Positive Rate')
axes[0,0].set_title('ROC Curve for Underpriced Detection')
axes[0,0].legend()
axes[0,0].grid(True, alpha=0.3)

# 2. Probability distribution
axes[0,1].hist(y_pred_proba[y_underpriced == 0], bins=20, alpha=0.7, 
               label='Fairly priced', color='blue')
axes[0,1].hist(y_pred_proba[y_underpriced == 1], bins=20, alpha=0.7, 
               label='Actually underpriced', color='red')
axes[0,1].set_xlabel('Predicted Probability of Being Underpriced')
axes[0,1].set_ylabel('Count')
axes[0,1].set_title('Probability Distribution by True Class')
axes[0,1].legend()
axes[0,1].grid(axis='y', alpha=0.3)

# 3. Confusion Matrix
cm = confusion_matrix(y_underpriced, y_pred_binary)
im = axes[1,0].imshow(cm, interpolation='nearest', cmap='Blues')
axes[1,0].set_title('Confusion Matrix')
tick_marks = range(2)
axes[1,0].set_xticks(tick_marks)
axes[1,0].set_yticks(tick_marks)
axes[1,0].set_xticklabels(['Fairly Priced', 'Underpriced'])
axes[1,0].set_yticklabels(['Fairly Priced', 'Underpriced'])
axes[1,0].set_ylabel('True Label')
axes[1,0].set_xlabel('Predicted Label')

# Add text annotations
thresh = cm.max() / 2
for i in range(2):
    for j in range(2):
        axes[1,0].text(j, i, format(cm[i, j], 'd'),
                      ha="center", va="center",
                      color="white" if cm[i, j] > thresh else "black")

# 4. Feature importance
axes[1,1].barh(range(len(feature_names)), coefficients)
axes[1,1].set_yticks(range(len(feature_names)))
axes[1,1].set_yticklabels(feature_names)
axes[1,1].set_xlabel('Coefficient')
axes[1,1].set_title('Feature Importance for Underpricing Detection')
axes[1,1].grid(axis='x', alpha=0.3)

plt.tight_layout()
plt.show()

# Business insights
print(f"\nBUSINESS INSIGHTS:")
print(f"- Model can identify underpriced apartments with {auc_score:.1%} accuracy")
print(f"- Found {(y_pred_proba > 0.7).sum()} high-confidence opportunities")
print(f"- Key factors: {feature_names[abs(coefficients).argmax()]} is most predictive")

if coefficients[0] > 0:
    print("- Larger apartments more likely to be underpriced")
else:
    print("- Smaller apartments more likely to be underpriced")
\end{lstlisting}

\subsection{Challenge 4: Mexico City vs Buenos Aires Market Visualization}

\begin{lstlisting}[caption=Comprehensive Market Comparison Dashboard]
# Create comprehensive comparison between Mexico City and Buenos Aires
print("COMPREHENSIVE MARKET COMPARISON")
print("=" * 35)

# Simulated Buenos Aires data for comparison (in real project, you'd load actual data)
np.random.seed(42)
ba_size = 2000
ba_data = {
    'price_aprox_usd': np.random.normal(180000, 60000, ba_size),
    'surface_covered_in_m2': np.random.normal(85, 25, ba_size),
    'borough': np.random.choice(['Palermo', 'Recoleta', 'San Telmo', 'Villa Crespo', 
                                'Belgrano', 'Caballito'], ba_size),
    'lat': np.random.normal(-34.61, 0.02, ba_size),
    'lon': np.random.normal(-58.38, 0.02, ba_size)
}
ba_df = pd.DataFrame(ba_data)
ba_df = ba_df[(ba_df['price_aprox_usd'] > 50000) & (ba_df['price_aprox_usd'] < 400000)]
ba_df = ba_df[(ba_df['surface_covered_in_m2'] > 30) & (ba_df['surface_covered_in_m2'] < 200)]

# Mexico City current data
mx_df = df.copy()

print(f"Market Comparison:")
print(f"Buenos Aires: {len(ba_df)} apartments")
print(f"Mexico City:  {len(mx_df)} apartments")

# Calculate key metrics for both cities
ba_metrics = {
    'avg_price': ba_df['price_aprox_usd'].mean(),
    'avg_size': ba_df['surface_covered_in_m2'].mean(),
    'price_per_sqm': (ba_df['price_aprox_usd'] / ba_df['surface_covered_in_m2']).mean(),
    'price_std': ba_df['price_aprox_usd'].std(),
    'size_std': ba_df['surface_covered_in_m2'].std()
}

mx_metrics = {
    'avg_price': mx_df['price_aprox_usd'].mean(),
    'avg_size': mx_df['surface_covered_in_m2'].mean(),
    'price_per_sqm': (mx_df['price_aprox_usd'] / mx_df['surface_covered_in_m2']).mean(),
    'price_std': mx_df['price_aprox_usd'].std(),
    'size_std': mx_df['surface_covered_in_m2'].std()
}

# Create comprehensive visualization dashboard
fig = plt.figure(figsize=(20, 15))

# 1. Price distributions
ax1 = plt.subplot(3, 4, 1)
plt.hist(ba_df['price_aprox_usd'], bins=30, alpha=0.7, label='Buenos Aires', color='blue')
plt.hist(mx_df['price_aprox_usd'], bins=30, alpha=0.7, label='Mexico City', color='red')
plt.xlabel('Price (USD)')
plt.ylabel('Count')
plt.title('Price Distributions')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 2. Size distributions
ax2 = plt.subplot(3, 4, 2)
plt.hist(ba_df['surface_covered_in_m2'], bins=30, alpha=0.7, label='Buenos Aires', color='blue')
plt.hist(mx_df['surface_covered_in_m2'], bins=30, alpha=0.7, label='Mexico City', color='red')
plt.xlabel('Size (sq meters)')
plt.ylabel('Count')
plt.title('Size Distributions')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 3. Price vs Size scatter
ax3 = plt.subplot(3, 4, 3)
plt.scatter(ba_df['surface_covered_in_m2'], ba_df['price_aprox_usd'], 
           alpha=0.5, s=10, label='Buenos Aires', color='blue')
plt.scatter(mx_df['surface_covered_in_m2'], mx_df['price_aprox_usd'], 
           alpha=0.5, s=10, label='Mexico City', color='red')
plt.xlabel('Size (sq meters)')
plt.ylabel('Price (USD)')
plt.title('Price vs Size')
plt.legend()
plt.grid(True, alpha=0.3)

# 4. Average metrics comparison
ax4 = plt.subplot(3, 4, 4)
metrics_comparison = pd.DataFrame({
    'Buenos Aires': [ba_metrics['avg_price'], ba_metrics['avg_size'], ba_metrics['price_per_sqm']],
    'Mexico City': [mx_metrics['avg_price'], mx_metrics['avg_size'], mx_metrics['price_per_sqm']]
}, index=['Avg Price', 'Avg Size', 'Price/sqm'])

metrics_comparison.plot(kind='bar', ax=ax4)
plt.title('Key Metrics Comparison')
plt.ylabel('Value')
plt.xticks(rotation=45)
plt.grid(axis='y', alpha=0.3)

# 5. Geographic spread (latitude)
ax5 = plt.subplot(3, 4, 5)
plt.hist(ba_df['lat'], bins=20, alpha=0.7, label='Buenos Aires', color='blue')
plt.hist(mx_df['lat'], bins=20, alpha=0.7, label='Mexico City', color='red')
plt.xlabel('Latitude')
plt.ylabel('Count')
plt.title('Geographic Spread (Latitude)')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 6. Geographic spread (longitude)
ax6 = plt.subplot(3, 4, 6)
plt.hist(ba_df['lon'], bins=20, alpha=0.7, label='Buenos Aires', color='blue')
plt.hist(mx_df['lon'], bins=20, alpha=0.7, label='Mexico City', color='red')
plt.xlabel('Longitude')
plt.ylabel('Count')
plt.title('Geographic Spread (Longitude)')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 7. Price per square meter distribution
ax7 = plt.subplot(3, 4, 7)
ba_price_per_sqm = ba_df['price_aprox_usd'] / ba_df['surface_covered_in_m2']
mx_price_per_sqm = mx_df['price_aprox_usd'] / mx_df['surface_covered_in_m2']
plt.hist(ba_price_per_sqm, bins=30, alpha=0.7, label='Buenos Aires', color='blue')
plt.hist(mx_price_per_sqm, bins=30, alpha=0.7, label='Mexico City', color='red')
plt.xlabel('Price per sq meter (USD)')
plt.ylabel('Count')
plt.title('Price Density Distribution')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 8. Borough/neighborhood diversity
ax8 = plt.subplot(3, 4, 8)
ba_borough_counts = ba_df['borough'].value_counts()
mx_borough_counts = mx_df['borough'].value_counts()
plt.bar(range(len(ba_borough_counts)), ba_borough_counts.values, 
        alpha=0.7, label=f'BA ({len(ba_borough_counts)} areas)', color='blue')
plt.bar(range(len(mx_borough_counts)), mx_borough_counts.values, 
        alpha=0.7, label=f'MX ({len(mx_borough_counts)} areas)', color='red')
plt.xlabel('Area Rank')
plt.ylabel('Apartment Count')
plt.title('Geographic Diversity')
plt.legend()
plt.grid(axis='y', alpha=0.3)

# 9. Market volatility (price coefficient of variation)
ax9 = plt.subplot(3, 4, 9)
ba_cv = ba_df['price_aprox_usd'].std() / ba_df['price_aprox_usd'].mean()
mx_cv = mx_df['price_aprox_usd'].std() / mx_df['price_aprox_usd'].mean()
plt.bar(['Buenos Aires', 'Mexico City'], [ba_cv, mx_cv], 
        color=['blue', 'red'], alpha=0.7)
plt.ylabel('Coefficient of Variation')
plt.title('Market Volatility')
plt.grid(axis='y', alpha=0.3)

# 10. Affordability index (median price as % of max)
ax10 = plt.subplot(3, 4, 10)
ba_affordability = ba_df['price_aprox_usd'].median() / ba_df['price_aprox_usd'].max()
mx_affordability = mx_df['price_aprox_usd'].median() / mx_df['price_aprox_usd'].max()
plt.bar(['Buenos Aires', 'Mexico City'], [ba_affordability, mx_affordability], 
        color=['blue', 'red'], alpha=0.7)
plt.ylabel('Affordability Index')
plt.title('Market Accessibility')
plt.grid(axis='y', alpha=0.3)

# 11. Size efficiency (average space per dollar)
ax11 = plt.subplot(3, 4, 11)
ba_efficiency = ba_df['surface_covered_in_m2'].mean() / ba_df['price_aprox_usd'].mean() * 1000
mx_efficiency = mx_df['surface_covered_in_m2'].mean() / mx_df['price_aprox_usd'].mean() * 1000
plt.bar(['Buenos Aires', 'Mexico City'], [ba_efficiency, mx_efficiency], 
        color=['blue', 'red'], alpha=0.7)
plt.ylabel('Sq meters per $1000')
plt.title('Space Efficiency')
plt.grid(axis='y', alpha=0.3)

# 12. Market summary table
ax12 = plt.subplot(3, 4, 12)
ax12.axis('tight')
ax12.axis('off')

summary_data = [
    ['Metric', 'Buenos Aires', 'Mexico City', 'Winner'],
    ['Avg Price', f'${ba_metrics["avg_price"]:,.0f}', f'${mx_metrics["avg_price"]:,.0f}', 
     'MX' if mx_metrics["avg_price"] < ba_metrics["avg_price"] else 'BA'],
    ['Avg Size', f'{ba_metrics["avg_size"]:.0f}sqm', f'{mx_metrics["avg_size"]:.0f}sqm',
     'MX' if mx_metrics["avg_size"] > ba_metrics["avg_size"] else 'BA'],
    ['Price/sqm', f'${ba_metrics["price_per_sqm"]:,.0f}', f'${mx_metrics["price_per_sqm"]:,.0f}',
     'MX' if mx_metrics["price_per_sqm"] < ba_metrics["price_per_sqm"] else 'BA'],
    ['Volatility', f'{ba_cv:.2f}', f'{mx_cv:.2f}',
     'MX' if mx_cv < ba_cv else 'BA'],
    ['Efficiency', f'{ba_efficiency:.1f}', f'{mx_efficiency:.1f}',
     'MX' if mx_efficiency > ba_efficiency else 'BA']
]

table = ax12.table(cellText=summary_data[1:], colLabels=summary_data[0],
                  cellLoc='center', loc='center')
table.auto_set_font_size(False)
table.set_fontsize(8)
table.scale(1.2, 1.5)
ax12.set_title('Market Comparison Summary')

plt.tight_layout()
plt.show()

# Print detailed analysis
print(f"\nDETAILED MARKET ANALYSIS:")
print(f"=" * 30)
print(f"AFFORDABILITY:")
print(f"  Buenos Aires: ${ba_metrics['avg_price']:,.0f} average")
print(f"  Mexico City:  ${mx_metrics['avg_price']:,.0f} average")
print(f"  Mexico City is {mx_metrics['avg_price']/ba_metrics['avg_price']:.1f}x the price")

print(f"\nSPACE VALUE:")
print(f"  Buenos Aires: ${ba_metrics['price_per_sqm']:,.0f} per sq meter")
print(f"  Mexico City:  ${mx_metrics['price_per_sqm']:,.0f} per sq meter")

print(f"\nMARKET CHARACTERISTICS:")
print(f"  Buenos Aires volatility: {ba_cv:.2f}")
print(f"  Mexico City volatility:  {mx_cv:.2f}")

if mx_cv < ba_cv:
    print("  Mexico City has a more stable market")
else:
    print("  Buenos Aires has a more stable market")
\end{lstlisting}

\section{Commentary on New Concepts and Learning Outcomes}

\subsection{Advanced Concepts Mastered in Project 2.5}

\begin{concept}
\textbf{Independent Problem Solving:}
This project introduced several critical data science skills:

\begin{itemize}
    \item \textbf{Dataset Adaptation:} Learning to modify techniques for new geographic and economic contexts
    \item \textbf{Cross-Market Analysis:} Comparing model performance across different cities and countries
    \item \textbf{Value Investment Analysis:} Using ML predictions to identify undervalued assets
    \item \textbf{Binary Classification:} Converting regression problems into classification for different business objectives
    \item \textbf{Market Intelligence:} Creating comprehensive analytical frameworks for real estate investment
\end{itemize}
\end{concept}

\subsection{Key Technical Innovations}

\begin{enumerate}
    \item \textbf{Multi-City Modeling Framework:} Demonstrated how to adapt models across different geographic markets while maintaining performance
    
    \item \textbf{Investment Opportunity Detection:} Created binary classification system to identify undervalued properties using regression predictions as features
    
    \item \textbf{Comprehensive Market Analysis:} Built analytical frameworks comparing multiple cities across various economic and geographic dimensions
    
    \item \textbf{Value Engineering:} Developed metrics for assessing real estate value that combine size, location, and price considerations
    
    \item \textbf{Uncertainty Quantification:} Used model predictions to assess investment risk and opportunity
\end{enumerate}

\subsection{Business Applications Learned}

\begin{itemize}
    \item \textbf{Real Estate Investment:} Systematic approach to identifying undervalued properties
    \item \textbf{Market Research:} Comparative analysis techniques for geographic market assessment
    \item \textbf{Risk Assessment:} Using statistical models to quantify investment opportunities
    \item \textbf{Portfolio Optimization:} Borough-level analysis for diversified real estate investment
    \item \textbf{Automated Screening:} ML-powered property evaluation systems
\end{itemize}

\subsection{Professional Skills Developed}

\begin{itemize}
    \item \textbf{Code Adaptability:} Modifying existing solutions for new requirements
    \item \textbf{Independent Research:} Working without step-by-step guidance
    \item \textbf{Business Insight Generation:} Translating technical results into actionable recommendations
    \item \textbf{Comparative Analysis:} Systematic evaluation of different markets and approaches
    \item \textbf{Presentation Skills:} Creating comprehensive analytical dashboards
\end{itemize}

This project represents a significant milestone in your data science journey - the transition from following tutorials to independent problem-solving and insight generation. These skills are directly applicable to professional data science roles in finance, real estate, consulting, and technology companies.

\end{document} 