Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
3.3. Autoregressive Models

import warnings
​
import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
from IPython.display import VimeoVideo
from pymongo import MongoClient
from sklearn.metrics import mean_absolute_error
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.tsa.ar_model import AutoReg
​
warnings.simplefilter(action="ignore", category=FutureWarning)
VimeoVideo("665851858", h="e39fc3d260", width=600)
Prepare Data
Import
VimeoVideo("665851852", h="16aa0a56e6", width=600)
Task 3.3.1: Complete to the create a client to connect to the MongoDB server, assigns the "air-quality" database to db, and assigned the "nairobi" connection to nairobi.

Create a client object for a MongoDB instance.
Access a database using PyMongo.
Access a collection in a database using PyMongo.
client = ...
db = ...
nairobi = ...
VimeoVideo("665851840", h="e048425f49", width=600)
Task 3.3.2: Change the wrangle function below so that it returns a Series of the resampled data instead of a DataFrame.

def wrangle(collection):
    results = collection.find(
        {"metadata.site": 29, "metadata.measurement": "P2"},
        projection={"P2": 1, "timestamp": 1, "_id": 0},
    )
​
    # Read data into DataFrame
    df = pd.DataFrame(list(results)).set_index("timestamp")
​
    # Localize timezone
    df.index = df.index.tz_localize("UTC").tz_convert("Africa/Nairobi")
​
    # Remove outliers
    df = df[df["P2"] < 500]
​
    # Resample to 1hr window
    y = df["P2"].resample("1H").mean().fillna(method='ffill').to_frame()
​
    return y
Task 3.3.3: Use your wrangle function to read the data from the nairobi collection into the Series y.

y = ...
y.head()
# Check your work
assert isinstance(y, pd.Series), f"`y` should be a Series, not type {type(y)}"
assert len(y) == 2928, f"`y` should have 2928 observations, not {len(y)}"
assert y.isnull().sum() == 0
Explore
VimeoVideo("665851830", h="85f58bc92b", width=600)
Task 3.3.4: Create an ACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient".

What's an ACF plot?
Create an ACF plot using statsmodels
fig, ax = plt.subplots(figsize=(15, 6))
​
plt.xlabel("Lag [hours]")
plt.ylabel("Correlation Coefficient");
VimeoVideo("665851811", h="ee3a2b5c24", width=600)
Task 3.3.5: Create an PACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient".

What's a PACF plot?
Create an PACF plot using statsmodels
fig, ax = plt.subplots(figsize=(15, 6))
​
plt.xlabel("Lag [hours]")
plt.ylabel("Correlation Coefficient");
Split
VimeoVideo("665851798", h="6c191cd94c", width=600)
Task 3.3.6: Split y into training and test sets. The first 95% of the data should be in your training set. The remaining 5% should be in the test set.

Divide data into training and test sets in pandas.
cutoff_test = ...
​
y_train = ...
y_test = ...
Build Model
Baseline
Task 3.3.7: Calculate the baseline mean absolute error for your model.

Calculate summary statistics for a DataFrame or Series in pandas.
y_train_mean = y_train.mean()
y_pred_baseline = [y_train_mean] * len(y_train)
mae_baseline = mean_absolute_error(y_train, y_pred_baseline)
​
print("Mean P2 Reading:", round(y_train_mean, 2))
print("Baseline MAE:", round(mae_baseline, 2))
Iterate
VimeoVideo("665851769", h="94a4296cde", width=600)
Task 3.3.8: Instantiate an AutoReg model and fit it to the training data y_train. Be sure to set the lags argument to 26.

What's an AR model?
Instantiate a predictor in statsmodels.
Train a model in statsmodels.
model = ...
VimeoVideo("665851746", h="1a4511e883", width=600)
Task 3.3.9: Generate a list of training predictions for your model and use them to calculate your training mean absolute error.

Generate in-sample predictions for a model in statsmodels.
Calculate the mean absolute error for a list of predictions in scikit-learn.
y_pred = ...
training_mae = ...
print("Training MAE:", training_mae)
VimeoVideo("665851744", h="60d053b455", width=600)
Task 3.3.10: Use y_train and y_pred to calculate the residuals for your model.

What's a residual?
Create new columns derived from existing columns in a DataFrame using pandas.
y_train_resid = ...
y_train_resid.tail()
VimeoVideo("665851712", h="9ff0cdba9c", width=600)
Task 3.3.11: Create a plot of y_train_resid.

Create a line plot using pandas.
fig, ax = plt.subplots(figsize=(15, 6))
​
VimeoVideo("665851702", h="b494adc297", width=600)
Task 3.3.12: Create a histogram of y_train_resid.

Create a histogram using plotly express.
​
VimeoVideo("665851684", h="d6d782a1f3", width=600)
Task 3.3.13: Create an ACF plot of y_train_resid.

What's an ACF plot?
Create an ACF plot using statsmodels
fig, ax = plt.subplots(figsize=(15, 6))
​
Evaluate
VimeoVideo("665851662", h="72e767e121", width=600)
Task 3.3.14: Calculate the test mean absolute error for your model.

Generate out-of-sample predictions using model in statsmodels.
Calculate the mean absolute error for a list of predictions in scikit-learn.
y_pred_test = ...
test_mae = ...
print("Test MAE:", test_mae)
Task 3.3.15: Create a DataFrame test_predictions that has two columns: "y_test" and "y_pred". The first should contain the true values for your test set, and the second should contain your model's predictions. Be sure the index of test_predictions matches the index of y_test.

Create a DataFrame from a dictionary using pandas.WQU WorldQuant University Applied Data Science Lab QQQQ
df_pred_test = pd.DataFrame(
    {"y_test": y_test, "y_pred": y_pred_test}, index=y_test.index
)
VimeoVideo("665851628", h="29b43e482e", width=600)
Task 3.3.16: Create a time series plot for the values in test_predictions using plotly express. Be sure that the y-axis is properly labeled as "P2".

Create a line plot in plotly express.
fig = px.line(df_pred_test, labels={"value": "P2"})
fig.show()
VimeoVideo("665851599", h="bb30d96e43", width=600)
Task 3.3.17: Perform walk-forward validation for your model for the entire test set y_test. Store your model's predictions in the Series y_pred_wfv.

What's walk-forward validation?
Perform walk-forward validation for time series model.
%%capture
​
y_pred_wfv = ...
history = ...
for i in range(len(y_test)):
    
    pass
VimeoVideo("665851568", h="a764ab5416", width=600)
Task 3.3.18: Calculate the test mean absolute error for your model.

Calculate the mean absolute error for a list of predictions in scikit-learn.
test_mae = ...
print("Test MAE (walk forward validation):", round(test_mae, 2))
Communicate Results
VimeoVideo("665851553", h="46338036cc", width=600)
Task 3.3.19: Print out the parameters for your trained model.

Access model parameters in statsmodels
​
VimeoVideo("665851529", h="39284d37ac", width=600)
Task 3.3.20: Put the values for y_test and y_pred_wfv into the DataFrame df_pred_test (don't forget the index). Then plot df_pred_test using plotly express.

Create a line plot in plotly express.
​
fig = ...
fig.show()
Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
7
Python 3 (ipykernel) | Idle
0
033-autoregressive-models.ipynb
Ln 1, Col 1
Mode: Command