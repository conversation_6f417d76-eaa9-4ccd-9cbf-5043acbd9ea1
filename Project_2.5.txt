Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
2.5. Predicting Apartment Prices in Mexico City 🇲🇽

import warnings
​
import wqet_grader
​
warnings.simplefilter(action="ignore", category=FutureWarning)
wqet_grader.init("Project 2 Assessment")
In this assignment, you'll decide which libraries you need to complete the tasks. You can import them in the cell below. 👇

# Import libraries here# Import libraries here
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.express as px
from glob import glob
​
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.metrics import mean_absolute_error
import warnings
warnings.filterwarnings('ignore')
​
Prepare Data
Import
Task 2.5.1: Write a wrangle function that takes the name of a CSV file as input and returns a DataFrame. The function should do the following steps:

Subset the data in the CSV file and return only apartments in Mexico City ("Distrito Federal") that cost less than $100,000.
Remove outliers by trimming the bottom and top 10% of properties in terms of "surface_covered_in_m2".
Create separate "lat" and "lon" columns.
Mexico City is divided into 15 boroughs. Create a "borough" feature from the "place_with_parent_names" column.
Drop columns that are more than 50% null values.
Drop columns containing low- or high-cardinality categorical values.
Drop any columns that would constitute leakage for the target "price_aprox_usd".
Drop any columns that would create issues of multicollinearity.
Tip: Don't try to satisfy all the criteria in the first version of your wrangle function. Instead, work iteratively. Start with the first criteria, test it out with one of the Mexico CSV files in the data/ directory, and submit it to the grader for feedback. Then add the next criteria.
def wrangle(filepath):
    import pandas as pd
​
    # Read CSV file
    df = pd.read_csv(filepath)
​
    # Subset: Apartments in Distrito Federal, price < 100000
    mask_mx = df["place_with_parent_names"].str.contains("Distrito Federal")
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 100_000
    df = df[mask_mx & mask_apt & mask_price]
​
    # Remove outliers in surface_covered_in_m2
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df =df[mask_area]
    # Split "lat-lon" into two columns
    if "lat-lon" in df.columns:
        df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
        df.drop(columns="lat-lon", inplace=True)
​
    # Extract borough from place_with_parent_names
    df["borough"] = df["place_with_parent_names"].str.split("|", expand=True)[1]
    df.drop(columns="place_with_parent_names", inplace=True)
    #Drop columns that are more than 50% null values.
    df.drop(columns=["surface_total_in_m2","price_usd_per_m2","floor","expenses"])
    #Drop columns containing low or high cardinality categorical values
    df.drop(columns=["operation","property_type","currency","properati_url"], inplace = True)
    #Drop any columns that would constitute leakage for the traget "price_aprox_usd" 
    df.drop(columns=["price", "price_aprox_local_currency", "price_per_m2", "price_usd_per_m2"], inplace=True)
    #Drop any columns that create issues multi collinearity
    df.drop(columns=["surface_total_in_m2","floor","rooms","expenses"],inplace =True)
    
    return df
​
# Use this cell to test your wrangle function and explore the data
​
​
wqet_grader.grade(
    "Project 2 Assessment", "Task 2.5.1", wrangle("data/mexico-city-real-estate-1.csv")
)
Good work!

Score: 1

Task 2.5.2: Use glob to create the list files. It should contain the filenames of all the Mexico City real estate CSVs in the ./data directory, except for mexico-city-test-features.csv.

files = glob("./data/mexico-city-real-estate-*.csv")
files = [f for f in files if "test-features" not in f]
files
['./data/mexico-city-real-estate-1.csv',
 './data/mexico-city-real-estate-5.csv',
 './data/mexico-city-real-estate-2.csv',
 './data/mexico-city-real-estate-4.csv',
 './data/mexico-city-real-estate-3.csv']
wqet_grader.grade("Project 2 Assessment", "Task 2.5.2", files)
Boom! You got it.

Score: 1

Task 2.5.3: Combine your wrangle function, a list comprehension, and pd.concat to create a DataFrame df. It should contain all the properties from the five CSVs in files.

frame = []
for i in files:
    df = wrangle(i)
    frame.append(df)
​
​
# In[75]:
​
​
df = pd.concat(frame)
print(df.info())
df.head()
<class 'pandas.core.frame.DataFrame'>
Int64Index: 5473 entries, 11 to 4622
Data columns (total 5 columns):
 #   Column                 Non-Null Count  Dtype  
---  ------                 --------------  -----  
 0   price_aprox_usd        5473 non-null   float64
 1   surface_covered_in_m2  5473 non-null   float64
 2   lat                    5149 non-null   float64
 3   lon                    5149 non-null   float64
 4   borough                5473 non-null   object 
dtypes: float64(4), object(1)
memory usage: 256.5+ KB
None
price_aprox_usd	surface_covered_in_m2	lat	lon	borough
11	94022.66	57.0	23.634501	-102.552788	Benito Juárez
20	70880.12	56.0	19.402413	-99.095391	Iztacalco
21	68228.99	80.0	19.357820	-99.149406	Benito Juárez
22	24235.78	60.0	19.504985	-99.208557	Azcapotzalco
26	94140.20	50.0	19.354219	-99.126244	Coyoacán
​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.3", df)
Excellent work.

Score: 1

Explore
Task 2.5.4: Create a histogram showing the distribution of apartment prices ("price_aprox_usd") in df. Be sure to label the x-axis "Price [$]", the y-axis "Count", and give it the title "Distribution of Apartment Prices". Use Matplotlib (plt).

What does the distribution of price look like? Is the data normal, a little skewed, or very skewed?

# Build histogram
plt.hist(df["price_aprox_usd"])
​
​
# Label axes
plt.xlabel("Area[sq meters]")
plt.ylabel("Count")
​
# Add title
plt.title("Distribution of Apartment sizes")
​
# Don't delete the code below 👇
plt.savefig("images/2-5-4.png", dpi=150)
​

with open("images/2-5-4.png", "rb") as file:
    wqet_grader.grade("Project 2 Assessment", "Task 2.5.4", file)
Way to go!

Score: 1

Task 2.5.5: Create a scatter plot that shows apartment price ("price_aprox_usd") as a function of apartment size ("surface_covered_in_m2"). Be sure to label your x-axis "Area [sq meters]" and y-axis "Price [USD]". Your plot should have the title "Mexico City: Price vs. Area". Use Matplotlib (plt).

# Plot price vs area
plt.scatter(x = df['surface_covered_in_m2'], y=df['price_aprox_usd'], )
plt.ylabel('Price [USD]')
plt.xlabel('Area [sq meters]')
plt.title('Mexico City: Price vs. Area')
​
​
# Don't delete the code below 👇
plt.savefig("images/2-5-5.png", dpi=150)
​

Do you see a relationship between price and area in the data? How is this similar to or different from the Buenos Aires dataset?WQU WorldQuant University Applied Data Science Lab QQQQ

with open("images/2-5-5.png", "rb") as file:
    wqet_grader.grade("Project 2 Assessment", "Task 2.5.5", file)
Yes! Your hard work is paying off.

Score: 1

Task 2.5.6: (UNGRADED) Create a Mapbox scatter plot that shows the location of the apartments in your dataset and represent their price using color.

What areas of the city seem to have higher real estate prices?

# Plot Mapbox location and price
fig = ...
​
​
fig.show()
Split
Task 2.5.7: Create your feature matrix X_train and target vector y_train. Your target is "price_aprox_usd". Your features should be all the columns that remain in the DataFrame you cleaned above.

# Split data into feature matrix `X_train` and target vector `y_train`.
​
feature = 'price_aprox_usd'
X_train = df[['surface_covered_in_m2', 'lat', 'lon', 'borough']]
y_train = df[feature]
​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.7a", X_train)
That's the right answer. Keep it up!

Score: 1

​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.7b", y_train)
Way to go!

Score: 1

Build Model
Baseline
Task 2.5.8: Calculate the baseline mean absolute error for your model.

y_mean = y_train.mean()
y_pred_baseline = [y_mean] * len(y_train)
baseline_mae = mean_absolute_error(y_train, y_pred_baseline)
print("Mean apt price:", y_mean)
print("Baseline MAE:", baseline_mae)
​
Mean apt price: 54246.5314982642
Baseline MAE: 17239.939475888295
wqet_grader.grade("Project 2 Assessment", "Task 2.5.8", [baseline_mae])
Excellent! Keep going.

Score: 1

Iterate
Task 2.5.9: Create a pipeline named model that contains all the transformers necessary for this dataset and one of the predictors you've used during this project. Then fit your model to the training data.

from category_encoders import OneHotEncoder
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.pipeline import make_pipeline
​
​
model = make_pipeline(
    OneHotEncoder(use_cat_names = True), 
    SimpleImputer(),
    Ridge()
)
model.fit(X_train, y_train)
Pipeline(steps=[('onehotencoder',
                 OneHotEncoder(cols=['borough'], use_cat_names=True)),
                ('simpleimputer', SimpleImputer()), ('ridge', Ridge())])
In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook.
On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.
​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.9", model)
Party time! 🎉🎉🎉

Score: 1

Evaluate
Task 2.5.10: Read the CSV file mexico-city-test-features.csv into the DataFrame X_test.

Tip: Make sure the X_train you used to train your model has the same column order as X_test. Otherwise, it may hurt your model's performance.
X_test =  pd.read_csv('./data/mexico-city-test-features.csv')
print(X_test.info())
X_test.head()
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 1041 entries, 0 to 1040
Data columns (total 4 columns):
 #   Column                 Non-Null Count  Dtype  
---  ------                 --------------  -----  
 0   surface_covered_in_m2  1041 non-null   float64
 1   lat                    986 non-null    float64
 2   lon                    986 non-null    float64
 3   borough                1041 non-null   object 
dtypes: float64(3), object(1)
memory usage: 32.7+ KB
None
surface_covered_in_m2	lat	lon	borough
0	60.0	19.493185	-99.205755	Azcapotzalco
1	55.0	19.307247	-99.166700	Coyoacán
2	50.0	19.363469	-99.010141	Iztapalapa
3	60.0	19.474655	-99.189277	Azcapotzalco
4	74.0	19.394628	-99.143842	Benito Juárez
​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.10", X_test)
That's the right answer. Keep it up!

Score: 1

Task 2.5.11: Use your model to generate a Series of predictions for X_test. When you submit your predictions to the grader, it will calculate the mean absolute error for your model.

y_test_pred = pd.Series(model.predict(X_test))
y_test_pred.head()
0    53538.366480
1    53171.988369
2    34263.884179
3    53488.425607
4    68738.924884
dtype: float64
wqet_grader.grade("Project 2 Assessment", "Task 2.5.11", y_test_pred)
Your model's mean absolute error is 14901.618. Excellent! Keep going.

Score: 1

Communicate Results
Task 2.5.12: Create a Series named feat_imp. The index should contain the names of all the features your model considers when making predictions; the values should be the coefficient values associated with each feature. The Series should be sorted ascending by absolute value.

coefficients = model.named_steps['ridge'].coef_
features = model.named_steps["onehotencoder"].get_feature_names_out()
feat_imp = pd.Series(coefficients, index=features).sort_values(key=abs)
feat_imp
surface_covered_in_m2               291.654156
borough_Cuauhtémoc                 -350.531990
borough_Iztacalco                   405.403127
lat                                 478.901375
borough_Xochimilco                  929.857400
borough_Miguel Hidalgo             1977.314718
borough_Azcapotzalco               2459.288646
lon                               -2492.221814
borough_Álvaro Obregón             3275.121061
borough_Coyoacán                   3737.561001
borough_Venustiano Carranza       -5609.918629
borough_La Magdalena Contreras    -5925.666450
borough_Gustavo A. Madero         -6637.429757
borough_Cuajimalpa de Morelos      9157.269123
borough_Tlalpan                   10319.429804
borough_Iztapalapa               -13349.017448
borough_Benito Juárez             13778.188880
borough_Tláhuac                  -14166.869486
dtype: float64
​
wqet_grader.grade("Project 2 Assessment", "Task 2.5.12", feat_imp)
Wow, you're making great progress.

Score: 1

Task 2.5.13: Create a horizontal bar chart that shows the 10 most influential coefficients for your model. Be sure to label your x- and y-axis "Importance [USD]" and "Feature", respectively, and give your chart the title "Feature Importances for Apartment Price". Use pandas.

plt.barh(features,feat_imp)
plt.title('Feature Importances for Apartment Price')
plt.ylabel('Feature')
plt.xlabel('Importance [USD]')
plt.show()
​
# Don't delete the code below 👇
plt.savefig("images/2-5-13.png", dpi=150)
​

<Figure size 640x480 with 0 Axes>
with open("images/2-5-13.png", "rb") as file:
    wqet_grader.grade("Project 2 Assessment", "Task 2.5.13", file)
Excellent! Keep going.

Score: 1

Copyright 2024 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
12
Python 3 (ipykernel) | Idle
0
025-assignment.ipynb
Ln 1, Col 1
Mode: Command