\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 1.1: Organizing Data}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 1.1: Organizing Tabular Data} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Your First Steps with Data in Python}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Welcome to Data Organization!}

Welcome to your first data science project! Today we'll learn how to organize and work with real estate data from Mexico. Think of this as learning to organize information in a filing cabinet - but using Python instead!

\subsection{What You'll Learn Today}
\begin{itemize}
    \item How to store data in Python lists and dictionaries
    \item Understanding what makes data "tabular" and "tidy"
    \item Basic calculations with real estate data
    \item Your first introduction to pandas DataFrames
    \item Organizing data for analysis
\end{itemize}

\begin{concept}
\textbf{What is Tabular Data?}
Tabular data is like a spreadsheet - it has rows and columns. Each row represents one thing (like a house), and each column represents a characteristic (like price or size).

Example: House data with price, area, and number of rooms organized in a table.
\end{concept}

\section{Task 1.1.1: Your First Python List}

Let's start by representing one house using a Python list. A list is like a shopping list - it stores multiple items in order.

\begin{lstlisting}[caption=Creating Your First House List]
# This list represents one house: [price, area, rooms]
house_0_list = [115910.26, 128, 4]

print("What type of object is this?", type(house_0_list))
print("How many items are in the list?", len(house_0_list))
print("The house data:", house_0_list)
\end{lstlisting}

\begin{learningtip}
\textbf{Understanding Lists:}
\begin{itemize}
    \item Lists use square brackets: [ ]
    \item Items are separated by commas
    \item Lists keep items in order (price first, area second, rooms third)
    \item We can store different types of data (numbers, text, etc.)
\end{itemize}
\end{learningtip}

\section{Task 1.1.2: Calculate Price per Square Meter}

Now let's do our first calculation! We'll find out how much this house costs per square meter.

\begin{lstlisting}[caption=Price per Square Meter Calculation]
# Calculate price per square meter
# Formula: total price divided by area
house_0_price_m2 = house_0_list[0] / house_0_list[1]

print("Price per square meter:", house_0_price_m2)
print("Type of result:", type(house_0_price_m2))

# Let's make it easier to read
print(f"This house costs ${house_0_price_m2:.2f} per square meter")
\end{lstlisting}

\begin{concept}
\textbf{List Indexing:}
Python starts counting from 0:
\begin{itemize}
    \item house\_0\_list[0] = first item (price)
    \item house\_0\_list[1] = second item (area) 
    \item house\_0\_list[2] = third item (rooms)
\end{itemize}
This is called "zero-based indexing" and is used throughout programming.
\end{concept}

\section{Task 1.1.3: Adding Data to Your List}

Let's add our calculated price per square meter to the house list.

\begin{lstlisting}[caption=Adding Data to List]
# Add price per square meter to the end of our list
house_0_list.append(house_0_price_m2)

print("Updated house list:")
print(house_0_list)
print("New length:", len(house_0_list))

# Now our list has: [price, area, rooms, price_per_m2]
print("Price:", house_0_list[0])
print("Area:", house_0_list[1]) 
print("Rooms:", house_0_list[2])
print("Price per m2:", house_0_list[3])
\end{lstlisting}

\begin{learningtip}
\textbf{List Methods:}
\begin{itemize}
    \item \texttt{append()} adds an item to the end of a list
    \item \texttt{len()} tells us how many items are in the list
    \item Lists can grow and shrink as needed
    \item Always remember: indexing starts at 0!
\end{itemize}
\end{learningtip}

\section{Task 1.1.4: Working with Multiple Houses}

Now let's work with data from multiple houses using a list of lists.

\begin{lstlisting}[caption=Multiple Houses Data]
# Create data for 5 houses
# Each inner list: [price, area, rooms]
houses_list = [
    [115910.26, 128, 4],
    [48718.17, 210, 3], 
    [28977.56, 58, 2],
    [36932.27, 79, 3],
    [83903.51, 111, 3]
]

print("Number of houses:", len(houses_list))
print("First house:", houses_list[0])
print("Last house:", houses_list[4])  # or houses_list[-1]

# Access specific data: second house's area
print("Second house area:", houses_list[1][1])
\end{lstlisting}

\begin{concept}
\textbf{Nested Lists:}
A list of lists is like a table:
\begin{itemize}
    \item houses\_list[0] = first house (entire row)
    \item houses\_list[1][2] = second house, third column (rooms)
    \item Think: [row][column] to access specific data
\end{itemize}
\end{concept}

\section{Task 1.1.5: Calculate Statistics for All Houses}

Let's calculate the average price across all houses using a loop.

\begin{lstlisting}[caption=Calculating Average Price]
# Method 1: Using a for loop
total_price = 0
for house in houses_list:
    total_price = total_price + house[0]  # Add each house's price

average_price = total_price / len(houses_list)
print(f"Average price: ${average_price:,.2f}")

# Method 2: Using list comprehension (more advanced)
prices = [house[0] for house in houses_list]
print("All prices:", prices)
print(f"Average price (method 2): ${sum(prices)/len(prices):,.2f}")
\end{lstlisting}

\begin{learningtip}
\textbf{Loops and Calculations:}
\begin{itemize}
    \item \texttt{for house in houses\_list:} goes through each house one by one
    \item We accumulate (add up) values using \texttt{total = total + value}
    \item List comprehension \texttt{[house[0] for house in houses\_list]} creates a new list
    \item \texttt{sum()} adds all numbers in a list
\end{itemize}
\end{learningtip}

\section{Task 1.1.6: Introduction to Dictionaries}

Lists are great, but sometimes we want to label our data. That's where dictionaries come in!

\begin{lstlisting}[caption=Your First Dictionary]
# Dictionary uses labels (keys) for each piece of data
house_0_dict = {
    "price_usd": 115910.26,
    "area_m2": 128,
    "rooms": 4
}

print("House dictionary:", house_0_dict)
print("Price:", house_0_dict["price_usd"])
print("Area:", house_0_dict["area_m2"])
print("Rooms:", house_0_dict["rooms"])

# Calculate price per square meter
price_per_m2 = house_0_dict["price_usd"] / house_0_dict["area_m2"]
print(f"Price per m2: ${price_per_m2:.2f}")
\end{lstlisting}

\begin{concept}
\textbf{Dictionaries vs Lists:}
\begin{itemize}
    \item \textbf{Lists:} Use numbers to access items (house[0])
    \item \textbf{Dictionaries:} Use descriptive names (house["price"])
    \item Dictionaries make code easier to read and understand
    \item Use curly braces \{ \} and key: value pairs
\end{itemize}
\end{concept}

\section{Task 1.1.7: List of Dictionaries}

The most powerful approach: combine lists and dictionaries!

\begin{lstlisting}[caption=List of Dictionaries]
# Create a list where each house is a dictionary
houses_dicts = [
    {"price_usd": 115910.26, "area_m2": 128, "rooms": 4},
    {"price_usd": 48718.17, "area_m2": 210, "rooms": 3},
    {"price_usd": 28977.56, "area_m2": 58, "rooms": 2},
    {"price_usd": 36932.27, "area_m2": 79, "rooms": 3},
    {"price_usd": 83903.51, "area_m2": 111, "rooms": 3}
]

print("Number of houses:", len(houses_dicts))
print("First house:", houses_dicts[0])

# Access specific data: third house's number of rooms
print("Third house rooms:", houses_dicts[2]["rooms"])

# Calculate average area
total_area = 0
for house in houses_dicts:
    total_area += house["area_m2"]

average_area = total_area / len(houses_dicts)
print(f"Average area: {average_area:.1f} square meters")
\end{lstlisting}

\begin{learningtip}
\textbf{Why Use Dictionaries in Lists?}
\begin{itemize}
    \item Self-documenting: code explains what each number means
    \item Flexible: easy to add new characteristics
    \item Readable: house["price"] is clearer than house[0]
    \item Professional: this is how real data scientists organize data
\end{itemize}
\end{learningtip}

\section{Task 1.1.8: Your First pandas DataFrame}

Now let's see how pandas makes working with tabular data even easier!

\begin{lstlisting}[caption=Creating a DataFrame]
import pandas as pd

# Convert our list of dictionaries to a DataFrame
df = pd.DataFrame(houses_dicts)

print("DataFrame:")
print(df)
print("\nDataFrame info:")
print(f"Shape: {df.shape}")  # (rows, columns)
print(f"Columns: {list(df.columns)}")
\end{lstlisting}

\begin{concept}
\textbf{What is a DataFrame?}
A DataFrame is like a super-powered spreadsheet:
\begin{itemize}
    \item Rows represent individual observations (houses)
    \item Columns represent features (price, area, rooms)
    \item Each column can have a different data type
    \item Built-in functions for calculations and analysis
\end{itemize}
\end{concept}

\section{Task 1.1.9: Basic DataFrame Operations}

Let's explore what we can do with our DataFrame.

\begin{lstlisting}[caption=DataFrame Calculations]
# Basic statistics
print("Basic statistics:")
print(df.describe())

# Calculate new columns
df["price_per_m2"] = df["price_usd"] / df["area_m2"]
print("\nDataFrame with price per m2:")
print(df)

# Find houses with more than 3 rooms
big_houses = df[df["rooms"] > 3]
print(f"\nHouses with more than 3 rooms:")
print(big_houses)

# Calculate average price by number of rooms
avg_by_rooms = df.groupby("rooms")["price_usd"].mean()
print("\nAverage price by room count:")
print(avg_by_rooms)
\end{lstlisting}

\begin{learningtip}
\textbf{DataFrame Superpowers:}
\begin{itemize}
    \item \texttt{describe()} gives instant statistics
    \item Create new columns with simple formulas
    \item Filter data with conditions like \texttt{df["rooms"] > 3}
    \item \texttt{groupby()} organizes data by categories
    \item Much faster than writing loops for calculations
\end{itemize}
\end{learningtip}

\section{Task 1.1.10: Tidy Data Principles}

Let's learn what makes data "tidy" and well-organized.

\begin{lstlisting}[caption=Understanding Tidy Data]
# Our current DataFrame follows tidy data principles
print("Our tidy DataFrame:")
print(df)

# Tidy data rules:
print("\nTidy Data Principles:")
print("1. Each row = one observation (one house)")
print("2. Each column = one variable (price, area, etc.)")
print("3. Each cell = one value")

# Let's verify our data is tidy
print(f"\nRows (observations): {df.shape[0]}")
print(f"Columns (variables): {df.shape[1]}")
print("Variable names:", list(df.columns))
\end{lstlisting}

\begin{concept}
\textbf{Tidy Data Benefits:}
\begin{itemize}
    \item Consistent structure makes analysis easier
    \item Works well with pandas and other tools
    \item Easy to add new observations (rows)
    \item Easy to add new variables (columns)
    \item Standard format used across data science
\end{itemize}
\end{concept}

\section{Summary: What We Accomplished}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Python Lists:} Store multiple items in order
    \item \textbf{List Indexing:} Access specific items using numbers
    \item \textbf{Dictionaries:} Store data with descriptive labels
    \item \textbf{Loops:} Repeat operations across multiple items
    \item \textbf{pandas DataFrames:} Professional data organization
    \item \textbf{Basic Calculations:} Compute new variables from existing data
\end{enumerate}

\subsection{Key Concepts}
\begin{itemize}
    \item \textbf{Tabular Data:} Information organized in rows and columns
    \item \textbf{Tidy Data:} Each row = observation, each column = variable
    \item \textbf{Data Types:} Numbers, text, and their Python representations
    \item \textbf{Indexing:} Python starts counting from 0
    \item \textbf{Data Structures:} Choose the right tool for the job
\end{itemize}

\subsection{Real-World Applications}
\begin{itemize}
    \item Organizing customer data for a business
    \item Managing product inventories
    \item Tracking student grades and performance
    \item Analyzing sales data over time
    \item Any situation where you have multiple related pieces of information
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Create a list of your top 5 favorite movies and print each one with its position number
    \item Build a dictionary representing a student with keys: name, age, major, gpa
    \item Create a simple DataFrame with 3 rows of student data and display it
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Write a function that takes a list of numbers and returns only the even numbers
    \item Create a dictionary of dictionaries representing a small class roster (3 students)
    \item Build a DataFrame from a list of dictionaries and calculate the average GPA
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Create a function that analyzes a list of grades and returns statistics (mean, max, min, count)
    \item Build a mini student management system using dictionaries and functions
    \item Import data from a CSV file and perform basic analysis using pandas
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Top 5 favorite movies with position numbers}
\begin{lstlisting}[caption=Movies List with Positions]
# Create list of favorite movies
favorite_movies = [
    "The Matrix",
    "Inception", 
    "Interstellar",
    "The Godfather",
    "Pulp Fiction"
]

# Print each movie with its position
print("My Top 5 Favorite Movies:")
for i, movie in enumerate(favorite_movies, 1):
    print(f"{i}. {movie}")

# Alternative approach using range
print("\nAlternative approach:")
for i in range(len(favorite_movies)):
    print(f"{i+1}. {favorite_movies[i]}")
\end{lstlisting}

\textbf{Exercise 2: Student dictionary}
\begin{lstlisting}[caption=Creating Student Dictionary]
# Create a dictionary representing a student
student = {
    "name": "Alice Johnson",
    "age": 20,
    "major": "Computer Science", 
    "gpa": 3.75
}

# Display the student information
print("Student Information:")
for key, value in student.items():
    print(f"{key.capitalize()}: {value}")

# Access specific information
print(f"\n{student['name']} is studying {student['major']}")
print(f"Current GPA: {student['gpa']}")
\end{lstlisting}

\textbf{Exercise 3: Simple student DataFrame}
\begin{lstlisting}[caption=Creating Student DataFrame]
import pandas as pd

# Create student data
student_data = {
    "Name": ["Alice Johnson", "Bob Smith", "Carol Davis"],
    "Age": [20, 19, 21],
    "Major": ["Computer Science", "Mathematics", "Physics"],
    "GPA": [3.75, 3.42, 3.89]
}

# Create DataFrame
students_df = pd.DataFrame(student_data)

# Display the DataFrame
print("Student Records:")
print(students_df)

# Basic information about the DataFrame
print(f"\nDataFrame Info:")
print(f"Shape: {students_df.shape}")
print(f"Columns: {list(students_df.columns)}")
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Function to filter even numbers}
\begin{lstlisting}[caption=Even Numbers Filter Function]
def get_even_numbers(numbers):
    """
    Takes a list of numbers and returns only the even numbers
    """
    even_numbers = []
    for num in numbers:
        if num % 2 == 0:
            even_numbers.append(num)
    return even_numbers

# Alternative using list comprehension
def get_even_numbers_short(numbers):
    return [num for num in numbers if num % 2 == 0]

# Test the function
test_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
even_nums = get_even_numbers(test_numbers)

print(f"Original numbers: {test_numbers}")
print(f"Even numbers: {even_nums}")

# Test with random numbers
import random
random_numbers = [random.randint(1, 20) for _ in range(10)]
print(f"\nRandom numbers: {random_numbers}")
print(f"Even from random: {get_even_numbers(random_numbers)}")
\end{lstlisting}

\textbf{Exercise 2: Class roster dictionary}
\begin{lstlisting}[caption=Class Roster Dictionary]
# Create class roster using nested dictionaries
class_roster = {
    "student_001": {
        "name": "Alice Johnson",
        "age": 20,
        "major": "Computer Science",
        "gpa": 3.75,
        "year": "Sophomore"
    },
    "student_002": {
        "name": "Bob Smith", 
        "age": 19,
        "major": "Mathematics",
        "gpa": 3.42,
        "year": "Freshman"
    },
    "student_003": {
        "name": "Carol Davis",
        "age": 21,
        "major": "Physics", 
        "gpa": 3.89,
        "year": "Junior"
    }
}

# Display the class roster
print("CLASS ROSTER")
print("=" * 40)
for student_id, info in class_roster.items():
    print(f"\n{student_id.upper()}:")
    for key, value in info.items():
        print(f"  {key.capitalize()}: {value}")

# Find student with highest GPA
highest_gpa_student = None
highest_gpa = 0

for student_id, info in class_roster.items():
    if info['gpa'] > highest_gpa:
        highest_gpa = info['gpa']
        highest_gpa_student = info['name']

print(f"\nHighest GPA: {highest_gpa_student} with {highest_gpa}")
\end{lstlisting}

\textbf{Exercise 3: DataFrame from dictionaries with GPA analysis}
\begin{lstlisting}[caption=DataFrame Analysis]
import pandas as pd

# Create list of student dictionaries
students_list = [
    {"name": "Alice Johnson", "age": 20, "major": "Computer Science", "gpa": 3.75},
    {"name": "Bob Smith", "age": 19, "major": "Mathematics", "gpa": 3.42},
    {"name": "Carol Davis", "age": 21, "major": "Physics", "gpa": 3.89},
    {"name": "David Wilson", "age": 20, "major": "Chemistry", "gpa": 3.56},
    {"name": "Eva Brown", "age": 22, "major": "Biology", "gpa": 3.91}
]

# Create DataFrame
df = pd.DataFrame(students_list)

print("Student DataFrame:")
print(df)

# Calculate statistics
average_gpa = df['gpa'].mean()
highest_gpa = df['gpa'].max()
lowest_gpa = df['gpa'].min()
median_gpa = df['gpa'].median()

print(f"\nGPA Statistics:")
print(f"Average GPA: {average_gpa:.2f}")
print(f"Highest GPA: {highest_gpa:.2f}")
print(f"Lowest GPA: {lowest_gpa:.2f}")
print(f"Median GPA: {median_gpa:.2f}")

# Find students above average
above_average = df[df['gpa'] > average_gpa]
print(f"\nStudents above average GPA:")
print(above_average[['name', 'gpa']])
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Grade analysis function}
\begin{lstlisting}[caption=Comprehensive Grade Statistics]
def analyze_grades(grades):
    """
    Analyzes a list of grades and returns comprehensive statistics
    """
    if not grades:
        return "No grades provided"
    
    # Calculate basic statistics
    total_grades = len(grades)
    sum_grades = sum(grades)
    mean_grade = sum_grades / total_grades
    max_grade = max(grades)
    min_grade = min(grades)
    
    # Calculate median
    sorted_grades = sorted(grades)
    if total_grades % 2 == 0:
        median = (sorted_grades[total_grades//2 - 1] + sorted_grades[total_grades//2]) / 2
    else:
        median = sorted_grades[total_grades//2]
    
    # Grade distribution
    a_grades = len([g for g in grades if g >= 90])
    b_grades = len([g for g in grades if 80 <= g < 90])
    c_grades = len([g for g in grades if 70 <= g < 80])
    d_grades = len([g for g in grades if 60 <= g < 70])
    f_grades = len([g for g in grades if g < 60])
    
    # Return comprehensive statistics
    return {
        "count": total_grades,
        "mean": round(mean_grade, 2),
        "median": median,
        "max": max_grade,
        "min": min_grade,
        "range": max_grade - min_grade,
        "distribution": {
            "A (90-100)": a_grades,
            "B (80-89)": b_grades, 
            "C (70-79)": c_grades,
            "D (60-69)": d_grades,
            "F (0-59)": f_grades
        }
    }

# Test the function
test_grades = [88, 92, 76, 84, 91, 73, 89, 95, 82, 79, 87, 94]
stats = analyze_grades(test_grades)

print("GRADE ANALYSIS REPORT")
print("=" * 30)
print(f"Total Grades: {stats['count']}")
print(f"Average: {stats['mean']}")
print(f"Median: {stats['median']}")
print(f"Highest: {stats['max']}")
print(f"Lowest: {stats['min']}")
print(f"Range: {stats['range']}")

print("\nGrade Distribution:")
for grade_level, count in stats['distribution'].items():
    percentage = (count / stats['count']) * 100
    print(f"  {grade_level}: {count} students ({percentage:.1f}%)")
\end{lstlisting}

\textbf{Exercise 2: Mini student management system}
\begin{lstlisting}[caption=Student Management System]
class StudentManager:
    """
    A simple student management system using dictionaries
    """
    
    def __init__(self):
        self.students = {}
        self.next_id = 1
    
    def add_student(self, name, age, major, gpa):
        """Add a new student to the system"""
        student_id = f"STU{self.next_id:03d}"
        self.students[student_id] = {
            "name": name,
            "age": age,
            "major": major,
            "gpa": gpa
        }
        self.next_id += 1
        return student_id
    
    def get_student(self, student_id):
        """Get student information by ID"""
        return self.students.get(student_id, "Student not found")
    
    def update_gpa(self, student_id, new_gpa):
        """Update a student's GPA"""
        if student_id in self.students:
            self.students[student_id]["gpa"] = new_gpa
            return f"Updated GPA for {self.students[student_id]['name']}"
        return "Student not found"
    
    def get_students_by_major(self, major):
        """Get all students in a specific major"""
        return {sid: info for sid, info in self.students.items() 
                if info["major"].lower() == major.lower()}
    
    def calculate_average_gpa(self):
        """Calculate average GPA across all students"""
        if not self.students:
            return 0
        total_gpa = sum(info["gpa"] for info in self.students.values())
        return total_gpa / len(self.students)
    
    def list_all_students(self):
        """List all students in the system"""
        return self.students

# Example usage
manager = StudentManager()

# Add students
id1 = manager.add_student("Alice Johnson", 20, "Computer Science", 3.75)
id2 = manager.add_student("Bob Smith", 19, "Computer Science", 3.42)
id3 = manager.add_student("Carol Davis", 21, "Physics", 3.89)

print("STUDENT MANAGEMENT SYSTEM")
print("=" * 35)

# List all students
print("All Students:")
for student_id, info in manager.list_all_students().items():
    print(f"  {student_id}: {info['name']} - {info['major']} (GPA: {info['gpa']})")

# Get students by major
cs_students = manager.get_students_by_major("Computer Science")
print(f"\nComputer Science Students: {len(cs_students)}")
for sid, info in cs_students.items():
    print(f"  {info['name']} (GPA: {info['gpa']})")

# Calculate average GPA
avg_gpa = manager.calculate_average_gpa()
print(f"\nAverage GPA across all students: {avg_gpa:.2f}")

# Update a student's GPA
print(f"\n{manager.update_gpa(id1, 3.85)}")
print(f"New average GPA: {manager.calculate_average_gpa():.2f}")
\end{lstlisting}

\section{Next Steps in Your Data Journey}

\textbf{Coming up in future projects:}
\begin{itemize}
    \item \textbf{Project 1.2:} Data preparation and cleaning techniques
    \item \textbf{Project 1.3:} Exploratory data analysis and visualization
    \item \textbf{Advanced topics:} Working with real-world messy data
    \item \textbf{Data visualization:} Creating charts and graphs
\end{itemize}

Congratulations! You've taken your first steps into the world of data organization and analysis. You now understand the fundamental building blocks that all data science is built upon.

\section{Commentary on New Concepts and Learning Outcomes}

\subsection{Fundamental Concepts Mastered in Project 1.1}

\begin{concept}
\textbf{Foundation Building:}
This project established critical programming foundations for data science:

\begin{itemize}
    \item \textbf{Data Structures:} Understanding lists, dictionaries, and DataFrames as core data containers
    \item \textbf{Data Types:} Working with strings, numbers, and mixed data types
    \item \textbf{Function Design:} Creating reusable code blocks for data processing
    \item \textbf{Conditional Logic:} Implementing decision-making in data analysis
    \item \textbf{Iteration Patterns:} Processing collections of data systematically
\end{itemize}
\end{concept}

\subsection{Key Technical Innovations Introduced}

\begin{enumerate}
    \item \textbf{List Comprehensions:} Elegant, Pythonic way to transform and filter data collections
    \begin{lstlisting}[caption=List Comprehension Power]
# Traditional approach
even_numbers = []
for num in numbers:
    if num % 2 == 0:
        even_numbers.append(num)

# Pythonic approach (list comprehension)
even_numbers = [num for num in numbers if num % 2 == 0]
    \end{lstlisting}
    
    \item \textbf{Dictionary Manipulation:} Using key-value pairs for structured data representation
    
    \item \textbf{Nested Data Structures:} Building complex data organizations using dictionaries within dictionaries
    
    \item \textbf{pandas Integration:} Converting between Python data structures and DataFrames
    
    \item \textbf{Statistical Computing:} Calculating descriptive statistics from data collections
\end{enumerate}

\subsection{Problem-Solving Patterns Learned}

\begin{itemize}
    \item \textbf{Data Collection:} Gathering and organizing information systematically
    \item \textbf{Data Validation:} Checking data quality and handling edge cases
    \item \textbf{Data Transformation:} Converting data from one format to another
    \item \textbf{Statistical Analysis:} Computing meaningful metrics from raw data
    \item \textbf{Report Generation:} Presenting results in clear, structured formats
\end{itemize}

\subsection{Professional Skills Developed}

\begin{itemize}
    \item \textbf{Code Organization:} Writing clean, readable, and maintainable functions
    \item \textbf{Documentation:} Using meaningful variable names and comments
    \item \textbf{Error Handling:} Anticipating and managing potential issues
    \item \textbf{Testing Mindset:} Verifying functions work correctly with example data
    \item \textbf{Modularity:} Creating reusable components for data processing
\end{itemize}

\subsection{Real-World Applications}

The concepts in this project directly apply to:
\begin{itemize}
    \item \textbf{Business Analytics:} Customer data analysis and reporting
    \item \textbf{Academic Research:} Student performance analysis and grade management
    \item \textbf{Data Engineering:} Building data processing pipelines
    \item \textbf{Quality Assurance:} Automated data validation and testing
    \item \textbf{System Administration:} Managing user accounts and permissions
\end{itemize}

\subsection{Cognitive Skills Enhanced}

\begin{itemize}
    \item \textbf{Logical Thinking:} Breaking complex problems into manageable steps
    \item \textbf{Pattern Recognition:} Identifying common data structures and operations
    \item \textbf{Abstraction:} Creating general solutions from specific examples
    \item \textbf{Systematic Approach:} Following consistent methodologies for problem-solving
    \item \textbf{Attention to Detail:} Ensuring accuracy in data handling and calculations
\end{itemize}

\subsection{Preparation for Advanced Topics}

This foundational project prepares you for:
\begin{itemize}
    \item \textbf{Data Cleaning:} Handling missing values, outliers, and inconsistencies
    \item \textbf{Exploratory Data Analysis:} Discovering patterns and relationships in data
    \item \textbf{Statistical Modeling:} Building predictive models from data
    \item \textbf{Machine Learning:} Implementing algorithms for pattern recognition
    \item \textbf{Data Visualization:} Creating compelling charts and graphs
\end{itemize}

The skills you've learned in organizing, manipulating, and analyzing data using Python's core data structures form the bedrock of all advanced data science techniques. Every complex machine learning model, every sophisticated analysis, and every insightful visualization starts with these fundamental operations you've now mastered.

\end{document} 