{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 3.5. Air Quality in Dar es Salaam 🇹🇿\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "import wqet_grader\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "wqet_grader.init(\"Project 3 Assessment\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries here\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px\n", "from pymongo import MongoClient\n", "from sklearn.metrics import mean_absolute_error\n", "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf\n", "from statsmodels.tsa.ar_model import AutoReg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Connect"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.1: Connect to MongoDB\n", "\n", "Connect to MongoDB server running at host \"localhost\" on port 27017. Then connect to the \"air-quality\" database and assign the collection for Dar es Salaam to the variable name dar."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = MongoClient(host=\"localhost\", port=27017)\n", "db = client[\"air-quality\"]\n", "dar = db[\"dar-es-salaam\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.1\", [dar.name])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Explore"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.2: Find Sensor Sites\n", "\n", "Determine the numbers assigned to all the sensor sites in the Dar es Salaam collection. Your submission should be a list of integers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sites = dar.distinct(\"metadata.site\")\n", "sites"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.2\", sites)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.3: Find Site with Most Readings\n", "\n", "Determine which site in the Dar es Salaam collection has the most sensor readings (of any type, not just PM2.5 readings). You submission readings_per_site should be a list of dictionaries that follows this format:\n", "\n", "[{'_id': 6, 'count': 70360}, {'_id': 29, 'count': 131852}]\n", "\n", "Note that the values here ☝️ are from the Nairobi collection, so your values will look different."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result = dar.aggregate([\n", "    {\"$group\": {\"_id\": \"$metadata.site\", \"count\": {\"$count\": {}}}},\n", "    {\"$sort\": {\"count\": -1}}\n", "])\n", "readings_per_site = list(result)\n", "readings_per_site"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.3\", readings_per_site)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Import"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.4: Create Wrangle Function\n", "\n", "Create a wrangle function that will extract the PM2.5 readings from the site that has the most total readings in the Dar es Salaam collection. Your function should do the following steps:\n", "\n", "1. Localize reading time stamps to the timezone for \"Africa/Dar_es_Salaam\".\n", "2. Remove all outlier PM2.5 readings that are above 100.\n", "3. Resample the data to provide the mean PM2.5 reading for each hour.\n", "4. Impute any missing values using the forward-fill method.\n", "5. Return a Series y."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(collection):\n", "    # Get the site with the most readings (from previous task)\n", "    site_with_most_readings = readings_per_site[0][\"_id\"]\n", "    \n", "    # Query for PM2.5 readings from that site\n", "    results = collection.find(\n", "        {\"metadata.site\": site_with_most_readings, \"metadata.measurement\": \"P2\"},\n", "        projection={\"P2\": 1, \"timestamp\": 1, \"_id\": 0},\n", "    )\n", "    \n", "    # Read results into DataFrame\n", "    df = pd.DataFrame(list(results)).set_index(\"timestamp\")\n", "    \n", "    # Localize timezone to Africa/Dar_es_Salaam\n", "    df.index = df.index.tz_localize(\"UTC\").tz_convert(\"Africa/Dar_es_Salaam\")\n", "    \n", "    # Remove outliers (readings above 100)\n", "    df = df[df[\"P2\"] <= 100]\n", "    \n", "    # Resample to hourly means and forward fill\n", "    y = df[\"P2\"].resample(\"1H\").mean().fillna(method='ffill')\n", "    \n", "    return y"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use your wrangle function to query the dar collection and return your cleaned results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y = wrangle(dar)\n", "y.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.4\", wrangle(dar))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Explore Some More"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.5: Create Time Series Plot\n", "\n", "Create a time series plot of the readings in y. Label your x-axis \"Date\" and your y-axis \"PM2.5 Level\". Use the title \"Dar es Salaam PM2.5 Levels\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "y.plot(ax=ax)\n", "ax.set_xlabel(\"Date\")\n", "ax.set_ylabel(\"PM2.5 Level\")\n", "ax.set_title(\"Dar es Salaam PM2.5 Levels\")\n", "\n", "# Don't delete the code below 👇\n", "plt.savefig(\"images/3-5-5.png\", dpi=150)\n", "\n", "with open(\"images/3-5-5.png\", \"rb\") as file:\n", "    wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.5\", file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.6: Plot Rolling Average\n", "\n", "Plot the rolling average of the readings in y. Use a window size of 168 (the number of hours in a week). Label your x-axis \"Date\" and your y-axis \"PM2.5 Level\". Use the title \"Dar es Salaam PM2.5 Levels, 7-Day Rolling Average\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "y.rolling(window=168).mean().plot(ax=ax)\n", "ax.set_xlabel(\"Date\")\n", "ax.set_ylabel(\"PM2.5 Level\")\n", "ax.set_title(\"Dar es Salaam PM2.5 Levels, 7-Day Rolling Average\")\n", "\n", "# Don't delete the code below 👇\n", "plt.savefig(\"images/3-5-6.png\", dpi=150)\n", "\n", "with open(\"images/3-5-6.png\", \"rb\") as file:\n", "    wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.6\", file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.7: Create ACF Plot\n", "\n", "Create an ACF plot for the data in y. Be sure to label the x-axis as \"Lag [hours]\" and the y-axis as \"Correlation Coefficient\". Use the title \"Dar es Salaam PM2.5 Readings, ACF\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "plot_acf(y, ax=ax)\n", "ax.set_xlabel(\"Lag [hours]\")\n", "ax.set_ylabel(\"Correlation Coefficient\")\n", "ax.set_title(\"Dar es Salaam PM2.5 Readings, ACF\")\n", "\n", "# Don't delete the code below 👇\n", "plt.savefig(\"images/3-5-7.png\", dpi=150)\n", "\n", "with open(\"images/3-5-7.png\", \"rb\") as file:\n", "    wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.7\", file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.8: Create PACF Plot\n", "\n", "Create an PACF plot for the data in y. Be sure to label the x-axis as \"Lag [hours]\" and the y-axis as \"Correlation Coefficient\". Use the title \"Dar es Salaam PM2.5 Readings, PACF\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(figsize=(15, 6))\n", "plot_pacf(y, ax=ax)\n", "ax.set_xlabel(\"Lag [hours]\")\n", "ax.set_ylabel(\"Correlation Coefficient\")\n", "ax.set_title(\"Dar es Salaam PM2.5 Readings, PACF\")\n", "\n", "# Don't delete the code below 👇\n", "plt.savefig(\"images/3-5-8.png\", dpi=150)\n", "\n", "with open(\"images/3-5-8.png\", \"rb\") as file:\n", "    wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.8\", file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Split"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.9: Split Data\n", "\n", "Split y into training and test sets. The first 90% of the data should be in your training set. The remaining 10% should be in the test set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cutoff_test = int(len(y) * 0.9)\n", "y_train = y.iloc[:cutoff_test]\n", "y_test = y.iloc[cutoff_test:]\n", "print(\"y_train shape:\", y_train.shape)\n", "print(\"y_test shape:\", y_test.shape)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.9a\", y_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.9b\", y_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build Model\n", "### <PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.10: Calculate Baseline MAE\n", "\n", "Establish the baseline mean absolute error for your model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_train_mean = y_train.mean()\n", "y_pred_baseline = [y_train_mean] * len(y_train)\n", "mae_baseline = mean_absolute_error(y_train, y_pred_baseline)\n", "\n", "print(\"Mean P2 Reading:\", y_train_mean)\n", "print(\"Baseline MAE:\", mae_baseline)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.10\", mae_baseline)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.11: Fit AR Model\n", "\n", "Fit an AR model with lag order of 26 to the training data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = AutoReg(y_train, lags=26).fit()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.11\", model.params)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluate"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.12: Calculate Training MAE\n", "\n", "Calculate the training mean absolute error for your model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred_training = model.fittedvalues\n", "mae_training = mean_absolute_error(y_train[26:], y_pred_training)\n", "print(\"Training MAE:\", mae_training)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.12\", mae_training)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.13: Calculate Test MAE\n", "\n", "Calculate the test mean absolute error for your model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred_test = model.forecast(steps=len(y_test))\n", "mae_test = mean_absolute_error(y_test, y_pred_test)\n", "print(\"Test MAE:\", mae_test)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.13\", mae_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Communicate Results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.14: Create Predictions DataFrame\n", "\n", "Create a DataFrame df_pred_test that has two columns: \"y_test\" and \"y_pred\". The first should contain the true values for your test set, and the second should contain your model's predictions. Be sure the index of df_pred_test matches the index of y_test."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_pred_test = pd.DataFrame({\n", "    \"y_test\": y_test,\n", "    \"y_pred\": y_pred_test\n", "}, index=y_test.index)\n", "\n", "df_pred_test.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.14\", df_pred_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 3.5.15: Create Time Series Plot\n", "\n", "Create a time series plot that shows the values in df_pred_test. Label your x-axis \"Date\" and your y-axis \"PM2.5 Level\". Use the title \"Dar es Salaam, Test Predictions\"."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = px.line(df_pred_test, labels={\"value\": \"PM2.5 Level\", \"index\": \"Date\"})\n", "fig.update_layout(title=\"Dar es Salaam, Test Predictions\")\n", "fig.show()\n", "\n", "# Don't delete the code below 👇\n", "fig.write_image(\"images/3-5-15.png\", width=700, height=500)\n", "\n", "with open(\"images/3-5-15.png\", \"rb\") as file:\n", "    wqet_grader.grade(\"Project 3 Assessment\", \"Task 3.5.15\", file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "**Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}