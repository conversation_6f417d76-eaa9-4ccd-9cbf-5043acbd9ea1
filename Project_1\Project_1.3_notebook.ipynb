{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project 1.3: Exploratory Data Analysis\n", "## WorldQuant University - Applied Data Science Lab\n", "\n", "### Learning Objectives\n", "- Understand the principles of Exploratory Data Analysis (EDA)\n", "- Create effective data visualizations\n", "- Analyze location, categorical, and numerical data\n", "- Use statistical measures to understand data distributions\n", "- Create maps, histograms, and boxplots\n", "- Interpret data patterns and insights\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction to Exploratory Data Analysis\n", "\n", "After importing and cleaning data, the next step in many data science projects is **exploratory data analysis (EDA)**, where you get a feel for your data by summarizing its main characteristics using descriptive statistics and data visualization.\n", "\n", "A good way to plan your EDA is by looking at each column and asking yourself questions about what it says about your dataset.\n", "\n", "### Types of Data We'll Analyze:\n", "1. **Location Data:** \"lat\" and \"lon\" - Where are our properties located?\n", "2. **Categorical Data:** \"state\" - Which states are most common?\n", "3. **Numerical Data:** \"area_m2\" and \"price_usd\" - What are the distributions?"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import plotly.express as px\n", "import numpy as np\n", "\n", "# Set up plotting parameters\n", "plt.rcParams['figure.figsize'] = (10, 6)\n", "plt.rcParams['font.size'] = 12\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(\"Ready for Exploratory Data Analysis! 📊\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Data\n", "\n", "Let's start by importing the clean dataset we created in the previous project."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.1: Read Clean Dataset\n", "\n", "Read the cleaned CSV file and verify that all columns have the correct data types.\n", "\n", "**Learning Goals:**\n", "- Import cleaned data for analysis\n", "- Verify data integrity before EDA"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Read the clean dataset\n", "df = pd.read_csv(\"data/mexico-real-estate-clean.csv\")\n", "\n", "# Print basic information\n", "print(\"df type:\", type(df))\n", "print(\"df shape:\", df.shape)\n", "print(\"\\nDataset info:\")\n", "print(df.info())\n", "print(\"\\nFirst 5 rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick data quality check\n", "print(\"=== DATA QUALITY CHECK ===\")\n", "print(f\"Total properties: {len(df):,}\")\n", "print(f\"Missing values: {df.isnull().sum().sum()}\")\n", "print(f\"Duplicate rows: {df.duplicated().sum()}\")\n", "print(f\"\\nData types:\")\n", "print(df.dtypes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Location Data Analysis\n", "\n", "They say that the most important thing in real estate is **location**! We can see where our houses are located by using the \"lat\" and \"lon\" columns. Since latitude and longitude are based on a coordinate system, a good way to visualize them is to create a scatter plot on top of a map."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.2: Create Geographic Scatter Plot\n", "\n", "Create a Mapbox scatter plot showing the location of properties in our dataset.\n", "\n", "**Learning Goals:**\n", "- Understand location data visualization\n", "- Create interactive maps with plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate center point for the map\n", "center_lat = df['lat'].median()\n", "center_lon = df['lon'].median()\n", "print(f\"Map center: ({center_lat:.2f}, {center_lon:.2f})\")\n", "\n", "# Create mapbox scatter plot\n", "fig = px.scatter_mapbox(\n", "    df,  # Our DataFrame\n", "    lat=\"lat\",\n", "    lon=\"lon\",\n", "    center={\"lat\": center_lat, \"lon\": center_lon},  # Center on Mexico\n", "    width=800,  # Width of map\n", "    height=600,  # Height of map\n", "    hover_data=[\"price_usd\", \"area_m2\", \"state\"],  # Display info when hovering\n", "    color=\"price_usd\",  # Color points by price\n", "    color_continuous_scale=\"viridis\",\n", "    title=\"Mexico Real Estate Properties by Location and Price\"\n", ")\n", "\n", "# Update map style\n", "fig.update_layout(mapbox_style=\"open-street-map\")\n", "\n", "# Show the map\n", "fig.show()\n", "\n", "print(\"🗺️ Interactive map created!\")\n", "print(\"💡 Hover over points to see property details\")\n", "print(\"🔍 Use zoom controls to explore different regions\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Map Analysis Questions:**\n", "- Are houses distributed evenly throughout Mexico?\n", "- Which regions have the highest concentration of properties?\n", "- Can you identify Mexico's biggest cities based on property density?\n", "- Do you notice any price patterns by geographic region?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Categorical Data Analysis\n", "\n", "Even though we can get a good idea of which states are most common from the map, we can get exact counts by analyzing the \"state\" column."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.3: Analyze State Distribution\n", "\n", "Use the value_counts method to determine the 10 most prevalent states in our dataset.\n", "\n", "**Learning Goals:**\n", "- Understand categorical data analysis\n", "- Use value_counts for frequency analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get value counts of \"state\" column\n", "state_counts = df[\"state\"].value_counts()\n", "\n", "print(\"=== TOP 10 STATES BY PROPERTY COUNT ===\")\n", "print(state_counts.head(10))\n", "\n", "print(f\"\\n=== STATE STATISTICS ===\")\n", "print(f\"Total unique states: {df['state'].nunique()}\")\n", "print(f\"Most common state: {state_counts.index[0]} ({state_counts.iloc[0]} properties)\")\n", "print(f\"Least common state: {state_counts.index[-1]} ({state_counts.iloc[-1]} properties)\")\n", "\n", "# Calculate percentage distribution\n", "state_percentages = (state_counts / len(df) * 100).round(2)\n", "print(f\"\\n=== TOP 5 STATES BY PERCENTAGE ===\")\n", "for state, count, pct in zip(state_counts.head().index, state_counts.head().values, state_percentages.head().values):\n", "    print(f\"{state}: {count} properties ({pct}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create bar plot of top 10 states\n", "plt.figure(figsize=(12, 8))\n", "top_10_states = state_counts.head(10)\n", "bars = plt.bar(range(len(top_10_states)), top_10_states.values, \n", "               color='skyblue', edgecolor='navy', alpha=0.7)\n", "\n", "# Customize the plot\n", "plt.title('Top 10 States by Number of Properties', fontsize=16, fontweight='bold', pad=20)\n", "plt.xlabel('State', fontsize=12)\n", "plt.ylabel('Number of Properties', fontsize=12)\n", "plt.xticks(range(len(top_10_states)), top_10_states.index, rotation=45, ha='right')\n", "\n", "# Add value labels on bars\n", "for bar, value in zip(bars, top_10_states.values):\n", "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, \n", "             str(value), ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 State distribution visualization created!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Numerical Data Analysis\n", "\n", "Now let's explore our numerical features: house prices and areas. The best way to understand numerical data is through descriptive statistics and visualizations."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.4: Descriptive Statistics\n", "\n", "Use the describe method to calculate summary statistics for \"area_m2\" and \"price_usd\".\n", "\n", "**Learning Goals:**\n", "- Calculate and interpret descriptive statistics\n", "- Understand measures of central tendency and spread"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate descriptive statistics\n", "numerical_stats = df[[\"area_m2\", \"price_usd\"]].describe()\n", "\n", "print(\"=== DESCRIPTIVE STATISTICS ===\")\n", "print(numerical_stats)\n", "\n", "# Additional analysis\n", "print(\"\\n=== ADDITIONAL INSIGHTS ===\")\n", "print(f\"Area (m²):\")\n", "print(f\"  • Mean > Median? {df['area_m2'].mean() > df['area_m2'].median()} (indicates right skew)\")\n", "print(f\"  • Coefficient of Variation: {(df['area_m2'].std() / df['area_m2'].mean() * 100):.1f}%\")\n", "\n", "print(f\"\\nPrice (USD):\")\n", "print(f\"  • Mean > Median? {df['price_usd'].mean() > df['price_usd'].median()} (indicates right skew)\")\n", "print(f\"  • Coefficient of Variation: {(df['price_usd'].std() / df['price_usd'].mean() * 100):.1f}%\")\n", "\n", "# Calculate price per square meter\n", "df['price_per_m2'] = df['price_usd'] / df['area_m2']\n", "print(f\"\\nPrice per m²:\")\n", "print(f\"  • Average: ${df['price_per_m2'].mean():.2f}/m²\")\n", "print(f\"  • Median: ${df['price_per_m2'].median():.2f}/m²\")\n", "print(f\"  • Range: ${df['price_per_m2'].min():.2f} - ${df['price_per_m2'].max():.2f}/m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Distribution Analysis\n", "\n", "Let's look at how house sizes and prices are distributed using histograms and boxplots."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.5: Home Size Distribution\n", "\n", "Create a histogram of \"area_m2\" to understand the distribution of home sizes.\n", "\n", "**Learning Goals:**\n", "- Create and interpret histograms\n", "- Understand data distributions and skewness"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create histogram of area_m2\n", "plt.figure(figsize=(12, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(df[\"area_m2\"], bins=30, color='lightcoral', alpha=0.7, edgecolor='black')\n", "plt.axvline(df[\"area_m2\"].mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: {df[\"area_m2\"].mean():.1f}')\n", "plt.axvline(df[\"area_m2\"].median(), color='blue', linestyle='--', linewidth=2, label=f'Median: {df[\"area_m2\"].median():.1f}')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Home Sizes\")\n", "plt.legend()\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "# Create a zoomed-in version (removing extreme outliers for better view)\n", "plt.subplot(1, 2, 2)\n", "q99 = df[\"area_m2\"].quantile(0.99)\n", "area_filtered = df[df[\"area_m2\"] <= q99][\"area_m2\"]\n", "plt.hist(area_filtered, bins=30, color='lightcoral', alpha=0.7, edgecolor='black')\n", "plt.axvline(area_filtered.mean(), color='red', linestyle='--', linewidth=2, label=f'Mean: {area_filtered.mean():.1f}')\n", "plt.axvline(area_filtered.median(), color='blue', linestyle='--', linewidth=2, label=f'Median: {area_filtered.median():.1f}')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Home Sizes (99th percentile)\")\n", "plt.legend()\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 Area distribution shows right skew - most homes are smaller with few very large ones\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.6: Home Size Boxplot\n", "\n", "Create a horizontal boxplot of \"area_m2\" to show the distribution differently.\n", "\n", "**Learning Goals:**\n", "- Create and interpret boxplots\n", "- Identify outliers and quartiles"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create horizontal boxplot of area_m2\n", "plt.figure(figsize=(12, 4))\n", "boxplot = plt.boxplot(df[\"area_m2\"], vert=False, patch_artist=True, \n", "                     boxprops=dict(facecolor='lightblue', alpha=0.7),\n", "                     medianprops=dict(color='red', linewidth=2))\n", "\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.title(\"Distribution of Home Sizes (Boxplot)\")\n", "plt.grid(axis='x', alpha=0.3)\n", "\n", "# Add statistical annotations\n", "q1, median, q3 = df[\"area_m2\"].quantile([0.25, 0.5, 0.75])\n", "plt.text(q1, 1.2, f'Q1: {q1:.0f}', ha='center', fontweight='bold')\n", "plt.text(median, 1.2, f'Median: {median:.0f}', ha='center', fontweight='bold', color='red')\n", "plt.text(q3, 1.2, f'Q3: {q3:.0f}', ha='center', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Analyze outliers\n", "iqr = q3 - q1\n", "lower_bound = q1 - 1.5 * iqr\n", "upper_bound = q3 + 1.5 * iqr\n", "outliers = df[(df[\"area_m2\"] < lower_bound) | (df[\"area_m2\"] > upper_bound)]\n", "\n", "print(f\"📦 Boxplot Analysis:\")\n", "print(f\"  • IQR (Interquartile Range): {iqr:.0f} m²\")\n", "print(f\"  • Outliers detected: {len(outliers)} properties\")\n", "print(f\"  • Largest property: {df['area_m2'].max():.0f} m²\")\n", "print(f\"  • Smallest property: {df['area_m2'].min():.0f} m²\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.7: Price Distribution\n", "\n", "Create a histogram of \"price_usd\" to understand price distributions.\n", "\n", "**Learning Goal:** Analyze price distributions and compare with area distributions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create histogram of price_usd\n", "plt.figure(figsize=(12, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.hist(df[\"price_usd\"], bins=30, color='lightgreen', alpha=0.7, edgecolor='black')\n", "plt.axvline(df[\"price_usd\"].mean(), color='red', linestyle='--', linewidth=2, \n", "           label=f'Mean: ${df[\"price_usd\"].mean():,.0f}')\n", "plt.axvline(df[\"price_usd\"].median(), color='blue', linestyle='--', linewidth=2, \n", "           label=f'Median: ${df[\"price_usd\"].median():,.0f}')\n", "plt.xlabel(\"Price [USD]\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Home Prices\")\n", "plt.legend()\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "# Log scale version for better visualization\n", "plt.subplot(1, 2, 2)\n", "plt.hist(df[\"price_usd\"], bins=30, color='lightgreen', alpha=0.7, edgecolor='black')\n", "plt.yscale('log')\n", "plt.axvline(df[\"price_usd\"].mean(), color='red', linestyle='--', linewidth=2, \n", "           label=f'Mean: ${df[\"price_usd\"].mean():,.0f}')\n", "plt.axvline(df[\"price_usd\"].median(), color='blue', linestyle='--', linewidth=2, \n", "           label=f'Median: ${df[\"price_usd\"].median():,.0f}')\n", "plt.xlabel(\"Price [USD]\")\n", "plt.ylabel(\"Frequency (log scale)\")\n", "plt.title(\"Distribution of Home Prices (Log Scale)\")\n", "plt.legend()\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"📊 Price distribution is even more right-skewed than area distribution\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Task 1.3.8: Price Boxplot\n", "\n", "Create a horizontal boxplot of \"price_usd\".\n", "\n", "**Learning Goal:** Compare price and area distributions using boxplots"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create horizontal boxplot of price_usd\n", "plt.figure(figsize=(12, 4))\n", "boxplot = plt.boxplot(df[\"price_usd\"], vert=False, patch_artist=True,\n", "                     boxprops=dict(facecolor='lightgreen', alpha=0.7),\n", "                     medianprops=dict(color='red', linewidth=2))\n", "\n", "plt.xlabel(\"Price [USD]\")\n", "plt.title(\"Distribution of Home Prices (Boxplot)\")\n", "plt.grid(axis='x', alpha=0.3)\n", "\n", "# Add statistical annotations\n", "q1_price, median_price, q3_price = df[\"price_usd\"].quantile([0.25, 0.5, 0.75])\n", "plt.text(q1_price, 1.2, f'Q1: ${q1_price:,.0f}', ha='center', fontweight='bold')\n", "plt.text(median_price, 1.2, f'Median: ${median_price:,.0f}', ha='center', fontweight='bold', color='red')\n", "plt.text(q3_price, 1.2, f'Q3: ${q3_price:,.0f}', ha='center', fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Analyze price outliers\n", "iqr_price = q3_price - q1_price\n", "price_outliers = df[(df[\"price_usd\"] < q1_price - 1.5 * iqr_price) | \n", "                   (df[\"price_usd\"] > q3_price + 1.5 * iqr_price)]\n", "\n", "print(f\"💰 Price Analysis:\")\n", "print(f\"  • Price IQR: ${iqr_price:,.0f}\")\n", "print(f\"  • Price outliers: {len(price_outliers)} properties\")\n", "print(f\"  • Most expensive: ${df['price_usd'].max():,.0f}\")\n", "print(f\"  • Least expensive: ${df['price_usd'].min():,.0f}\")\n", "print(f\"  • Price span: ${df['price_usd'].max() - df['price_usd'].min():,.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Analysis: Price vs Area Relationship\n", "\n", "Let's explore the relationship between house size and price."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create scatter plot of price vs area\n", "plt.figure(figsize=(12, 8))\n", "\n", "# Main scatter plot\n", "plt.subplot(2, 2, 1)\n", "plt.scatter(df[\"area_m2\"], df[\"price_usd\"], alpha=0.6, color='purple')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price [USD]\")\n", "plt.title(\"Price vs Area (All Data)\")\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Filtered scatter plot (remove extreme outliers)\n", "plt.subplot(2, 2, 2)\n", "area_q99 = df[\"area_m2\"].quantile(0.99)\n", "price_q99 = df[\"price_usd\"].quantile(0.99)\n", "df_filtered = df[(df[\"area_m2\"] <= area_q99) & (df[\"price_usd\"] <= price_q99)]\n", "plt.scatter(df_filtered[\"area_m2\"], df_filtered[\"price_usd\"], alpha=0.6, color='orange')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price [USD]\")\n", "plt.title(\"Price vs Area (99th percentile)\")\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Price per m² distribution\n", "plt.subplot(2, 2, 3)\n", "plt.hist(df[\"price_per_m2\"], bins=30, color='gold', alpha=0.7, edgecolor='black')\n", "plt.xlabel(\"Price per m² [USD]\")\n", "plt.ylabel(\"Frequency\")\n", "plt.title(\"Distribution of Price per m²\")\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "# Price per m² by area (to see if larger homes have different $/m²)\n", "plt.subplot(2, 2, 4)\n", "plt.scatter(df[\"area_m2\"], df[\"price_per_m2\"], alpha=0.6, color='red')\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price per m² [USD]\")\n", "plt.title(\"Price per m² vs Area\")\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate correlation\n", "correlation = df[\"area_m2\"].corr(df[\"price_usd\"])\n", "print(f\"📈 Correlation Analysis:\")\n", "print(f\"  • Area vs Price correlation: {correlation:.3f}\")\n", "print(f\"  • Interpretation: {'Strong' if abs(correlation) > 0.7 else 'Moderate' if abs(correlation) > 0.4 else 'Weak'} positive relationship\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Geographic Price Analysis\n", "\n", "Let's see how prices vary by state."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate average price by state\n", "price_by_state = df.groupby('state')['price_usd'].agg(['mean', 'median', 'count']).round(2)\n", "price_by_state = price_by_state.sort_values('mean', ascending=False)\n", "\n", "print(\"=== TOP 10 STATES BY AVERAGE PRICE ===\")\n", "print(price_by_state.head(10))\n", "\n", "# Create visualization\n", "plt.figure(figsize=(14, 8))\n", "\n", "# Top 10 states by average price\n", "plt.subplot(2, 1, 1)\n", "top_10_price = price_by_state.head(10)\n", "bars = plt.bar(range(len(top_10_price)), top_10_price['mean'], \n", "               color='coral', alpha=0.7, edgecolor='darkred')\n", "plt.title('Top 10 States by Average House Price', fontsize=14, fontweight='bold')\n", "plt.ylabel('Average Price [USD]')\n", "plt.xticks(range(len(top_10_price)), top_10_price.index, rotation=45, ha='right')\n", "\n", "# Add value labels\n", "for bar, value in zip(bars, top_10_price['mean']):\n", "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000, \n", "             f'${value:,.0f}', ha='center', va='bottom', fontweight='bold', fontsize=9)\n", "\n", "plt.grid(axis='y', alpha=0.3)\n", "\n", "# States with most properties vs their average price\n", "plt.subplot(2, 1, 2)\n", "top_states_by_count = df['state'].value_counts().head(10)\n", "prices_for_top_states = [price_by_state.loc[state, 'mean'] if state in price_by_state.index else 0 \n", "                        for state in top_states_by_count.index]\n", "\n", "bars = plt.bar(range(len(top_states_by_count)), prices_for_top_states, \n", "               color='lightblue', alpha=0.7, edgecolor='navy')\n", "plt.title('Average Price in States with Most Properties', fontsize=14, fontweight='bold')\n", "plt.xlabel('State')\n", "plt.ylabel('Average Price [USD]')\n", "plt.xticks(range(len(top_states_by_count)), top_states_by_count.index, rotation=45, ha='right')\n", "\n", "# Add value labels\n", "for bar, value in zip(bars, prices_for_top_states):\n", "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000, \n", "             f'${value:,.0f}', ha='center', va='bottom', fontweight='bold', fontsize=9)\n", "\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n🏛️ Geographic Insights:\")\n", "print(f\"  • Highest average price: {price_by_state.index[0]} (${price_by_state.iloc[0]['mean']:,.0f})\")\n", "print(f\"  • Lowest average price: {price_by_state.index[-1]} (${price_by_state.iloc[-1]['mean']:,.0f})\")\n", "print(f\"  • Price variation: ${price_by_state.iloc[0]['mean'] - price_by_state.iloc[-1]['mean']:,.0f} difference\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## EDA Summary and Insights\n", "\n", "Let's summarize our key findings from this exploratory data analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive summary\n", "print(\"=\" * 60)\n", "print(\"📊 EXPLORATORY DATA ANALYSIS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🗺️ GEOGRAPHIC DISTRIBUTION:\")\n", "print(f\"  • Total properties analyzed: {len(df):,}\")\n", "print(f\"  • States represented: {df['state'].nunique()}\")\n", "print(f\"  • Most properties in: {df['state'].value_counts().index[0]} ({df['state'].value_counts().iloc[0]} properties)\")\n", "print(f\"  • Properties concentrated in central Mexico\")\n", "\n", "print(\"\\n🏠 PROPERTY SIZE ANALYSIS:\")\n", "print(f\"  • Average size: {df['area_m2'].mean():.0f} m²\")\n", "print(f\"  • Median size: {df['area_m2'].median():.0f} m²\")\n", "print(f\"  • Size range: {df['area_m2'].min():.0f} - {df['area_m2'].max():.0f} m²\")\n", "print(f\"  • Distribution: Right-skewed (many small, few large properties)\")\n", "\n", "print(\"\\n💰 PRICE ANALYSIS:\")\n", "print(f\"  • Average price: ${df['price_usd'].mean():,.0f}\")\n", "print(f\"  • Median price: ${df['price_usd'].median():,.0f}\")\n", "print(f\"  • Price range: ${df['price_usd'].min():,.0f} - ${df['price_usd'].max():,.0f}\")\n", "print(f\"  • Distribution: Highly right-skewed (many affordable, few expensive)\")\n", "\n", "print(\"\\n📈 PRICE PER SQUARE METER:\")\n", "print(f\"  • Average: ${df['price_per_m2'].mean():.2f}/m²\")\n", "print(f\"  • Median: ${df['price_per_m2'].median():.2f}/m²\")\n", "print(f\"  • Range: ${df['price_per_m2'].min():.2f} - ${df['price_per_m2'].max():.2f}/m²\")\n", "\n", "print(\"\\n🔗 RELATIONSHIPS:\")\n", "correlation = df['area_m2'].corr(df['price_usd'])\n", "print(f\"  • Area vs Price correlation: {correlation:.3f}\")\n", "print(f\"  • Relationship strength: {'Strong' if abs(correlation) > 0.7 else 'Moderate' if abs(correlation) > 0.4 else 'Weak'} positive\")\n", "print(f\"  • Larger homes tend to be more expensive\")\n", "\n", "print(\"\\n🏛️ REGIONAL PATTERNS:\")\n", "most_expensive_state = price_by_state.index[0]\n", "cheapest_state = price_by_state.index[-1]\n", "print(f\"  • Most expensive state: {most_expensive_state} (${price_by_state.loc[most_expensive_state, 'mean']:,.0f} avg)\")\n", "print(f\"  • Most affordable state: {cheapest_state} (${price_by_state.loc[cheapest_state, 'mean']:,.0f} avg)\")\n", "print(f\"  • Regional price variation: {(price_by_state.iloc[0]['mean'] / price_by_state.iloc[-1]['mean']):.1f}x difference\")\n", "\n", "print(\"\\n✨ KEY INSIGHTS:\")\n", "print(f\"  • Mexico's real estate market shows significant regional variation\")\n", "print(f\"  • Property sizes and prices are both right-skewed distributions\")\n", "print(f\"  • Strong correlation between size and price suggests area is a good predictor\")\n", "print(f\"  • Urban areas likely drive higher property concentrations\")\n", "print(f\"  • Market accessibility varies greatly across states\")\n", "\n", "print(\"\\n🎯 NEXT STEPS FOR ANALYSIS:\")\n", "print(f\"  • Investigate specific urban vs rural patterns\")\n", "print(f\"  • Analyze property types (houses vs apartments)\")\n", "print(f\"  • Build predictive models using size and location\")\n", "print(f\"  • Study market trends and seasonal patterns\")\n", "print(f\"  • Examine investment opportunities by region\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "🎉 **Congratulations!** You've completed a comprehensive exploratory data analysis of Mexico's real estate market!\n", "\n", "### What You've Learned:\n", "\n", "1. **EDA Methodology:**\n", "   - **Systematic approach** to understanding datasets\n", "   - **Multiple visualization types** for different data types\n", "   - **Statistical measures** to quantify patterns\n", "   - **Geographic analysis** using maps and location data\n", "\n", "2. **Visualization Skills:**\n", "   - **Interactive maps** with plotly for geographic data\n", "   - **Histograms** for understanding distributions\n", "   - **Boxplots** for identifying outliers and quartiles\n", "   - **<PERSON><PERSON><PERSON> plots** for exploring relationships\n", "   - **Bar charts** for categorical comparisons\n", "\n", "3. **Statistical Analysis:**\n", "   - **Descriptive statistics** (mean, median, quartiles)\n", "   - **Distribution analysis** (skewness, outliers)\n", "   - **Correlation analysis** (relationships between variables)\n", "   - **Frequency analysis** (value counts, percentages)\n", "\n", "4. **Data Insights Discovery:**\n", "   - **Geographic patterns** in real estate markets\n", "   - **Price-size relationships** and market dynamics\n", "   - **Regional variations** and market accessibility\n", "   - **Distribution characteristics** and market segmentation\n", "\n", "5. **Professional Analysis Skills:**\n", "   - **Multi-dimensional analysis** (location, categorical, numerical)\n", "   - **Data quality assessment** throughout analysis\n", "   - **Insight synthesis** and pattern recognition\n", "   - **Actionable conclusions** for business decisions\n", "\n", "### Critical EDA Principles Applied:\n", "- **Look at data from multiple angles** (maps, distributions, relationships)\n", "- **Use appropriate visualizations** for each data type\n", "- **Identify and handle outliers** appropriately\n", "- **Quantify relationships** with correlation analysis\n", "- **Summarize findings** clearly and actionably\n", "\n", "### Business Impact:\n", "Your analysis reveals valuable insights for:\n", "- **Real estate investors** seeking regional opportunities\n", "- **Property developers** understanding market demands\n", "- **Buyers and sellers** making informed decisions\n", "- **Policy makers** understanding housing markets\n", "\n", "### Next Steps:\n", "With this solid EDA foundation, you're ready to:\n", "- Build predictive models for property pricing\n", "- Conduct hypothesis testing and statistical inference\n", "- Develop market segmentation strategies\n", "- Create business intelligence dashboards\n", "\n", "**You've transformed raw data into actionable market intelligence!** 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}