{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 2.4. Predicting <PERSON> with <PERSON><PERSON>, Location, and Neighborhood\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "from glob import glob\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import wqet_grader\n", "from category_encoders import OneHotEncoder\n", "from IPython.display import VimeoVideo\n", "from ipywidgets import Dropdown, FloatSlider, IntSlider, interact\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LinearRegression, Ridge  # noqa F401\n", "from sklearn.metrics import mean_absolute_error\n", "from sklearn.pipeline import make_pipeline\n", "from sklearn.utils.validation import check_is_fitted\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "wqet_grader.init(\"Project 2 Assessment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the final lesson for this project, we're going to try to use all the features in our dataset to improve our model. This means that we'll have to do a more careful cleaning of the dataset and consider some of the finer points of linear models."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656842813\", h=\"07f074324e\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Import"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    # Read CSV file\n", "    df = pd.read_csv(filepath)\n", "\n", "    # Subset data: Apartments in \"Capital Federal\", less than 400,000\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_price = df[\"price_aprox_usd\"] < 400_000\n", "    df = df[mask_ba & mask_apt & mask_price]\n", "\n", "    # Subset data: Remove outliers for \"surface_covered_in_m2\"\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "\n", "    # Split \"lat-lon\" column\n", "    df[[\"lat\", \"lon\"]] = df[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "    df.drop(columns=\"lat-lon\", inplace=True)\n", "\n", "    # Get place name\n", "    df[\"neighborhood\"] = df[\"place_with_parent_names\"].str.split(\"|\", expand=True)[3]\n", "    df.drop(columns=\"place_with_parent_names\", inplace=True)\n", "    \n", "    # Drop columns that are more than half NaN values\n", "    thresh = len(df) / 2\n", "    df = df.dropna(axis=1, thresh=thresh)\n", "\n", "    # Drop high- and low-cardinality categorical features\n", "    df.drop(columns=[\"operation\",\"property_type\",\"currency\",\"properati_url\"], inplace=True)  \n", "    \n", "    # Drop any features that would constitute leakage\n", "    df.drop(columns=['price',\n", "                    'price_aprox_local_currency',\n", "                    'price_per_m2',\n", "                    'price_usd_per_m2'\n", "                    ], inplace=True)\n", "    \n", "    # Remove strongly correlated features\n", "    df.drop(columns=[\"surface_total_in_m2\",\"rooms\"], inplace=True)\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's begin by using what we've learned to load all our CSV files into a DataFrame."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656842538\", h=\"bd85634eb1\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.4.1: Create File List\n", "\n", "Use glob to create a list that contains the filenames for all the Buenos Aires real estate CSV files in the data directory. Assign this list to the variable name files.\n", "\n", "**Assemble a list of path names that match a pattern in glob.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["files = glob(\"data/buenos-aires-real-estate-*.csv\")\n", "files"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert len(files) == 5, f\"`files` should contain 5 items, not {len(files)}\""]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}