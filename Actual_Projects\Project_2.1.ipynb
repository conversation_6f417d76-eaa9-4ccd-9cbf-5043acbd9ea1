{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 2.1. Predicting <PERSON> with <PERSON><PERSON>\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import wqet_grader\n", "from IPython.display import VimeoVideo\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_absolute_error\n", "from sklearn.utils.validation import check_is_fitted\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "wqet_grader.init(\"Project 2 Assessment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this project, you're working for a client who wants to create a model that can predict the price of apartments in the city of Buenos Aires — with a focus on apartments that cost less than $400,000 USD."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656704385\", h=\"abf81d298d\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Import\n", "\n", "In the previous project, we cleaned our data files one-by-one. This isn't an issue when you're working with just three files, but imagine if you had several hundred! One way to automate the data importing and cleaning process is by writing a function. This will make sure that all our data undergoes the same process, and that our analysis is easily reproducible — something that's very important in science in general and data science in particular."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656703362\", h=\"bae256298f\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.1: Write a Wrangle Function\n", "\n", "Write a function named wrangle that takes a file path as an argument and returns a DataFrame.\n", "\n", "**What's a function?**\n", "\n", "**Write a function in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    df = pd.read_csv(filepath)\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_cost = df[\"price_aprox_usd\"] < 400_000\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "\n", "    df = df[mask_ba & mask_apt & mask_cost]\n", "    \n", "    # Remove outliers by \"surface_covered_in_m2\"\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have a function written, let's test it out on one of the CSV files we'll use in this project."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656701336\", h=\"c3a3e9bc16\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.2: Test Wrangle Function\n", "\n", "Use your wrangle function to create a DataFrame df from the CSV file data/buenos-aires-real-estate-1.csv."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = wrangle(\"data/buenos-aires-real-estate-1.csv\")\n", "print(\"df shape:\", df.shape)\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["At this point, your DataFrame df should have no more than 8,606 observations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    len(df) <= 8606\n", "), f\"`df` should have no more than 8606 observations, not {len(df)}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this project, we want to build a model for apartments in Buenos Aires proper (\"Capital Federal\") that cost less than $400,000. Looking at the first five rows of our DataFrame, we can already see that there properties that fall outside those parameters. So our first cleaning task is to remove those observations from our dataset. Since we're using a function to import and clean our data, we'll need to make changes there."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656697884\", h=\"95081c955c\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.3: Filter Data\n", "\n", "Add to your wrangle function so that the DataFrame it returns only includes apartments in Buenos Aires (\"Capital Federal\") that cost less than $400,000 USD. Then recreate df from data/buenos-aires-real-estate-1.csv by re-running the cells above.\n", "\n", "**Subset a DataFrame with a mask using pandas.**\n", "\n", "To check your work, df should no have no more than 1,781 observations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"currency\"].unique()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    len(df) <= 1781\n", "), f\"`df` should have no more than 1781 observations, not {len(df)}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Explore\n", "\n", "We saw in the previous project that property size is an important factor in determining price. With that in mind, let's look at the distribution of apartment sizes in our dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656697539\", h=\"9e0a4673f0\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.4: Create Histogram\n", "\n", "Create a histogram of \"surface_covered_in_m2\". Make sure that the x-axis has the label \"Area [sq meters]\" and the plot has the title \"Distribution of Apartment Sizes\".\n", "\n", "**What's a histogram?**\n", "\n", "**Create a histogram using Matplotlib.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"surface_covered_in_m2\"].plot.hist()\n", "plt.xlabel(\"Area (sq meters)\")\n", "plt.title(\"Distribution of Apartment Sizes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Yikes! When you see a histogram like the one above, it suggests that there are outliers in your dataset. This can affect model performance — especially in the sorts of linear models we'll learn about in this project. To confirm, let's look at the summary statistics for the \"surface_covered_in_m2\" feature."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656697049\", h=\"649a69e5a2\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.5: Calculate Summary Statistics\n", "\n", "Calculate the summary statistics for df using the describe method.\n", "\n", "**What's skewed data?**\n", "\n", "**Print the summary statistics for a DataFrame using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df[\"surface_covered_in_m2\"].describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The statistics above confirm what we suspected. While most of the apartments in our dataset are smaller that 73 square meters, there are some that are several thousand square meters. The best thing to do is to change our wrangle function and remove them from the dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656696370\", h=\"a809e66bb8\", width=600)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "mask_area"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.6: Remove Outliers\n", "\n", "Add to your wrangle function so that it removes observations that are outliers in the \"surface_covered_in_m2\" column. Specifically, all observations should fall between the 0.1 and 0.9 quantiles for \"surface_covered_in_m2\".\n", "\n", "**What's a quantile?**\n", "\n", "**Calculate the quantiles for a Series in pandas.**\n", "**Subset a DataFrame with a mask using pandas.**\n", "\n", "When you're done, don't forget to rerun all the cells above. Note how your histogram changes now that there are no outliers. At this point, df should have no more than 1,343 observations.\n", "\n", "**Tip:** One of the DS Lab students, <PERSON><PERSON>, wrote an excellent forum post on how the order in which you apply masks can affect your data. Check it out!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert len(df) <= 1343"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that our dataset is free of outliers, it's time to start exploring the relationship between apartment size and price. Let's use one of the tools we learned in the last project."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656696079\", h=\"80b4e6ce8e\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.7: C<PERSON> <PERSON><PERSON><PERSON> Plot\n", "\n", "Create a scatter plot that shows price (\"price_aprox_usd\") vs area (\"surface_covered_in_m2\") in our dataset. Make sure to label your x-axis \"Area [sq meters]\" and your y-axis \"Price [USD]\".\n", "\n", "**What's a scatter plot?**\n", "\n", "**Create a scatter plot using Matplotlib.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.plot.scatter(x=\"surface_covered_in_m2\", y=\"price_aprox_usd\")\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price [USD]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This plot suggests that there's a moderate positive correlation between apartment price and size. This means that if thing we want to predict is price, size will be a good feature to include."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656749759\", h=\"095ad450ac\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Split\n", "\n", "A key part in any model-building project is separating your target (the thing you want to predict) from your features (the information your model will use to make its predictions). Since this is our first model, we'll use just one feature: apartment size."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656688282\", h=\"84ef8e90b3\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.8: Create Feature Matrix\n", "\n", "Create the feature matrix named X_train, which you'll use to train your model. It should contain one feature only: [\"surface_covered_in_m2\"]. Remember that your feature matrix should always be two-dimensional.\n", "\n", "**What's a feature matrix?**\n", "\n", "**Create a DataFrame from a Series in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["features = [\"surface_covered_in_m2\"]\n", "X_train = df[features]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert X_train.shape == (\n", "    1343,\n", "    1,\n", "), f\"The shape of `X_train` should be (1343, 1), not {X_train.shape}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have a features, the next step is to create a target. (By the way, you may have noticed that we're adding a _train tag to the variable names for our feature matrix and target vector. This is to remind us that this is the data we'll use to train our model, and not the data we'll use to test it.)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656688065\", h=\"c391dae2e6\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.9: Create Target Vector\n", "\n", "Create the target vector named y_train, which you'll use to train your model. Your target should be \"price_aprox_usd\". Remember that, in most cases, your target vector should be one-dimensional.\n", "\n", "**What's a target vector?**\n", "\n", "**Select a Series from a DataFrame in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["target = \"price_aprox_usd\"\n", "y_train = df[target]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert y_train.shape == (1343,)\n", "y_train.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Build Model\n", "### <PERSON><PERSON>\n", "\n", "The first step in building a model is baselining. To do this, ask yourself how you will know if the model you build is performing well?\" One way to think about this is to see how a \"dumb\" model would perform on the same data. Some people also call this a naïve or baseline model, but it's always a model makes only one prediction — in this case, it predicts the same price regardless of an apartment's size. So let's start by figuring out what our baseline model's prediction should be."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656687537\", h=\"67df9f3bd7\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.10: Calculate Mean Target\n", "\n", "Calculate the mean of your target vector y_train and assign it to the variable y_mean.\n", "\n", "**What's a regression problem?**\n", "\n", "**Calculate summary statistics for a DataFrame or Series in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_mean = y_train.mean()\n", "y_mean"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have the one prediction that our dumb model will always make, we need to generate a list that repeats the prediction for every observation in our dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656687259\", h=\"684b40ef32\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.11: Create Baseline Predictions\n", "\n", "Create a list named y_pred_baseline that contains the value of y_mean repeated so that it's the same length at y.\n", "\n", "**Calculate the length of a list in Python.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred_baseline = [y_mean] * len(y_train)\n", "y_pred_baseline[1:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So how does our baseline model perform? One way to evaluate it is by plotting it on top of the scatter plot we made above."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656686948\", h=\"2dbbdccfa4\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.12: Plot Baseline Model\n", "\n", "Add a line to the plot below that shows the relationship between the observations <PERSON>_<PERSON> and our dumb model's predictions y_pred_baseline. Be sure that the line color is orange, and that it has the label \"Baseline Model\".\n", "\n", "**What's a line plot?**\n", "\n", "**Create a line plot in Matplotlib.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(X_train, y_pred_baseline, color='orange', label='Baseline Model')\n", "\n", "plt.scatter(X_train, y_train)\n", "plt.xlabel(\"Area [sq meters]\")\n", "plt.ylabel(\"Price [USD]\")\n", "plt.title(\"Buenos Aires: Price vs. Area\")\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Looking at this visualization, it seems like our baseline model doesn't really follow the trend in the data. But, as a data scientist, you can't depend only on a subjective plot to evaluate a model. You need an exact, mathematically calculate performance metric. There are lots of performance metrics, but the one we'll use here is the mean absolute error."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656686010\", h=\"214406a99f\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.13: Calculate Baseline MAE\n", "\n", "Calculate the baseline mean absolute error for your predictions in y_pred_baseline as compared to the true targets in y.\n", "\n", "**What's a performance metric?**\n", "**What's mean absolute error?**\n", "\n", "**Calculate the mean absolute error for a list of predictions in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae_baseline = mean_absolute_error(y_train, y_pred_baseline)\n", "\n", "print(\"Mean apt price\", round(y_mean, 2))\n", "print(\"Baseline MAE:\", round(mae_baseline, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["What does this information tell us? If we always predicted that an apartment price is $135,527.84, our predictions would be off by an average of $45,199.46. It also tells us that our model needs to have mean absolute error below $45,199.46 in order to be useful."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Iterate\n", "\n", "The next step in building a model is iterating. This involves building a model, training it, evaluating it, and then repeating the process until you're happy with the model's performance. Even though the model we're building is linear, the iteration process rarely follows a straight line. Be prepared for trying new things, hitting dead-ends, and waiting around while your computer does long computations to train your model. ☕️ Let's get started!\n", "\n", "The first thing we need to do is create our model — in this case, one that uses linear regression."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656685822\", h=\"6b6bce7f3c\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.14: Instantiate Linear Regression Model\n", "\n", "Instantiate a LinearRegression model named model.\n", "\n", "**What's linear regression?**\n", "**What's a cost function?**\n", "\n", "**Instantiate a predictor in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = LinearRegression()\n", "# Check your work\n", "assert isinstance(model, LinearRegression)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The second thing we need to do is use our data to train our model. Another way to say this is fit our model to the training data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656685645\", h=\"444e6e49e7\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.15: Fit Model\n", "\n", "Fit your model to the data, X_train and y_train.\n", "\n", "**Fit a model to training data in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "check_is_fitted(model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate\n", "\n", "The final step is to evaluate our model. In order to do that, we'll start by seeing how well it performs when making predictions for data that it saw during training. So let's have it predict the price for the houses in our training set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656685380\", h=\"3b79fe2cdb\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.16: Generate Predictions\n", "\n", "Using your model's predict method, create a list of predictions for the observations in your feature matrix X_train. Name this array y_pred_training.\n", "\n", "**Generate predictions using a trained model in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["y_pred_training = model.predict(X_train)\n", "y_pred_training[:5]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    len(y_pred_training) == 1343\n", "), f\"There should be 1343 predictions in `y_pred_training`, not {len(y_pred_training)}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have predictions, we'll use them to assess our model's performance with the training data. We'll use the same metric we used to evaluate our baseline model: mean absolute error."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656685229\", h=\"b668f12bc1\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.17: Calculate Training MAE\n", "\n", "Calculate your training mean absolute error for your predictions in y_pred_training as compared to the true targets in y_train.\n", "\n", "**Calculate the mean absolute error for a list of predictions in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mae_training = mean_absolute_error(y_train, y_pred_training)\n", "print(\"Training MAE:\", round(mae_training, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Good news: Our model beat the baseline by over $10,000! That's a good indicator that it will be helpful in predicting apartment prices. But the real test is how the model performs on data that it hasn't seen before, data that we call the test set."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.18: Test Model Performance\n", "\n", "Run the code below to import your test data buenos-aires-test-features.csv into a DataFrame and generate a Series of predictions using your model.\n", "\n", "**What's generalizability?**\n", "\n", "**Generate predictions using a trained model in scikit-learn.**\n", "**Calculate the mean absolute error for a list of predictions in scikit-learn.**\n", "\n", "**Tip:** Make sure the X_train you used to train your model has the same column order as X_test. Otherwise, it may hurt your model's performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["X_test = pd.read_csv(\"data/buenos-aires-test-features.csv\")[features]\n", "y_pred_test = pd.Series(model.predict(X_test))\n", "y_pred_test.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["wqet_grader.grade(\"Project 2 Assessment\", \"Task 2.1.18\", y_pred_test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Communicate Results\n", "\n", "Once your model is built and tested, it's time to share it with others. If you're presenting to simple linear model to a technical audience, they might appreciate an equation. When we created our baseline model, we represented it as a line. The equation for a line like this is usually written as:\n", "\n", "**Equation:** y = m*x + b\n", "\n", "Since data scientists often work with more complicated linear models, they prefer to write the equation as:\n", "\n", "**Equation:** y = beta 0 + beta 1 * x\n", "\n", "Regardless of how we write the equation, we need to find the values that our model has determined for the intercept and and coefficient. Fortunately, all trained models in scikit-learn store this information in the model itself. Let's start with the intercept."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656684478\", h=\"87d29a2ba6\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.19: Extract Model Intercept\n", "\n", "Extract the intercept from your model, and assign it to the variable intercept.\n", "\n", "**What's an intercept in a linear model?**\n", "\n", "**Access an attribute of a trained model in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["intercept = model.intercept_\n", "print(\"Model Intercept:\", intercept)\n", "assert any([isinstance(intercept, int), isinstance(intercept, float)])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next comes the coefficient. We'll extract it in a very similar way."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656684245\", h=\"f96cf91211\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.20: Extract Model Coefficient\n", "\n", "Extract the coefficient associated \"surface_covered_in_m2\" in your model, and assign it to the variable coefficient.\n", "\n", "**What's a coefficient in a linear model?**\n", "\n", "**Access an attribute of a trained model in scikit-learn.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["coefficient = model.coef_[0]\n", "print('Model coefficient for \"surface_covered_in_m2\":', coefficient)\n", "assert any([isinstance(coefficient, int), isinstance(coefficient, float)])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that we have our intercept and coefficient, we need to insert them into a string that we can print out the complete equation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656684037\", h=\"f30c2b4dfc\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.21: Print Model Equation\n", "\n", "Complete the code below and run the cell to print the equation that your model has determined for predicting apartment price based on size.\n", "\n", "**What's an f-string?**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"Price = {intercept:.2f} + {coefficient:.2f} * Surface_Covered_in_m2\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Equation might work for some technical audiences, but visualization or generally much more effective communication tool — especially for non-technical audiences. So let's use the scatter plot we made at the beginning of this lesson and plot the line that that are equation would make."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656683862\", h=\"886904448d\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.1.22: Visualize Model\n", "\n", "Add a line to the plot below that shows the relationship between the observations in X_train and your model's predictions y_pred_training. Be sure that the line color is red, and that it has the label \"Linear Model\".\n", "\n", "**What's a line plot?**\n", "\n", "**Create a line plot in pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plt.plot(X_train, y_pred_baseline, color='orange', label='Baseline Model')\n", "plt.plot(X_train, y_pred_training, color='red', label='Linear Model')\n", "\n", "plt.scatter(X_train, y_train)\n", "plt.xlabel(\"surface covered [sq meters]\")\n", "plt.ylabel(\"price [usd]\")\n", "plt.legend();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["---\n", "\n", "**Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.**"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}