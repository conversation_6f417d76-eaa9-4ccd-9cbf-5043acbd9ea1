Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
1.4. Location or Size: What Influences House Prices in Mexico?

import matplotlib.pyplot as plt
import pandas as pd
from IPython.display import VimeoVideo
You've wrangled the data, you've gained an understanding of its basic characteristics in your EDA, and now it's time to ask some research questions.

Import Data
Task 1.4.1: Read the CSV file that you created in the last notebook ("data/mexico-real-estate-clean.csv") into a DataFrame named df. Be sure to check that all your columns are the correct data type before you go to the next task.

What's a DataFrame?
What's a CSV file?
Read a CSV file into a DataFrame using pandas.
# Import "data/mexico-real-estate-clean.csv"
df = pd.read_csv("data/mexico-real-estate-clean.csv")
​
# Print object type, shape, and head
print("df type:", type(df))
print("df shape:", df.shape)
df.head()
df type: <class 'pandas.core.frame.DataFrame'>
df shape: (1736, 6)
property_type	state	lat	lon	area_m2	price_usd
0	house	Estado de México	19.560181	-99.233528	150.0	67965.56
1	house	Nuevo León	25.688436	-100.198807	186.0	63223.78
2	apartment	Guerrero	16.767704	-99.764383	82.0	84298.37
3	apartment	Guerrero	16.829782	-99.911012	150.0	94308.80
4	house	Yucatán	21.052583	-89.538639	205.0	105191.37
df.info();
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 1736 entries, 0 to 1735
Data columns (total 6 columns):
 #   Column         Non-Null Count  Dtype  
---  ------         --------------  -----  
 0   property_type  1736 non-null   object 
 1   state          1736 non-null   object 
 2   lat            1736 non-null   float64
 3   lon            1736 non-null   float64
 4   area_m2        1736 non-null   float64
 5   price_usd      1736 non-null   float64
dtypes: float64(4), object(2)
memory usage: 81.5+ KB
Research Question 1
Which state has the most expensive real estate market?

Do housing prices vary by state? If so, which are the most expensive states for purchasing a home? During our exploratory data analysis, we used descriptive statistics like mean and median to get an idea of the "typical" house price in Mexico. Now, we need to break that calculation down by state and visualize the results.

We know in which state each house is located thanks to the "state" column. The next step is to divide our dataset into groups (one per state) and calculate the mean house price for each group.

VimeoVideo("656378731", h="8daa35d1e8", width=600)
Task 1.4.2: Use the groupby method to create a Series named mean_price_by_state, where the index contains each state in the dataset and the values correspond to the mean house price for that state. Make sure your Series is sorted from highest to lowest mean price.

What's a Series?
Aggregate data using the groupby method in pandas.
# Declare variable `mean_price_by_state`
mean_price_by_state = df.groupby("state")["price_usd"].mean().sort_values(ascending=False).head(10)
mean_price_by_state
state
Querétaro           133955.913281
Guanajuato          133277.965833
Nuevo León          129221.985663
Distrito Federal    128347.267426
Quintana Roo        128065.416053
Chihuahua           127073.852000
Jalisco             123386.472167
Estado de México    122723.490503
Campeche            121734.633333
Puebla              121732.974000
Name: price_usd, dtype: float64
# Print object type, shape, and head
print("mean_price_by_state type:", type(mean_price_by_state))
print("mean_price_by_state shape:", mean_price_by_state.shape)
mean_price_by_state.head()
mean_price_by_state type: <class 'pandas.core.series.Series'>
mean_price_by_state shape: (30,)
state
Querétaro           133955.913281
Guanajuato          133277.965833
Nuevo León          129221.985663
Distrito Federal    128347.267426
Quintana Roo        128065.416053
Name: price_usd, dtype: float64
VimeoVideo("656378435", h="b3765f3339", width=600)
mean_price_by_state.plot(
    kind="bar",
    xlabel="State",
    ylabel="Price [USD]",
    title="Mean House price by State"
);

Task 1.4.3: Use mean_price_by_state to create a bar chart of your results. Make sure the states are sorted from the highest to lowest mean, that you label the x-axis as "State" and the y-axis as "Mean Price [USD]", and give the chart the title "Mean House Price by State".

Create a bar chart using pandas.
# Create bar chart from `mean_price_by_state` using pandas
plt.bar(x=mean_price_by_state.index, height=mean_price_by_state.values )
plt.xticks(rotation=90);
plt.xlabel("State")
plt.ylabel("Price [USD]")
plt.title("Mean House Price by State");

It seems odd that Querétaro would be the most expensive real estate market in Mexico when, according to recent GDP numbers, it's not in the top 10 state economies. With all the variations in house sizes across states, a better metric to look at would be price per m2. In order to do that, we need to create a new column.

VimeoVideo("656378342", h="2f4da7f7b4", width=600)
Task 1.4.4: Create a new column in df called "price_per_m2". This should be the price for each house divided by it's size.

Create new columns derived from existing columns in a DataFrame using pandas.
# Create "price_per_m2" column
df["price_per_m2"] = df["price_usd"]/df["area_m2"]
​
# Print object type, shape, and head
print("df type:", type(df))
print("df shape:", df.shape)
df.head()
df type: <class 'pandas.core.frame.DataFrame'>
df shape: (1736, 7)
property_type	state	lat	lon	area_m2	price_usd	price_per_m2
0	house	Estado de México	19.560181	-99.233528	150.0	67965.56	453.103733
1	house	Nuevo León	25.688436	-100.198807	186.0	63223.78	339.912796
2	apartment	Guerrero	16.767704	-99.764383	82.0	84298.37	1028.028902
3	apartment	Guerrero	16.829782	-99.911012	150.0	94308.80	628.725333
4	house	Yucatán	21.052583	-89.538639	205.0	105191.37	513.128634
Let's redo our bar chart from above, but this time with the mean of "price_per_m2" for each state.

VimeoVideo("656377991", h="c7319b0458", width=600)
Task 1.4.5: First, use the groupby method to create a Series where the index contains each state in the dataset and the values correspond to the mean house price per m2 for that state. Then use the Series to create a bar chart of your results. Make sure the states are sorted from the highest to lowest mean, that you label the x-axis as "State" and the y-axis as "Mean Price per M^2[USD]", and give the chart the title "Mean House Price per M^2 by State".

What's a Series?
Aggregate data using the groupby method in pandas.
Create a bar chart using pandas.
# Group `df` by "state", create bar chart of "price_per_m2"
(
    df
     .groupby("state")["price_per_m2"]
     .mean()
     .sort_values(ascending=False)
     .plot(
         kind="bar",
         xlabel="State",
         ylabel="Mean Price per M^2[USD]",
         title="Mean House Price per M^2 by State"
         )
);

​
Now we see that the capital Mexico City (Distrito Federal) is by far the most expensive market. Additionally, many of the top 10 states by GDP are also in the top 10 most expensive real estate markets. So it looks like this bar chart is a more accurate reflection of state real estate markets.

Research Question 2
Is there a relationship between home size and price?

From our previous question, we know that the location of a home affects its price (especially if it's in Mexico City), but what about home size? Does the size of a house influence price?

A scatter plot can be helpful when evaluating the relationship between two columns because it lets you see if two variables are correlated — in this case, if an increase in home size is associated with an increase in price.

VimeoVideo("656377758", h="62546c7b86", width=600)
( 
    df.plot(
        kind="scatter",
        x ="area_m2",
        y = "price_usd",
        xlabel ="Area [m^2]",
        ylabel ="Price [USD]",
        title = "Price vs Area"
    )
);

Task 1.4.6: Create a scatter plot from df that represents price as a function of size. In other words, "area_m2" should be on the x-axis, and "price_usd" should be on the y-axis. Be sure to use expressive axis labels ("Area [sq meters]" and "Price [USD]", respectively).

What's a scatter plot?
What's correlation?
Create a scatter plot using Matplotlib.
# Create scatter plot of "price_usd" vs "area_m2"
plt.scatter(df["area_m2"],df["price_usd"])
​
# Add x-axis label
plt.xlabel("Area [m^2]")
​
# Add y-axis label
plt.ylabel("Price [USD]")
​
# Add title
​
Text(0, 0.5, 'Price [USD]')

While there's a good amount of variation, there's definitely a positive correlation — in other words, the bigger the house, the higher the price. But how can we quantify this correlation?

VimeoVideo("656377616", h="8d3b060e71", width=600)
Task 1.4.7: Using the corr method, calculate the Pearson correlation coefficient for "area_m2" and "price_usd".

What's a correlation coefficient?
Calculate the correlation coefficient for two Series using pandas.
# Calculate correlation of "price_usd" and "area_m2"
p_correlation = df["area_m2"].corr(df["price_usd"])
​
# Print correlation coefficient
print("Correlation of 'area_m2' and 'price_usd' (all Mexico):", p_correlation)
Correlation of 'area_m2' and 'price_usd' (all Mexico): 0.5855182453232062
The correlation coefficient is over 0.5, so there's a moderate relationship house size and price in Mexico. But does this relationship hold true in every state? Let's look at a couple of states, starting with Morelos.

VimeoVideo("656377515", h="d2478d38df", width=600)
Task 1.4.8: Create a new DataFrame named df_morelos. It should include all the houses from df that are in the state of Morelos.

Subset a DataFrame with a mask using pandas.
# Declare variable `df_morelos` by subsetting `df`
df_morelos = df[df["state"]=="Morelos"]
​
# Print object type, shape, and head
print("df_morelos type:", type(df_morelos))
print("df_morelos shape:", df_morelos.shape)
df_morelos.head()
df_morelos type: <class 'pandas.core.frame.DataFrame'>
df_morelos shape: (160, 7)
property_type	state	lat	lon	area_m2	price_usd	price_per_m2
6	house	Morelos	18.812605	-98.954826	281.0	151509.56	539.179929
9	house	Morelos	18.804197	-98.932816	117.0	63223.78	540.374188
18	house	Morelos	18.855343	-99.241142	73.0	36775.16	503.769315
49	house	Morelos	18.804197	-98.932816	130.0	65858.10	506.600769
55	house	Morelos	18.960244	-99.212962	305.0	227351.46	745.414623
VimeoVideo("656377395", h="bd93b05ff9", width=600)
Task 1.4.9: Using df_morelos, create a scatter plot that shows price vs area. Make sure to use the same axis labels as your last scatter plot. The title should be "Morelos: Price vs. Area".

What's a scatter plot?
Create a scatter plot using Matplotlib.
# Create scatter plot of "price_usd" vs "area_m2" in Morelos
(
    df_morelos.plot(
        kind="scatter",
        x="area_m2",
        y="price_usd",
        xlabel = "Area [M^2]",
        ylabel = "Price [USD]",
        title = "Area vs Price"
    )
);

# Create scatter plot of "price_usd" vs "area_m2" in Morelos
plt.scatter(x=df_morelos["area_m2"], y = df_morelos["price_usd"])
# Add x-axis label
plt.xlabel("Area [M^2]")
​
# Add y-axis label
plt.ylabel("Price [USD]")
​
# Add title
plt.title("Area vs Price");

Wow! It looks like the correlation is even stronger within Morelos. Let's calculate the correlation coefficient and verify that that's the case.

VimeoVideo("656377340", h="664cb44291", width=600)
Task 1.4.10: Using the corr method, calculate the Pearson correlation coefficient for "area_m2" and "price_usd" in df_morelos.

What's a correlation coefficient?
Calculate the correlation coefficient for two Series using pandas.WQU WorldQuant University Applied Data Science Lab QQQQ
# Calculate correlation of "price_usd" and "area_m2" in `df_morelos`
p_correlation = df_morelos["area_m2"].corr(df_morelos["price_usd"])
​
# Print correlation coefficient
print("Correlation of 'area_m2' and 'price_usd' (Morelos):", p_correlation)
Correlation of 'area_m2' and 'price_usd' (Morelos): 0.8498077608713708
With a correlation coefficient that high, we can say that there's a strong relationship between house size and price in Morelos.

To conclude, let's look at the capital Mexico City (Distrito Federal).

VimeoVideo("656376911", h="19666a4c87", width=600)
Task 1.4.11: First, create a new DataFrame called df_mexico_city that includes all the observations from df that are part of the Distrito Federal. Next, create a scatter plot that shows price vs area. Don't forget to label the x- and y-axis and use the title "Mexico City: Price vs. Area". Finally, calculate the correlation coefficient for "area_m2" and "price_usd" in df_mexico_city.

Calculate the correlation coefficient for two Series using pandas.
Create a scatter plot using Matplotlib.
Subset a DataFrame with a mask using pandas.
# Declare variable `df_mexico_city` by subsetting `df`
df_mexico_city = df[df["state"]=="Distrito Federal"]
​
# Print object type and shape
print("df_mexico_city type:", type(df_mexico_city))
print("df_mexico_city shape:", df_mexico_city.shape)
​
# Create a scatter plot "price_usd" vs "area_m2" in Distrito Federal
plt.scatter(df_mexico_city["area_m2"], df_mexico_city["price_usd"])  # REMOVERHS
​
# Add x-axis label
plt.xlabel("Area [sq meters]")  # REMOVERHS
​
# Add y-axis label
plt.ylabel("Price [USD]")  # REMOVERHS
​
# Add title
plt.title("Mexico City: Price vs. Area")  # REMOVERHS
​
# Calculate correlation of "price_usd" and "area_m2" in `df_mexico_city`
p_correlation = df_mexico_city["area_m2"].corr(df_mexico_city["price_usd"])
​
# Print correlation coefficient
print("Correlation of 'area_m2' and 'price_usd' (Mexico City):", p_correlation)
df_mexico_city type: <class 'pandas.core.frame.DataFrame'>
df_mexico_city shape: (303, 7)
Correlation of 'area_m2' and 'price_usd' (Mexico City): 0.41070392130717887

Looking at the scatter plot and correlation coefficient, there's see a weak relationship between size and price. How should we interpret this?

One interpretation is that the relationship we see between size and price in many states doesn't hold true in the country's biggest and most economically powerful urban center because there are other factors that have a larger influence on price. In fact, in the next project, we're going to look at another important Latin American city — Buenos Aires, Argentina — and build a model that predicts housing price by taking much more than size into account.

Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
12
Python 3 (ipykernel) | Busy
0
014-size-or-location.ipynb
Ln 1, Col 1
Mode: Command