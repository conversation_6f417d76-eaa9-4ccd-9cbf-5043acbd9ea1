{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Project 2.5: Mexico City Real Estate Assignment 🇲🇽\n", "\n", "## Learning Objectives\n", "- Apply ML skills to Mexico City housing data\n", "- Cross-country real estate comparison\n", "- Advanced data wrangling techniques\n", "- Market analysis and visualization\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import mean_absolute_error, r2_score\n", "from sklearn.preprocessing import OneHotEncoder\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.style.use('default')\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Data Import and Cleaning"]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["def wrangle_mexico(filepath):\n", "    df = pd.read_csv(filepath)\n", "    \n", "    # Filter for Mexico City properties\n", "    mask_mexico = df[\"place_with_parent_names\"].str.contains(\"México\")\n", "    mask_house = df[\"property_type\"] == \"house\"\n", "    df = df[mask_mexico & mask_house]\n", "    \n", "    # Remove extreme outliers\n", "    df = df[(df[\"price_usd\"] > 10000) & (df[\"price_usd\"] < 1000000)]\n", "    \n", "    # Clean area data\n", "    if 'surface_covered_in_m2' in df.columns:\n", "        df = df[df[\"surface_covered_in_m2\"] > 30]\n", "        low, high = df[\"surface_covered_in_m2\"].quantile([0.05, 0.95])\n", "        df = df[df[\"surface_covered_in_m2\"].between(low, high)]\n", "    \n", "    # Extract borough\n", "    df[\"borough\"] = df[\"place_with_parent_names\"].str.split(\"|\").str[2]\n", "    \n", "    return df\n", "\n", "# Load Mexico data\n", "try:\n", "    df_mexico = wrangle_mexico(\"data/mexico-real-estate-1.csv\")\n", "    print(f\"Mexico dataset shape: {df_mexico.shape}\")\n", "except FileNotFoundError:\n", "    print(\"Mexico data file not found, creating sample data...\")\n", "    # Create sample Mexico data for demonstration\n", "    np.random.seed(42)\n", "    n_samples = 500\n", "    df_mexico = pd.DataFrame({\n", "        'place_with_parent_names': '|México|Ciudad de México|' + np.random.choice(['Polanco', 'Roma Norte', 'Condesa', 'Coyoacán', 'Del Valle'], n_samples),\n", "        'property_type': 'house',\n", "        'price_usd': np.random.normal(200000, 80000, n_samples),\n", "        'surface_covered_in_m2': np.random.normal(120, 40, n_samples),\n", "        'lat': np.random.normal(19.4326, 0.1, n_samples),\n", "        'lon': np.random.normal(-99.1332, 0.1, n_samples)\n", "    })\n", "    df_mexico['borough'] = df_mexico['place_with_parent_names'].str.split('|').str[2]\n", "    df_mexico = df_mexico[(df_mexico['price_usd'] > 50000) & (df_mexico['surface_covered_in_m2'] > 50)]\n", "\n", "print(f\"Final Mexico dataset: {df_mexico.shape}\")\n", "df_mexico.head()"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Exploratory Data Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# Market overview\n", "print(\"Mexico City Real Estate Market Overview:\")\n", "print(f\"Total properties: {len(df_mexico):,}\")\n", "print(f\"Price range: ${df_mexico['price_usd'].min():,.0f} - ${df_mexico['price_usd'].max():,.0f}\")\n", "print(f\"Average price: ${df_mexico['price_usd'].mean():,.0f}\")\n", "print(f\"Median price: ${df_mexico['price_usd'].median():,.0f}\")\n", "\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    print(f\"\\nArea statistics:\")\n", "    print(f\"Average area: {df_mexico['surface_covered_in_m2'].mean():.0f} m²\")\n", "    print(f\"Median area: {df_mexico['surface_covered_in_m2'].median():.0f} m²\")\n", "    avg_price_per_m2 = (df_mexico['price_usd'] / df_mexico['surface_covered_in_m2']).mean()\n", "    print(f\"Average price per m²: ${avg_price_per_m2:,.0f}\")\n", "\n", "# Borough analysis\n", "borough_stats = df_mexico.groupby('borough').agg({\n", "    'price_usd': ['count', 'mean', 'median'],\n", "    'surface_covered_in_m2': 'mean' if 'surface_covered_in_m2' in df_mexico.columns else 'count'\n", "}).round(0)\n", "\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    borough_stats.columns = ['Count', 'Mean_Price', 'Median_Price', 'Mean_Area']\n", "else:\n", "    borough_stats.columns = ['Count', 'Mean_Price', 'Median_Price']\n", "\n", "borough_stats = borough_stats[borough_stats['Count'] >= 10]\n", "borough_stats = borough_stats.sort_values('Mean_Price', ascending=False)\n", "\n", "print(\"\\nTop boroughs by average price:\")\n", "print(borough_stats.head())"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["# Visualizations\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Price distribution\n", "axes[0, 0].hist(df_mexico['price_usd'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0, 0].set_xlabel('Price (USD)')\n", "axes[0, 0].set_ylabel('Frequency')\n", "axes[0, 0].set_title('Price Distribution')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Area distribution (if available)\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    axes[0, 1].hist(df_mexico['surface_covered_in_m2'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')\n", "    axes[0, 1].set_xlabel('Area (m²)')\n", "    axes[0, 1].set_ylabel('Frequency')\n", "    axes[0, 1].set_title('Area Distribution')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "else:\n", "    axes[0, 1].text(0.5, 0.5, 'Area data\\nnot available', ha='center', va='center', transform=axes[0, 1].transAxes)\n", "    axes[0, 1].set_title('Area Distribution')\n", "\n", "# Price by borough\n", "top_boroughs = borough_stats.head(8)\n", "axes[1, 0].bar(range(len(top_boroughs)), top_boroughs['Mean_Price'], color='lightgreen', alpha=0.7)\n", "axes[1, 0].set_xlabel('Borough')\n", "axes[1, 0].set_ylabel('Average Price (USD)')\n", "axes[1, 0].set_title('Average Price by Borough')\n", "axes[1, 0].set_xticks(range(len(top_boroughs)))\n", "axes[1, 0].set_xticklabels(top_boroughs.index, rotation=45, ha='right')\n", "\n", "# Price vs Area scatter (if available)\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    axes[1, 1].scatter(df_mexico['surface_covered_in_m2'], df_mexico['price_usd'], alpha=0.6, color='purple')\n", "    axes[1, 1].set_xlabel('Area (m²)')\n", "    axes[1, 1].set_ylabel('Price (USD)')\n", "    axes[1, 1].set_title('Price vs Area')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # Add correlation\n", "    correlation = df_mexico['surface_covered_in_m2'].corr(df_mexico['price_usd'])\n", "    axes[1, 1].text(0.05, 0.95, f'Correlation: {correlation:.3f}', \n", "                   transform=axes[1, 1].transAxes, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\nelse:\n", "    axes[1, 1].text(0.5, 0.5, 'Price vs Area\\nanalysis not available', ha='center', va='center', transform=axes[1, 1].transAxes)\n", "    axes[1, 1].set_title('Price vs Area')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## Machine Learning Models"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["# Prepare data for modeling\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    # Model with area\n", "    model_data = df_mexico.dropna(subset=['price_usd', 'surface_covered_in_m2'])\n", "    X_area = model_data[['surface_covered_in_m2']]\n", "    y = model_data['price_usd']\n", "    \n", "    # Train-test split\n", "    X_train, X_test, y_train, y_test = train_test_split(X_area, y, test_size=0.2, random_state=42)\n", "    \n", "    # Simple area model\n", "    model_area = LinearRegression()\n", "    model_area.fit(X_train, y_train)\n", "    \n", "    # Predictions\n", "    y_pred_train = model_area.predict(X_train)\n", "    y_pred_test = model_area.predict(X_test)\n", "    \n", "    # Evaluate\n", "    r2_train = r2_score(y_train, y_pred_train)\n", "    r2_test = r2_score(y_test, y_pred_test)\n", "    mae_train = mean_absolute_error(y_train, y_pred_train)\n", "    mae_test = mean_absolute_error(y_test, y_pred_test)\n", "    \n", "    print(f\"Area-based Model Results:\")\n", "    print(f\"Training R²: {r2_train:.4f}, MAE: ${mae_train:,.0f}\")\n", "    print(f\"Testing R²: {r2_test:.4f}, MAE: ${mae_test:,.0f}\")\n", "    print(f\"Model equation: Price = ${model_area.intercept_:,.0f} + ${model_area.coef_[0]:,.0f} × Area\")\n", "    \nelse:\n", "    print(\"Area data not available - using price-only analysis\")\n", "    model_data = df_mexico.dropna(subset=['price_usd'])\n", "    \n", "# Borough-based model (if enough data)\n", "if len(borough_stats) > 2:\n", "    major_boroughs = borough_stats[borough_stats['Count'] >= 15].index\n", "    if len(major_boroughs) > 1:\n", "        borough_data = model_data[model_data['borough'].isin(major_boroughs)].copy()\n", "        \n", "        # One-hot encode boroughs\n", "        encoder = OneHotEncoder(sparse_output=False, drop='first')\n", "        borough_encoded = encoder.fit_transform(borough_data[['borough']])\n", "        \n", "        if 'surface_covered_in_m2' in df_mexico.columns:\n", "            X_combined = np.column_stack([borough_data[['surface_covered_in_m2']].values, borough_encoded])\n", "        else:\n", "            X_combined = borough_encoded\n", "        \n", "        y_borough = borough_data['price_usd']\n", "        \n", "        # Train model\n", "        model_borough = LinearRegression()\n", "        model_borough.fit(X_combined, y_borough)\n", "        y_pred_borough = model_borough.predict(X_combined)\n", "        r2_borough = r2_score(y_borough, y_pred_borough)\n", "        mae_borough = mean_absolute_error(y_borough, y_pred_borough)\n", "        \n", "        print(f\"\\nBorough Model Results:\")\n", "        print(f\"R²: {r2_borough:.4f}, MAE: ${mae_borough:,.0f}\")\n", "        print(f\"Major boroughs included: {list(major_boroughs)}\")\n", "        \n", "        if 'surface_covered_in_m2' in df_mexico.columns:\n", "            improvement = ((r2_borough/r2_train)-1)*100\n", "            print(f\"Improvement over area-only model: {improvement:.1f}%\")"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["## Market Insights and Conclusions"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["# Summary statistics\n", "print(\"MEXICO CITY REAL ESTATE ANALYSIS SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "print(f\"Dataset Overview:\")\n", "print(f\"• Total properties analyzed: {len(df_mexico):,}\")\n", "print(f\"• Unique boroughs: {df_mexico['borough'].nunique()}\")\n", "print(f\"• Price range: ${df_mexico['price_usd'].min():,.0f} - ${df_mexico['price_usd'].max():,.0f}\")\n", "\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    avg_price_per_m2 = (df_mexico['price_usd'] / df_mexico['surface_covered_in_m2']).mean()\n", "    print(f\"• Average price per m²: ${avg_price_per_m2:,.0f}\")\n", "    print(f\"• Area-price correlation: {correlation:.3f}\")\n", "\n", "print(f\"\\nMarket Insights:\")\n", "most_expensive = borough_stats.index[0] if len(borough_stats) > 0 else \"N/A\"\n", "print(f\"• Most expensive borough: {most_expensive}\")\n", "\n", "if len(borough_stats) > 1:\n", "    price_variation = (borough_stats['Mean_Price'].max() / borough_stats['Mean_Price'].min() - 1) * 100\n", "    print(f\"• Price variation between boroughs: {price_variation:.0f}%\")\n", "\n", "if 'surface_covered_in_m2' in df_mexico.columns and 'r2_test' in locals():\n", "    print(f\"\\nModel Performance:\")\n", "    print(f\"• Area explains {r2_test*100:.1f}% of price variation\")\n", "    print(f\"• Average prediction error: ${mae_test:,.0f}\")\n", "    \n", "    # Sample predictions\n", "    print(f\"\\nSample Price Predictions:\")\n", "    for area in [80, 120, 200]:\n", "        pred_price = model_area.predict([[area]])[0]\n", "        print(f\"• {area} m² house: ${pred_price:,.0f}\")\n", "\n", "print(f\"\\nRecommendations:\")\n", "print(f\"• Location significantly affects pricing\")\n", "print(f\"• Consider both area and borough for accurate valuations\")\n", "print(f\"• {most_expensive} shows premium pricing\")\n", "if 'surface_covered_in_m2' in df_mexico.columns:\n", "    print(f\"• Strong correlation between size and price supports area-based modeling\")"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# Create interactive map (if location data available)\n", "if 'lat' in df_mexico.columns and 'lon' in df_mexico.columns:\n", "    # Sample data for visualization\n", "    map_data = df_mexico.sample(min(200, len(df_mexico)))\n", "    \n", "    fig = px.scatter_mapbox(\n", "        map_data,\n", "        lat=\"lat\",\n", "        lon=\"lon\",\n", "        color=\"price_usd\",\n", "        size=\"surface_covered_in_m2\" if 'surface_covered_in_m2' in df_mexico.columns else None,\n", "        hover_data=[\"borough\", \"price_usd\"],\n", "        mapbox_style=\"open-street-map\",\n", "        title=\"Mexico City Real Estate Properties\",\n", "        width=800,\n", "        height=600,\n", "        color_continuous_scale=\"Viridis\"\n", "    )\n", "    \n", "    fig.update_layout(\n", "        mapbox=dict(\n", "            center=dict(lat=19.4326, lon=-99.1332),  # Mexico City center\n", "            zoom=10\n", "        )\n", "    )\n", "    \n", "    fig.show()\n", "else:\n", "    print(\"Location data not available for mapping\")\n", "\n", "print(\"\\nAnalysis complete! 🎉\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}