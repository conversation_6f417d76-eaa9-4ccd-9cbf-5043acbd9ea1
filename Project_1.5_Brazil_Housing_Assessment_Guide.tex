\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 1.5: Brazil Housing Assessment}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 1.5: Brazil Housing Assessment} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Your First Complete Data Science Project!}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Your Final Challenge!}

Congratulations on making it to Project 1.5! This is your chance to bring together everything you've learned and apply it to Brazilian real estate data. Think of this as your "graduation project" for the data preparation phase of your data science journey.

\subsection{What You'll Accomplish Today}
\begin{itemize}
    \item Combine data from multiple sources like a pro
    \item Clean messy, real-world data with confidence
    \item Handle different currencies and data formats
    \item Create meaningful geographic analysis
    \item Build a comprehensive data science workflow
    \item Present findings like a professional analyst
\end{itemize}

\begin{concept}
\textbf{What Makes This a "Capstone" Project?}
This project integrates all your previous skills:
\begin{itemize}
    \item \textbf{Data Organization:} Multiple datasets with different structures
    \item \textbf{Data Cleaning:} String processing, missing values, standardization
    \item \textbf{Data Analysis:} Grouping, correlations, comparisons
    \item \textbf{Visualization:} Charts that tell compelling stories
    \item \textbf{Real-world Application:} Working with actual market data
\end{itemize}
\end{concept}

\section{Task 1.5.1: Loading Brazilian Real Estate Data}

Let's start by loading our two Brazilian datasets and seeing what we're working with.

\begin{lstlisting}[caption=Loading Multiple Datasets]
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Load the first Brazilian real estate dataset
df1 = pd.read_csv("data/brasil-real-estate-1.csv")
print("Dataset 1:")
print(f"Shape: {df1.shape}")
print(f"Columns: {list(df1.columns)}")
print("\nFirst few rows:")
print(df1.head())

# Load the second Brazilian real estate dataset
df2 = pd.read_csv("data/brasil-real-estate-2.csv")
print("\n" + "="*50)
print("Dataset 2:")
print(f"Shape: {df2.shape}")
print(f"Columns: {list(df2.columns)}")
print("\nFirst few rows:")
print(df2.head())

# Compare the datasets
print("\n" + "="*50)
print("Quick Comparison:")
print(f"Dataset 1: {df1.shape[0]} properties, {df1.shape[1]} features")
print(f"Dataset 2: {df2.shape[0]} properties, {df2.shape[1]} features")
\end{lstlisting}

\begin{learningtip}
\textbf{Working with Multiple Datasets:}
\begin{itemize}
    \item Always examine each dataset separately first
    \item Look for common columns that might allow joining
    \item Note differences in column names and data formats
    \item Check for overlapping or complementary information
\end{itemize}
\end{learningtip}

\section{Task 1.5.2: Initial Data Quality Assessment}

Before we start cleaning, let's understand what data quality issues we're facing.

\begin{lstlisting}[caption=Data Quality Assessment]
# Check for missing values in both datasets
print("Missing Values Analysis:")
print("\nDataset 1 missing values:")
missing_1 = df1.isnull().sum()
print(missing_1[missing_1 > 0])

print("\nDataset 2 missing values:")
missing_2 = df2.isnull().sum()
print(missing_2[missing_2 > 0])

# Check data types
print("\n" + "="*50)
print("Data Types Analysis:")
print("\nDataset 1 data types:")
print(df1.dtypes)

print("\nDataset 2 data types:")
print(df2.dtypes)

# Look for any obvious data quality issues
print("\n" + "="*50)
print("Quick Data Quality Check:")
print(f"Dataset 1 - any duplicate rows? {df1.duplicated().sum()}")
print(f"Dataset 2 - any duplicate rows? {df2.duplicated().sum()}")

# Check if both datasets cover the same geography
if 'state' in df1.columns:
    print(f"Dataset 1 states: {df1['state'].nunique()} unique states")
if 'state' in df2.columns:
    print(f"Dataset 2 states: {df2['state'].nunique()} unique states")
\end{lstlisting}

\begin{concept}
\textbf{Data Quality Assessment Checklist:}
\begin{itemize}
    \item \textbf{Completeness:} How many missing values?
    \item \textbf{Consistency:} Are data types correct?
    \item \textbf{Uniqueness:} Any duplicate records?
    \item \textbf{Coverage:} Does the data cover what you expect?
    \item \textbf{Format:} Are values in expected formats?
\end{itemize}
\end{concept}

\section{Task 1.5.3: Cleaning Dataset 1}

Let's clean the first dataset step by step.

\begin{lstlisting}[caption=Cleaning Dataset 1]
# Make a copy to preserve original data
df1_clean = df1.copy()

# Remove rows with missing values
print(f"Dataset 1 before cleaning: {len(df1_clean)} rows")
df1_clean = df1_clean.dropna()
print(f"Dataset 1 after removing missing values: {len(df1_clean)} rows")

# Extract state information from hierarchical place names
# Format is typically "Brasil|State|City"
if 'place_with_parent_names' in df1_clean.columns:
    print("\nExtracting state information...")
    df1_clean['state'] = df1_clean['place_with_parent_names'].str.split('|').str[1]
    
    # Check the extraction worked
    print("States found in Dataset 1:")
    print(df1_clean['state'].value_counts().head())
    
    # Clean up by removing the original column
    df1_clean = df1_clean.drop('place_with_parent_names', axis=1)

# Convert currency if needed (from Brazilian Real to USD)
if 'price_brl' in df1_clean.columns:
    print("\nConverting currency from BRL to USD...")
    # Using approximate exchange rate: 5.2 BRL = 1 USD
    exchange_rate = 5.2
    df1_clean['price_usd'] = (df1_clean['price_brl'] / exchange_rate).round(2)
    
    print(f"Price range in BRL: {df1_clean['price_brl'].min():,.0f} to {df1_clean['price_brl'].max():,.0f}")
    print(f"Price range in USD: {df1_clean['price_usd'].min():,.0f} to {df1_clean['price_usd'].max():,.0f}")
    
    # Remove the BRL column to avoid confusion
    df1_clean = df1_clean.drop('price_brl', axis=1)

print(f"\nDataset 1 final shape: {df1_clean.shape}")
print("Final columns:", list(df1_clean.columns))
\end{lstlisting}

\begin{learningtip}
\textbf{Currency Conversion Best Practices:}
\begin{itemize}
    \item Always document the exchange rate you used
    \item Note the date - exchange rates change daily
    \item Round to appropriate precision (2 decimal places for currency)
    \item Keep conversion factor as a variable for easy updates
    \item Consider using real-time exchange rate APIs for production systems
\end{itemize}
\end{learningtip}

\section{Task 1.5.4: Cleaning Dataset 2}

Now let's clean the second dataset with similar care.

\begin{lstlisting}[caption=Cleaning Dataset 2]
# Make a copy of the second dataset
df2_clean = df2.copy()

# Handle missing values
print(f"Dataset 2 before cleaning: {len(df2_clean)} rows")
missing_before = df2_clean.isnull().sum().sum()
print(f"Total missing values: {missing_before}")

# Remove rows where critical information is missing
# (adapt this based on your actual data)
critical_columns = ['price_usd', 'area_m2']  # Adjust based on actual columns
for col in critical_columns:
    if col in df2_clean.columns:
        df2_clean = df2_clean.dropna(subset=[col])

print(f"Dataset 2 after cleaning: {len(df2_clean)} rows")

# Standardize column names to match Dataset 1
column_mapping = {
    'area_m2': 'area_sqm',  # Standardize area column name
    # Add other mappings as needed based on your data
}

for old_name, new_name in column_mapping.items():
    if old_name in df2_clean.columns:
        df2_clean = df2_clean.rename(columns={old_name: new_name})
        print(f"Renamed '{old_name}' to '{new_name}'")

# Add any missing columns that Dataset 1 has
if 'state' not in df2_clean.columns and 'lat' in df2_clean.columns:
    print("\nState information not directly available in Dataset 2")
    print("We'll work with coordinate data for geographic analysis")

print(f"\nDataset 2 final shape: {df2_clean.shape}")
print("Final columns:", list(df2_clean.columns))
\end{lstlisting}

\section{Task 1.5.5: Combining the Datasets}

Let's combine our cleaned datasets into one comprehensive dataset.

\begin{lstlisting}[caption=Combining Multiple Datasets]
# Find common columns between datasets
common_columns = list(set(df1_clean.columns) & set(df2_clean.columns))
print("Common columns:", common_columns)

# Prepare datasets for combination
# Add source identifier to track which dataset each row came from
df1_clean['data_source'] = 'dataset_1'
df2_clean['data_source'] = 'dataset_2'

# Ensure both datasets have the same columns
all_columns = list(set(df1_clean.columns) | set(df2_clean.columns))
print(f"Total unique columns: {len(all_columns)}")

# Add missing columns with NaN values
for col in all_columns:
    if col not in df1_clean.columns:
        df1_clean[col] = np.nan
    if col not in df2_clean.columns:
        df2_clean[col] = np.nan

# Combine the datasets
df_combined = pd.concat([df1_clean, df2_clean], ignore_index=True)

print(f"\nCombined dataset shape: {df_combined.shape}")
print("Data source distribution:")
print(df_combined['data_source'].value_counts())

# Quick quality check
print(f"\nCombined dataset summary:")
print(f"- Total properties: {len(df_combined):,}")
print(f"- Price range: ${df_combined['price_usd'].min():,.0f} to ${df_combined['price_usd'].max():,.0f}")
if 'area_sqm' in df_combined.columns:
    print(f"- Size range: {df_combined['area_sqm'].min():.0f} to {df_combined['area_sqm'].max():.0f} sqm")
\end{lstlisting}

\begin{concept}
\textbf{Data Integration Strategies:}
\begin{itemize}
    \item \textbf{Vertical Stacking:} Combine datasets with similar structures (like we did)
    \item \textbf{Horizontal Joining:} Merge datasets using common keys (like property IDs)
    \item \textbf{Source Tracking:} Always keep track of where each record came from
    \item \textbf{Schema Harmonization:} Make column names and types consistent
\end{itemize}
\end{concept}

\section{Task 1.5.6: Geographic Analysis of Brazilian Markets}

Now let's analyze the Brazilian real estate market by region.

\begin{lstlisting}[caption=Brazilian Regional Analysis]
# Analyze by state if available
if 'state' in df_combined.columns and df_combined['state'].notna().any():
    print("BRAZILIAN REAL ESTATE MARKET ANALYSIS BY STATE")
    print("=" * 60)
    
    # Get state-level statistics
    state_analysis = df_combined.groupby('state').agg({
        'price_usd': ['count', 'mean', 'median'],
        'area_sqm': 'mean'
    }).round(2)
    
    state_analysis.columns = ['Count', 'Avg_Price_USD', 'Median_Price_USD', 'Avg_Size_SqM']
    state_analysis = state_analysis.sort_values('Avg_Price_USD', ascending=False)
    
    print("\nTop 10 Most Expensive States:")
    print(state_analysis.head(10))
    
    # Visualize state comparison
    plt.figure(figsize=(14, 6))
    
    # Top 10 states by average price
    top_states = state_analysis.head(10)
    plt.subplot(1, 2, 1)
    bars = plt.bar(range(len(top_states)), top_states['Avg_Price_USD'], 
                   color='lightblue', edgecolor='darkblue')
    plt.title('Average Property Price by State (Top 10)')
    plt.xlabel('State')
    plt.ylabel('Average Price (USD)')
    plt.xticks(range(len(top_states)), top_states.index, rotation=45, ha='right')
    
    # Add value labels
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1000,
                 f'${height:,.0f}', ha='center', va='bottom', fontsize=9)
    
    # Property count by state
    plt.subplot(1, 2, 2)
    plt.bar(range(len(top_states)), top_states['Count'], 
            color='lightcoral', edgecolor='darkred')
    plt.title('Number of Properties by State (Top 10)')
    plt.xlabel('State')
    plt.ylabel('Number of Properties')
    plt.xticks(range(len(top_states)), top_states.index, rotation=45, ha='right')
    
    plt.tight_layout()
    plt.show()

else:
    print("State information not available - analyzing by data source instead")
    source_analysis = df_combined.groupby('data_source').agg({
        'price_usd': ['count', 'mean', 'median'],
        'area_sqm': 'mean'
    }).round(2)
    print("\nAnalysis by data source:")
    print(source_analysis)
\end{lstlisting}

\section{Task 1.5.7: Price-Size Relationship Analysis}

Let's investigate how property size relates to price in the Brazilian market.

\begin{lstlisting}[caption=Analyzing Price-Size Relationships]
# Calculate price per square meter
df_combined['price_per_sqm'] = df_combined['price_usd'] / df_combined['area_sqm']

# Remove any infinite or extremely high values that might be errors
df_analysis = df_combined[
    (df_combined['price_per_sqm'] > 0) & 
    (df_combined['price_per_sqm'] < df_combined['price_per_sqm'].quantile(0.99))
].copy()

print(f"Properties for analysis: {len(df_analysis):,}")
print(f"Price per sqm range: ${df_analysis['price_per_sqm'].min():.0f} - ${df_analysis['price_per_sqm'].max():.0f}")

# Create comprehensive size-price analysis
plt.figure(figsize=(15, 10))

# 1. Size vs Price scatter plot
plt.subplot(2, 3, 1)
plt.scatter(df_analysis['area_sqm'], df_analysis['price_usd'], alpha=0.6, color='green')
plt.title('Property Size vs Price')
plt.xlabel('Area (sqm)')
plt.ylabel('Price (USD)')
plt.grid(True, alpha=0.3)

# 2. Price per sqm distribution
plt.subplot(2, 3, 2)
plt.hist(df_analysis['price_per_sqm'], bins=50, color='orange', alpha=0.7, edgecolor='black')
plt.title('Distribution of Price per SqM')
plt.xlabel('Price per SqM (USD)')
plt.ylabel('Frequency')

# 3. Size distribution
plt.subplot(2, 3, 3)
plt.hist(df_analysis['area_sqm'], bins=50, color='purple', alpha=0.7, edgecolor='black')
plt.title('Distribution of Property Sizes')
plt.xlabel('Area (sqm)')
plt.ylabel('Frequency')

# 4. Price distribution
plt.subplot(2, 3, 4)
plt.hist(df_analysis['price_usd'], bins=50, color='red', alpha=0.7, edgecolor='black')
plt.title('Distribution of Prices')
plt.xlabel('Price (USD)')
plt.ylabel('Frequency')

# 5. Size vs Price per sqm
plt.subplot(2, 3, 5)
plt.scatter(df_analysis['area_sqm'], df_analysis['price_per_sqm'], alpha=0.6, color='blue')
plt.title('Size vs Price per SqM')
plt.xlabel('Area (sqm)')
plt.ylabel('Price per SqM (USD)')
plt.grid(True, alpha=0.3)

# 6. Correlation summary (as text)
correlation_size_price = df_analysis['area_sqm'].corr(df_analysis['price_usd'])
correlation_size_price_sqm = df_analysis['area_sqm'].corr(df_analysis['price_per_sqm'])

summary_text = f"""
Key Correlations:
Size vs Price: {correlation_size_price:.3f}
Size vs Price/SqM: {correlation_size_price_sqm:.3f}

Market Summary:
Avg Price: ${df_analysis['price_usd'].mean():,.0f}
Avg Size: {df_analysis['area_sqm'].mean():.0f} sqm
Avg Price/SqM: ${df_analysis['price_per_sqm'].mean():.0f}
"""

plt.subplot(2, 3, 6)
plt.text(0.1, 0.5, summary_text, fontsize=10, 
         verticalalignment='center', transform=plt.gca().transAxes)
plt.title('Market Summary')
plt.axis('off')

plt.tight_layout()
plt.show()

print("\nCorrelation Analysis:")
print(f"Size vs Price correlation: {correlation_size_price:.3f}")
print(f"Size vs Price per SqM correlation: {correlation_size_price_sqm:.3f}")
\end{lstlisting>

\begin{learningtip}
\textbf{Interpreting Property Market Correlations:}
\begin{itemize}
    \item \textbf{Size vs Price:} Usually positive (bigger = more expensive)
    \item \textbf{Size vs Price/SqM:} Often negative (economies of scale)
    \item \textbf{Market Efficiency:} Strong correlations suggest efficient pricing
    \item \textbf{Outliers:} Properties that don't fit patterns might be special cases
\end{itemize}
\end{learningtip>

\section{Task 1.5.8: Creating Property Categories}

Let's categorize properties to better understand market segments.

\begin{lstlisting}[caption=Creating Market Segments]
# Create size categories
def categorize_size(area):
    if area < 50:
        return "Compact"
    elif area < 100:
        return "Medium"
    elif area < 200:
        return "Large"
    else:
        return "Estate"

# Create price categories
price_percentiles = df_analysis['price_usd'].quantile([0.25, 0.5, 0.75])
def categorize_price(price):
    if price <= price_percentiles[0.25]:
        return "Affordable"
    elif price <= price_percentiles[0.5]:
        return "Mid-Market"
    elif price <= price_percentiles[0.75]:
        return "Premium"
    else:
        return "Luxury"

# Apply categorizations
df_analysis['size_category'] = df_analysis['area_sqm'].apply(categorize_size)
df_analysis['price_category'] = df_analysis['price_usd'].apply(categorize_price)

# Analyze categories
print("BRAZILIAN REAL ESTATE MARKET SEGMENTS")
print("=" * 50)

# Size category analysis
size_analysis = df_analysis.groupby('size_category').agg({
    'price_usd': ['count', 'mean'],
    'area_sqm': 'mean',
    'price_per_sqm': 'mean'
}).round(0)

print("\nBy Size Category:")
print(size_analysis)

# Price category analysis
price_analysis = df_analysis.groupby('price_category').agg({
    'price_usd': ['count', 'mean'],
    'area_sqm': 'mean',
    'price_per_sqm': 'mean'
}).round(0)

print("\nBy Price Category:")
print(price_analysis)

# Cross-tabulation
cross_tab = pd.crosstab(df_analysis['size_category'], df_analysis['price_category'])
print("\nSize vs Price Category Cross-tabulation:")
print(cross_tab)

# Visualize categories
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
size_counts = df_analysis['size_category'].value_counts()
plt.pie(size_counts.values, labels=size_counts.index, autopct='%1.1f%%')
plt.title('Distribution by Size Category')

plt.subplot(1, 3, 2)
price_counts = df_analysis['price_category'].value_counts()
plt.pie(price_counts.values, labels=price_counts.index, autopct='%1.1f%%')
plt.title('Distribution by Price Category')

plt.subplot(1, 3, 3)
# Average price per sqm by size category
avg_price_sqm_by_size = df_analysis.groupby('size_category')['price_per_sqm'].mean()
plt.bar(avg_price_sqm_by_size.index, avg_price_sqm_by_size.values, 
        color=['lightblue', 'lightgreen', 'lightcoral', 'lightyellow'])
plt.title('Avg Price/SqM by Size Category')
plt.ylabel('Price per SqM (USD)')
plt.xticks(rotation=45)

plt.tight_layout()
plt.show()
\end{lstlisting}

\section{Task 1.5.9: Regional Market Comparison}

Let's compare different regions or data sources if state data isn't available.

\begin{lstlisting}[caption=Regional Market Analysis]
# Compare by data source or state
grouping_var = 'state' if 'state' in df_analysis.columns and df_analysis['state'].notna().any() else 'data_source'

print(f"REGIONAL COMPARISON BY {grouping_var.upper()}")
print("=" * 60)

# Regional analysis
regional_stats = df_analysis.groupby(grouping_var).agg({
    'price_usd': ['count', 'mean', 'median', 'std'],
    'area_sqm': ['mean', 'median'],
    'price_per_sqm': ['mean', 'median']
}).round(2)

# Flatten column names
regional_stats.columns = ['_'.join(col).strip() for col in regional_stats.columns]
regional_stats = regional_stats.sort_values('price_usd_mean', ascending=False)

print("\nRegional Market Statistics:")
print(regional_stats)

# Find the most and least expensive regions
most_expensive = regional_stats.index[0]
least_expensive = regional_stats.index[-1]

print(f"\nMost expensive region: {most_expensive}")
print(f"Least expensive region: {least_expensive}")

price_difference = (regional_stats.loc[most_expensive, 'price_usd_mean'] / 
                   regional_stats.loc[least_expensive, 'price_usd_mean'])
print(f"Price difference: {price_difference:.1f}x")

# Visualize regional differences
if len(regional_stats) > 1:
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.bar(regional_stats.index, regional_stats['price_usd_mean'], 
            color='skyblue', edgecolor='navy')
    plt.title('Average Price by Region')
    plt.ylabel('Average Price (USD)')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 2, 2)
    plt.bar(regional_stats.index, regional_stats['price_per_sqm_mean'], 
            color='lightcoral', edgecolor='darkred')
    plt.title('Average Price per SqM by Region')
    plt.ylabel('Price per SqM (USD)')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 2, 3)
    plt.bar(regional_stats.index, regional_stats['area_sqm_mean'], 
            color='lightgreen', edgecolor='darkgreen')
    plt.title('Average Property Size by Region')
    plt.ylabel('Average Size (sqm)')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 2, 4)
    plt.bar(regional_stats.index, regional_stats['price_usd_count'], 
            color='orange', edgecolor='darkorange')
    plt.title('Number of Properties by Region')
    plt.ylabel('Property Count')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.show()
\end{lstlisting}

\section{Task 1.5.10: Final Market Assessment and Recommendations}

Let's summarize our findings and create actionable insights.

\begin{lstlisting}[caption=Final Market Assessment]
print("BRAZILIAN REAL ESTATE MARKET ASSESSMENT")
print("=" * 60)
print("COMPREHENSIVE ANALYSIS SUMMARY")
print("=" * 60)

# Overall market summary
total_properties = len(df_analysis)
avg_price = df_analysis['price_usd'].mean()
median_price = df_analysis['price_usd'].median()
avg_size = df_analysis['area_sqm'].mean()
avg_price_per_sqm = df_analysis['price_per_sqm'].mean()

print(f"\n1. MARKET OVERVIEW:")
print(f"   - Total properties analyzed: {total_properties:,}")
print(f"   - Average property price: ${avg_price:,.0f}")
print(f"   - Median property price: ${median_price:,.0f}")
print(f"   - Average property size: {avg_size:.0f} sqm")
print(f"   - Average price per sqm: ${avg_price_per_sqm:.0f}")

# Market segments
print(f"\n2. MARKET SEGMENTS:")
for category in ['Compact', 'Medium', 'Large', 'Estate']:
    if category in df_analysis['size_category'].values:
        segment_data = df_analysis[df_analysis['size_category'] == category]
        count = len(segment_data)
        avg_price_segment = segment_data['price_usd'].mean()
        print(f"   - {category}: {count:,} properties, avg ${avg_price_segment:,.0f}")

# Regional insights
if grouping_var == 'state' and len(regional_stats) > 1:
    print(f"\n3. REGIONAL INSIGHTS:")
    print(f"   - Most expensive state: {most_expensive}")
    print(f"   - Least expensive state: {least_expensive}")
    print(f"   - Regional price variation: {price_difference:.1f}x difference")

# Investment insights
best_value_region = regional_stats.sort_values('price_per_sqm_mean').index[0]
print(f"\n4. INVESTMENT OPPORTUNITIES:")
print(f"   - Best value region: {best_value_region}")
print(f"   - Lowest price per sqm: ${regional_stats.loc[best_value_region, 'price_per_sqm_mean']:.0f}")

# Market efficiency
correlation = df_analysis['area_sqm'].corr(df_analysis['price_usd'])
if correlation > 0.7:
    efficiency = "highly efficient"
elif correlation > 0.5:
    efficiency = "moderately efficient"
else:
    efficiency = "potentially inefficient"

print(f"\n5. MARKET EFFICIENCY:")
print(f"   - Size-price correlation: {correlation:.3f}")
print(f"   - Market appears {efficiency}")

# Recommendations
print(f"\n6. RECOMMENDATIONS:")
print(f"   - For buyers: Consider {least_expensive} for affordability")
print(f"   - For investors: Focus on {best_value_region} for value")
print(f"   - For developers: {most_expensive} shows premium market demand")

# Data quality summary
data_coverage = (df_combined.notna().sum() / len(df_combined) * 100).round(1)
print(f"\n7. DATA QUALITY:")
print(f"   - Dataset 1 contributed: {len(df1_clean):,} properties")
print(f"   - Dataset 2 contributed: {len(df2_clean):,} properties")
print(f"   - Data completeness: {data_coverage.mean():.1f}% average")

print("\n" + "="*60)
print("ANALYSIS COMPLETE - READY FOR PRESENTATION!")
print("="*60)
\end{lstlisting}

\section{Summary: Your Data Science Journey Milestone}

\subsection{Technical Skills You've Mastered}
\begin{enumerate}
    \item \textbf{Multi-Source Data Integration:} Combining datasets with different structures
    \item \textbf{Complex Data Cleaning:} Handling missing values, currency conversion, string processing
    \item \textbf{Geographic Analysis:} Regional market comparison and insights
    \item \textbf{Market Segmentation:} Creating meaningful categories from continuous data
    \item \textbf{Comprehensive Reporting:} Summarizing findings for business decisions
    \item \textbf{End-to-End Workflow:} Complete data science project from raw data to insights
\end{enumerate}

\subsection{Real-World Applications}
\begin{itemize}
    \item International real estate investment analysis
    \item Market entry strategy for property developers
    \item Regional economic development planning
    \item Comparative market analysis for real estate professionals
    \item Investment portfolio diversification decisions
\end{itemize}

\subsection{Professional Skills Developed}
\begin{itemize}
    \item \textbf{Problem Solving:} Breaking complex problems into manageable steps
    \item \textbf{Data Quality Assurance:} Identifying and fixing data issues
    \item \textbf{Business Communication:} Translating analysis into actionable insights
    \item \textbf{Project Management:} Organizing and executing multi-step analysis
    \item \textbf{Critical Thinking:} Questioning data and validating findings
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Add a new property category based on price per square meter
    \item Calculate the percentage of properties in each size category
    \item Find the state with the most properties in the dataset
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Create a "value score" combining multiple factors (price, size, location)
    \item Analyze seasonal patterns if date information is available
    \item Build a simple property recommender based on user preferences
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Implement outlier detection and analysis for unusual properties
    \item Create an interactive dashboard using plotly or streamlit
    \item Develop a predictive model for property prices (preview of Project 2!)
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Add property category based on price per square meter}
\begin{lstlisting}[caption=Price per SqM Categories]
# Create price per sqm categories based on quartiles
price_sqm_quartiles = df_analysis['price_per_sqm'].quantile([0.25, 0.5, 0.75])

def categorize_price_sqm(price_sqm):
    if price_sqm <= price_sqm_quartiles[0.25]:
        return "Economy"
    elif price_sqm <= price_sqm_quartiles[0.5]:
        return "Standard"
    elif price_sqm <= price_sqm_quartiles[0.75]:
        return "Premium"
    else:
        return "Ultra-Premium"

df_analysis['price_sqm_category'] = df_analysis['price_per_sqm'].apply(categorize_price_sqm)

# Analyze the new categories
price_sqm_analysis = df_analysis.groupby('price_sqm_category').agg({
    'price_usd': ['count', 'mean'],
    'area_sqm': 'mean',
    'price_per_sqm': 'mean'
}).round(2)

print("Properties by Price per SqM Category:")
print(price_sqm_analysis)
\end{lstlisting}

\textbf{Exercise 2: Calculate percentage of properties in each size category}
\begin{lstlisting}[caption=Size Category Percentages]
# Calculate percentages for size categories
size_percentages = df_analysis['size_category'].value_counts(normalize=True) * 100

print("Size Category Distribution:")
for category, percentage in size_percentages.items():
    count = df_analysis['size_category'].value_counts()[category]
    print(f"{category}: {percentage:.1f}% ({count:,} properties)")

# Visualize
plt.figure(figsize=(8, 6))
plt.pie(size_percentages.values, labels=size_percentages.index, autopct='%1.1f%%')
plt.title('Distribution of Properties by Size Category')
plt.show()
\end{lstlisting}

\textbf{Exercise 3: Find state with most properties}
\begin{lstlisting}[caption=State with Most Properties]
if 'state' in df_combined.columns:
    state_counts = df_combined['state'].value_counts()
    top_state = state_counts.index[0]
    top_count = state_counts.iloc[0]
    
    print(f"State with most properties: {top_state}")
    print(f"Number of properties: {top_count:,}")
    print(f"Percentage of total: {(top_count/len(df_combined)*100):.1f}%")
    
    # Show top 5 states
    print("\nTop 5 states by property count:")
    print(state_counts.head())
else:
    print("State information not available in combined dataset")
    source_counts = df_combined['data_source'].value_counts()
    print("Properties by data source:")
    print(source_counts)
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Create comprehensive value score}
\begin{lstlisting}[caption=Multi-Factor Value Score]
from sklearn.preprocessing import MinMaxScaler

# Create value score considering price, size, and location
scaler = MinMaxScaler()

# Prepare features for scoring
df_scoring = df_analysis.copy()

# Inverse price score (lower price = higher value)
df_scoring['price_value_score'] = 1 - scaler.fit_transform(df_scoring[['price_usd']])[:, 0]

# Size score (larger size = higher value, up to a point)
df_scoring['size_value_score'] = scaler.fit_transform(df_scoring[['area_sqm']])[:, 0]

# Price per sqm score (lower = better value)
df_scoring['price_sqm_value_score'] = 1 - scaler.fit_transform(df_scoring[['price_per_sqm']])[:, 0]

# Regional premium adjustment
if 'state' in df_scoring.columns:
    state_avg_prices = df_scoring.groupby('state')['price_per_sqm'].mean()
    df_scoring['regional_score'] = df_scoring['state'].map(
        lambda x: 1 - (state_avg_prices[x] - state_avg_prices.min()) / 
        (state_avg_prices.max() - state_avg_prices.min())
    )
else:
    df_scoring['regional_score'] = 0.5  # Neutral score if no state data

# Composite value score
df_scoring['total_value_score'] = (
    df_scoring['price_value_score'] * 0.3 +
    df_scoring['size_value_score'] * 0.25 +
    df_scoring['price_sqm_value_score'] * 0.25 +
    df_scoring['regional_score'] * 0.2
)

# Find best value properties
best_values = df_scoring.nlargest(10, 'total_value_score')[
    ['price_usd', 'area_sqm', 'price_per_sqm', 'total_value_score']
]

print("Top 10 Best Value Properties:")
print(best_values)
\end{lstlisting}

\textbf{Exercise 2: Build simple property recommender}
\begin{lstlisting}[caption=Property Recommender System]
def recommend_properties(budget_max, size_min, preferred_state=None, top_n=5):
    """
    Simple property recommender based on user preferences
    """
    # Filter properties based on criteria
    recommendations = df_analysis[
        (df_analysis['price_usd'] <= budget_max) &
        (df_analysis['area_sqm'] >= size_min)
    ].copy()
    
    if preferred_state and 'state' in recommendations.columns:
        state_properties = recommendations[recommendations['state'] == preferred_state]
        if len(state_properties) > 0:
            recommendations = state_properties
    
    if len(recommendations) == 0:
        return "No properties match your criteria. Consider adjusting your requirements."
    
    # Calculate value score for filtering results
    recommendations['value_ratio'] = recommendations['area_sqm'] / recommendations['price_usd'] * 1000
    
    # Get top recommendations
    top_recommendations = recommendations.nlargest(top_n, 'value_ratio')[
        ['price_usd', 'area_sqm', 'price_per_sqm', 'value_ratio']
    ]
    
    return top_recommendations

# Example usage
print("Property Recommendations:")
print("Budget: $200,000, Minimum Size: 80 sqm")
recommendations = recommend_properties(budget_max=200000, size_min=80, top_n=5)
print(recommendations)
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Advanced outlier detection and analysis}
\begin{lstlisting}[caption=Comprehensive Outlier Analysis]
import numpy as np
from scipy import stats

def detect_outliers_multiple_methods(df, columns):
    """
    Detect outliers using multiple statistical methods
    """
    outliers_dict = {}
    
    for col in columns:
        outliers_dict[col] = {}
        
        # Method 1: IQR Method
        Q1 = df[col].quantile(0.25)
        Q3 = df[col].quantile(0.75)
        IQR = Q3 - Q1
        iqr_outliers = df[(df[col] < Q1 - 1.5*IQR) | (df[col] > Q3 + 1.5*IQR)].index
        outliers_dict[col]['IQR'] = iqr_outliers
        
        # Method 2: Z-Score Method (|z| > 3)
        z_scores = np.abs(stats.zscore(df[col].dropna()))
        z_outliers = df.iloc[np.where(z_scores > 3)[0]].index
        outliers_dict[col]['Z_Score'] = z_outliers
        
        # Method 3: Modified Z-Score using MAD
        median = df[col].median()
        mad = np.median(np.abs(df[col] - median))
        modified_z_scores = 0.6745 * (df[col] - median) / mad
        mad_outliers = df[np.abs(modified_z_scores) > 3.5].index
        outliers_dict[col]['Modified_Z'] = mad_outliers
    
    return outliers_dict

# Detect outliers in key variables
outlier_analysis = detect_outliers_multiple_methods(
    df_analysis, ['price_usd', 'area_sqm', 'price_per_sqm']
)

# Analyze outliers
print("OUTLIER ANALYSIS SUMMARY")
print("="*50)

for column, methods in outlier_analysis.items():
    print(f"\n{column.upper()} OUTLIERS:")
    for method, outlier_indices in methods.items():
        print(f"  {method}: {len(outlier_indices)} outliers")
    
    # Show example outliers
    if len(methods['IQR']) > 0:
        sample_outliers = df_analysis.loc[methods['IQR'][:3]]
        print(f"  Sample {column} outliers:")
        print(f"  {sample_outliers[['price_usd', 'area_sqm', 'price_per_sqm']].to_string()}")

# Find properties that are outliers in multiple dimensions
all_price_outliers = set(outlier_analysis['price_usd']['IQR'])
all_size_outliers = set(outlier_analysis['area_sqm']['IQR'])
all_price_sqm_outliers = set(outlier_analysis['price_per_sqm']['IQR'])

multi_outliers = all_price_outliers.intersection(all_size_outliers, all_price_sqm_outliers)
print(f"\nProperties that are outliers in ALL dimensions: {len(multi_outliers)}")

if len(multi_outliers) > 0:
    print("These unusual properties:")
    print(df_analysis.loc[multi_outliers][['price_usd', 'area_sqm', 'price_per_sqm']].head())
\end{lstlisting}

\textbf{Exercise 2: Create interactive dashboard foundation}
\begin{lstlisting}[caption=Dashboard Data Preparation]
# Prepare data for interactive dashboard
def prepare_dashboard_data(df):
    """
    Prepare summary statistics and data for dashboard
    """
    dashboard_data = {}
    
    # Market overview
    dashboard_data['market_overview'] = {
        'total_properties': len(df),
        'avg_price': df['price_usd'].mean(),
        'median_price': df['price_usd'].median(),
        'avg_size': df['area_sqm'].mean(),
        'avg_price_per_sqm': df['price_per_sqm'].mean()
    }
    
    # Regional breakdown
    if 'state' in df.columns:
        regional_data = df.groupby('state').agg({
            'price_usd': ['count', 'mean', 'median'],
            'area_sqm': 'mean',
            'price_per_sqm': 'mean'
        }).round(2)
        dashboard_data['regional_data'] = regional_data
    
    # Size categories
    size_breakdown = df.groupby('size_category').agg({
        'price_usd': ['count', 'mean'],
        'area_sqm': 'mean'
    }).round(2)
    dashboard_data['size_breakdown'] = size_breakdown
    
    # Price distributions for histograms
    dashboard_data['price_distribution'] = df['price_usd'].tolist()
    dashboard_data['size_distribution'] = df['area_sqm'].tolist()
    dashboard_data['price_sqm_distribution'] = df['price_per_sqm'].tolist()
    
    return dashboard_data

# Prepare dashboard data
dashboard_summary = prepare_dashboard_data(df_analysis)

print("DASHBOARD DATA SUMMARY")
print("="*40)
print("Market Overview:")
for key, value in dashboard_summary['market_overview'].items():
    if 'price' in key or 'sqm' in key:
        print(f"  {key}: ${value:,.0f}")
    else:
        print(f"  {key}: {value:,.0f}")

print(f"\nRegional data prepared for {len(dashboard_summary.get('regional_data', []))} regions")
print(f"Size breakdown prepared for {len(dashboard_summary['size_breakdown'])} categories")

# Basic visualization that could be enhanced in dashboard
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Price distribution
axes[0,0].hist(dashboard_summary['price_distribution'], bins=30, alpha=0.7)
axes[0,0].set_title('Price Distribution')
axes[0,0].set_xlabel('Price (USD)')

# Size distribution  
axes[0,1].hist(dashboard_summary['size_distribution'], bins=30, alpha=0.7, color='orange')
axes[0,1].set_title('Size Distribution')
axes[0,1].set_xlabel('Area (sqm)')

# Price per sqm distribution
axes[1,0].hist(dashboard_summary['price_sqm_distribution'], bins=30, alpha=0.7, color='green')
axes[1,0].set_title('Price per SqM Distribution')
axes[1,0].set_xlabel('Price per SqM (USD)')

# Size category breakdown
size_counts = df_analysis['size_category'].value_counts()
axes[1,1].pie(size_counts.values, labels=size_counts.index, autopct='%1.1f%%')
axes[1,1].set_title('Properties by Size Category')

plt.tight_layout()
plt.show()

print("\nDashboard foundation ready - this data can be used with Plotly/Streamlit for interactivity")
\end{lstlisting}

\section{Congratulations and Next Steps}

\textbf{What You've Accomplished:}
You've completed a comprehensive real estate market analysis using real-world data from Brazil. You've demonstrated the ability to:
\begin{itemize}
    \item Handle messy, multi-source data professionally
    \item Clean and integrate datasets systematically
    \item Generate meaningful business insights from data
    \item Present findings clearly and professionally
\end{itemize}

\textbf{You're Now Ready For:}
\begin{itemize}
    \item \textbf{Project 2.1:} Machine learning and predictive modeling
    \item \textbf{Advanced analytics:} Time series, clustering, classification
    \item \textbf{Specialized domains:} Finance, healthcare, marketing analytics
    \item \textbf{Industry projects:} Real consulting and analysis work
\end{itemize}

\textbf{Professional Portfolio:}
This project represents a complete data science case study that you can include in your portfolio. You've shown you can work with real data, generate insights, and communicate findings - core skills of a data scientist.

Well done! You're now ready to take on more advanced challenges in machine learning and specialized analytics. The foundation you've built here will serve you throughout your data science career.
Well done! You're now ready to take on more advanced challenges in machine learning and specialized analytics. The foundation you've built here will serve you throughout your data science career.

\end{document}
