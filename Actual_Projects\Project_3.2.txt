Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
3.2. Linear Regression with Time Series Data

import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
import pytz
from IPython.display import VimeoVideo
from pymongo import MongoClient
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error
VimeoVideo("665412117", h="c39a50bd58", width=600)
Prepare Data
Import
VimeoVideo("665412469", h="135f32c7da", width=600)
Task 3.2.1: Complete to the create a client to connect to the MongoDB server, assign the "air-quality" database to db, and assign the "nairobi" connection to nairobi.

Create a client object for a MongoDB instance.
Access a database using PyMongo.
Access a collection in a database using PyMongo.
# Step 1: Import the MongoClient class (already done in your notebook)
from pymongo import MongoClient
​
# Step 2: Connect to the MongoDB server
client = MongoClient("mongodb://localhost:27017/")
​
# Step 3: Access the "air-quality" database
db = client["air-quality"]
​
# Step 4: Access the "nairobi" collection
nairobi = db["nairobi"]
​
VimeoVideo("665412480", h="c20ed3e570", width=600)
Task 3.2.2: Complete the wrangle function below so that the results from the database query are read into the DataFrame df. Be sure that the index of df is the "timestamp" from the results.

Create a DataFrame from a dictionary using pandas.
def wrangle(collection):
    # Query documents with specific site and measurement
    results = collection.find(
        {"metadata.site": 29, "metadata.measurement": "P2"},
        projection={"P2": 1, "timestamp": 1, "_id": 0},
    )
    
    # Convert to DataFrame
    df = pd.DataFrame(results)
    
    # Convert 'timestamp' to datetime and set as index
    df["timestamp"] = pd.to_datetime(df["timestamp"])
    df = df.set_index("timestamp")
​
    # Localize to Africa/Nairobi timezone
    df = df.tz_localize("UTC").tz_convert("Africa/Nairobi")
    # Remove P2 values above 500
    df = df[df["P2"] <= 500]
    
    # Resample to hourly means
    df = df.resample('H').mean()
​
    # Forward fill missing values
    df = df.ffill()
    
    # Create lagged feature "P2.L1" with 1-hour lag
    df['P2.L1'] = df['P2'].shift(1)
    
    # Drop rows with NaN values caused by the lag
    df = df.dropna()
    return df
​
VimeoVideo("665412496", h="d757475f7c", width=600)
Task 3.2.3: Use your wrangle function to read the data from the nairobi collection into the DataFrame df.

df = wrangle(nairobi)
df.head()
​
P2	P2.L1
timestamp		
2018-09-01 04:00:00+03:00	15.800000	17.541667
2018-09-01 05:00:00+03:00	11.420000	15.800000
2018-09-01 06:00:00+03:00	11.614167	11.420000
2018-09-01 07:00:00+03:00	17.665000	11.614167
2018-09-01 08:00:00+03:00	21.016667	17.665000
# Check your work
assert any([isinstance(df, pd.DataFrame), isinstance(df, pd.Series)])
assert len(df) <= 32907
assert isinstance(df.index, pd.DatetimeIndex)
VimeoVideo("665412520", h="e03eefff07", width=600)
Task 3.2.4: Add to your wrangle function so that the DatetimeIndex for df is localized to the correct timezone, "Africa/Nairobi". Don't forget to re-run all the cells above after you change the function.

Localize a timestamp to another timezone using pandas.
# Check your work
assert df.index.tzinfo == pytz.timezone("Africa/Nairobi")
Explore
VimeoVideo("665412546", h="97792cb982", width=600)
Task 3.2.5: Create a boxplot of the "P2" readings in df.

Create a boxplot using pandas.
import matplotlib.pyplot as plt
​
fig, ax = plt.subplots(figsize=(15, 6))
df["P2"].plot(kind="box", ax=ax)
ax.set_title("Boxplot of P2 Readings")
ax.set_ylabel("P2 (µg/m³)")
plt.show()
​

VimeoVideo("665412573", h="b46049021b", width=600)
Task 3.2.6: Add to your wrangle function so that all "P2" readings above 500 are dropped from the dataset. Don't forget to re-run all the cells above after you change the function.

Subset a DataFrame with a mask using pandas.
# Check your work
assert len(df) <= 32906
VimeoVideo("665412594", h="e56c2f6839", width=600)
Task 3.2.7: Create a time series plot of the "P2" readings in df.

Create a line plot using pandas.
fig, ax = plt.subplots(figsize=(15, 6))
# Plot the P2 values
df["P2"].plot(ax=ax)
# Set title and labels
ax.set_title("P2 Readings (Jan–May 2021)")
ax.set_xlabel("Date")
ax.set_ylabel("P2 Concentration (µg/m³)")
ax.grid(True)
​
# Show the plot
plt.show()

VimeoVideo("665412601", h="a16c5a73fc", width=600)
Task 3.2.8: Add to your wrangle function to resample df to provide the mean "P2" reading for each hour. Use a forward fill to impute any missing values. Don't forget to re-run all the cells above after you change the function.

Resample time series data in pandas.
Impute missing time series values using pandas.
# Resample to hourly means
#     df = df.resample('H').mean()
​
#     # Forward fill missing values
#     df = df.ffill()
# Check your work
assert len(df) <= 2928
VimeoVideo("665412649", h="d2e99d2e75", width=600)
Task 3.2.9: Plot the rolling average of the "P2" readings in df. Use a window size of 168 (the number of hours in a week).

What's a rolling window?
Do a rolling window calculation in pandas.
Make a line plot with time series data in pandas.
fig, ax = plt.subplots(figsize=(15, 6))
​
# Calculate rolling mean with window size 168 and plot it
df["P2"].rolling(window=168).mean().plot(ax=ax)
​
# Add title and labels for clarity
ax.set_title("Rolling Average of P2 Readings (Weekly Window)")
ax.set_xlabel("Timestamp")
ax.set_ylabel("P2 (Rolling Mean)")
​
plt.show()
​

VimeoVideo("665412693", h="c3bca16aff", width=600)
Task 3.2.10: Add to your wrangle function to create a column called "P2.L1" that contains the mean"P2" reading from the previous hour. Since this new feature will create NaN values in your DataFrame, be sure to also drop null rows from df.

Shift the index of a Series in pandas.
Drop rows with missing values from a DataFrame using pandas.
# Create lagged feature "P2.L1" with 1-hour lag
#    df['P2.L1'] = df['P2'].shift(1)
    
    # Drop rows with NaN values caused by the lag
#    df = df.dropna()
# Check your work
assert len(df) <= 11686
assert df.shape[1] == 2
VimeoVideo("665412732", h="059e4088c5", width=600)
Task 3.2.11: Create a correlation matrix for df.

Create a correlation matrix in pandas.
correlation_matrix = df.corr()
print(correlation_matrix)
​
             P2     P2.L1
P2     1.000000  0.650679
P2.L1  0.650679  1.000000
VimeoVideo("665412741", h="7439cb107c", width=600)
Task 3.2.12: Create a scatter plot that shows PM 2.5 mean reading for each our as a function of the mean reading from the previous hour. In other words, "P2.L1" should be on the x-axis, and "P2" should be on the y-axis. Don't forget to label your axes!

Create a scatter plot using Matplotlib.
import matplotlib.pyplot as plt
​
fig, ax = plt.subplots(figsize=(6, 6))
ax.scatter(df["P2.L1"], df["P2"], alpha=0.5)  # Scatter plot with some transparency
ax.set_xlabel("P2.L1 (Previous Hour's Mean PM2.5 Reading)")
ax.set_ylabel("P2 (Current Hour's Mean PM2.5 Reading)")
ax.set_title("Scatter Plot of PM2.5 Readings: Current vs Previous Hour")
plt.show()
​

Split
VimeoVideo("665412762", h="a5eba496f7", width=600)
Task 3.2.13: Split the DataFrame df into the feature matrix X and the target vector y. Your target is "P2".

Subset a DataFrame by selecting one or more columns in pandas.
Select a Series from a DataFrame in pandas.
target = "P2"
y = df[target]         # Target variable (Series)
X = df[["P2.L1"]]      # Features (DataFrame with one column)
​
VimeoVideo("665412785", h="03118eda71", width=600)
Task 3.2.14: Split X and y into training and test sets. The first 80% of the data should be in your training set. The remaining 20% should be in the test set.

Divide data into training and test sets in pandas.
cutoff = int(len(X) * 0.8)
​
X_train, y_train = X.iloc[:cutoff], y.iloc[:cutoff]
X_test, y_test = X.iloc[cutoff:], y.iloc[cutoff:]
​
Build Model
Baseline
Task 3.2.15: Calculate the baseline mean absolute error for your model.

Calculate summary statistics for a DataFrame or Series in pandas.
y_pred_baseline = [y_train.mean()] * len(y_train)
mae_baseline = mean_absolute_error(y_train, y_pred_baseline)
​
print("Mean P2 Reading:", round(y_train.mean(), 2))
print("Baseline MAE:", round(mae_baseline, 2))
​
Mean P2 Reading: 9.27
Baseline MAE: 3.89
Iterate
Task 3.2.16: Instantiate a LinearRegression model named model, and fit it to your training data.

Instantiate a predictor in scikit-learn.
Fit a model to training data in scikit-learn.
model = LinearRegression()
model.fit(X_train, y_train)
​
LinearRegression()
In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook.
On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.
Evaluate
VimeoVideo("665412844", h="129865775d", width=600)
Task 3.2.17: Calculate the training and test mean absolute error for your model.

Generate predictions using a trained model in scikit-learn.
Calculate the mean absolute error for a list of predictions in scikit-learn.
training_mae = mean_absolute_error(y_train, model.predict(X_train))
test_mae = mean_absolute_error(y_test, model.predict(X_test))
​
print("Training MAE:", round(training_mae, 2))
print("Test MAE:", round(test_mae, 2))
​
Training MAE: 2.46
Test MAE: 1.8
Communicate Results
Task 3.2.18: Extract the intercept and coefficient from your model.

Access an object in a pipeline in scikit-learnWQU WorldQuant University Applied Data Science Lab QQQQ
intercept = model.intercept_
coefficient = model.coef_[0]
​
print(f"P2 = {intercept} + ({coefficient} * P2.L1)")
​
P2 = 3.3555730583913483 + (0.6378942188744786 * P2.L1)
VimeoVideo("665412870", h="318d69683e", width=600)
Task 3.2.19: Create a DataFrame df_pred_test that has two columns: "y_test" and "y_pred". The first should contain the true values for your test set, and the second should contain your model's predictions. Be sure the index of df_pred_test matches the index of y_test.

Create a DataFrame from a dictionary using pandas.
df_pred_test = pd.DataFrame({
    "y_test": y_test,
    "y_pred": model.predict(X_test)
}, index=y_test.index)
​
df_pred_test.head()
​
y_test	y_pred
timestamp		
2018-12-07 17:00:00+03:00	7.070000	8.478927
2018-12-07 18:00:00+03:00	8.968333	7.865485
2018-12-07 19:00:00+03:00	11.630833	9.076421
2018-12-07 20:00:00+03:00	11.525833	10.774814
2018-12-07 21:00:00+03:00	9.533333	10.707836
VimeoVideo("665412891", h="39d7356a26", width=600)
Task 3.2.20: Create a time series line plot for the values in test_predictions using plotly express. Be sure that the y-axis is properly labeled as "P2".

Create a line plot using plotly express.
import plotly.express as px
​
fig = px.line(df_pred_test, y=["y_test", "y_pred"], labels={"value": "P2", "index": "Timestamp"})
fig.show()
​

Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
7
Python 3 (ipykernel) | Idle
0
032-linear-regression-with-time-series-data.ipynb
Ln 1, Col 1
Mode: Command