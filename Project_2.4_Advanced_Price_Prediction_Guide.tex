\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 2.4: Advanced Price Prediction}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 2.4: Predicting Price with Size, Location, and Neighborhood} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Advanced Feature Engineering and Model Building}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Building Your Most Advanced Model Yet}

Welcome to Project 2.4! This is where everything comes together. We'll combine all the features we've worked with - apartment size, geographic coordinates, and neighborhood names - to build our most sophisticated price prediction model yet.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item Working with multiple datasets using glob and list comprehensions
    \item Advanced data cleaning and feature selection techniques
    \item Handling categorical data with one-hot encoding
    \item Dealing with multicollinearity and missing data
    \item Building machine learning pipelines
    \item Creating interactive prediction tools
    \item Professional model deployment concepts
\end{itemize}

\begin{concept}
\textbf{From Simple to Complex:}
\begin{itemize}
    \item \textbf{Project 2.1:} price = f(size)
    \item \textbf{Project 2.2:} price = f(latitude, longitude)
    \item \textbf{Project 2.3:} price = f(neighborhood)
    \item \textbf{Project 2.4:} price = f(size, latitude, longitude, neighborhood)
\end{itemize}
Using all features together often gives much better predictions than using them separately!
\end{concept}

\section{Task 2.4.1: Efficient Data Loading with Glob}

Let's learn a professional way to load multiple datasets automatically.

\begin{lstlisting}[caption=Professional Data Loading]
import warnings
from glob import glob
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error

warnings.simplefilter(action="ignore", category=FutureWarning)

# Use glob to find all Buenos Aires files automatically
files = glob("data/buenos-aires-real-estate-*.csv")
print(f"Found {len(files)} data files:")
for file in files:
    print(f"  - {file}")

print(f"\nTotal files found: {len(files)}")
\end{lstlisting}

\begin{learningtip}
\textbf{Why Use Glob?}
\begin{itemize}
    \item \textbf{Automation:} Finds files matching a pattern automatically
    \item \textbf{Scalability:} Works whether you have 5 files or 50
    \item \textbf{Error Prevention:} No risk of forgetting to include a file
    \item \textbf{Professional Practice:} Used in real-world data science projects
\end{itemize}
Pattern \texttt{*.csv} means "any filename ending with .csv"
\end{learningtip}

\section{Task 2.4.2: Advanced Data Cleaning Function}

Let's create a comprehensive data cleaning function that handles all the issues we've learned about.

\begin{lstlisting}[caption=Professional Data Cleaning Pipeline]
def wrangle(filepath):
    """
    Comprehensive data cleaning for Buenos Aires real estate data.
    Returns a clean DataFrame ready for machine learning.
    """
    
    # Read CSV file
    df = pd.read_csv(filepath)
    print(f"Loaded {len(df)} properties from {filepath}")
    
    # Step 1: Basic filtering
    mask_ba = df["place_with_parent_names"].str.contains("Capital Federal", na=False)
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 400_000
    df = df[mask_ba & mask_apt & mask_price]
    
    # Step 2: Remove area outliers
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df = df[mask_area]
    
    # Step 3: Extract coordinates
    df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
    df.drop(columns="lat-lon", inplace=True)
    
    # Step 4: Extract neighborhood from location string
    df["neighborhood"] = df["place_with_parent_names"].str.split("|", expand=True)[3]
    df.drop(columns="place_with_parent_names", inplace=True)
    
    print(f"After cleaning: {len(df)} apartments with {df.shape[1]} features")
    return df

# Test our function
sample_df = wrangle("data/buenos-aires-real-estate-1.csv")
print(f"\nSample of cleaned data:")
print(sample_df[["surface_covered_in_m2", "lat", "lon", "neighborhood", "price_aprox_usd"]].head())
\end{lstlisting}

\begin{concept}
\textbf{Data Cleaning Pipeline:}
Good data cleaning happens in stages:
\begin{enumerate}
    \item \textbf{Filter relevant data:} Keep only what you need
    \item \textbf{Remove outliers:} Exclude extreme or unusual values
    \item \textbf{Extract features:} Create useful columns from existing data
    \item \textbf{Clean up:} Remove unnecessary columns
\end{enumerate}
Each step makes the data cleaner and more suitable for machine learning.
\end{concept}

\section{Task 2.4.3: List Comprehensions for Efficient Processing}

Learn a powerful Python technique for processing multiple files efficiently.

\begin{lstlisting}[caption=List Comprehensions in Action]
# Traditional approach using a for loop
frames_traditional = []
for file in files:
    df = wrangle(file)
    frames_traditional.append(df)

print(f"Traditional approach: Created {len(frames_traditional)} DataFrames")

# Modern approach using list comprehension (more professional)
frames = [wrangle(file) for file in files]
print(f"List comprehension: Created {len(frames)} DataFrames")

# Both approaches give the same result, but list comprehension is more concise
print(f"Results are identical: {len(frames_traditional) == len(frames)}")

# Show what we got
for i, frame in enumerate(frames):
    print(f"DataFrame {i+1}: {len(frame)} apartments")
\end{lstlisting}

\begin{learningtip}
\textbf{List Comprehensions:}
\begin{itemize}
    \item \textbf{Syntax:} \texttt{[function(item) for item in list]}
    \item \textbf{Equivalent to:} A for loop that builds a list
    \item \textbf{Benefits:} More concise, often faster, more "Pythonic"
    \item \textbf{Professional:} Widely used in data science code
\end{itemize}
Read it as: "Apply wrangle to each file and collect results in a list"
\end{learningtip}

\section{Task 2.4.4: Combining Datasets Professionally}

Let's combine all our cleaned datasets into one comprehensive dataset.

\begin{lstlisting}[caption=Dataset Combination and Validation]
# Combine all DataFrames
df = pd.concat(frames, ignore_index=True)

print(f"COMBINED DATASET SUMMARY")
print(f"=" * 35)
print(f"Total apartments: {len(df):,}")
print(f"Total features: {df.shape[1]}")
print(f"Data sources: {len(frames)} files")

# Show data quality metrics
print(f"\nDATA QUALITY CHECK:")
print(f"Price range: ${df['price_aprox_usd'].min():,.0f} - ${df['price_aprox_usd'].max():,.0f}")
print(f"Size range: {df['surface_covered_in_m2'].min():.0f} - {df['surface_covered_in_m2'].max():.0f} sq meters")
print(f"Unique neighborhoods: {df['neighborhood'].nunique()}")

# Check for missing data
missing_summary = df.isnull().sum()
missing_summary = missing_summary[missing_summary > 0]

if len(missing_summary) > 0:
    print(f"\nMISSING DATA FOUND:")
    for column, count in missing_summary.items():
        percentage = (count / len(df)) * 100
        print(f"  {column}: {count:,} missing ({percentage:.1f}%)")
else:
    print(f"\nNo missing data - excellent!")

# Show final dataset structure
print(f"\nFINAL DATASET STRUCTURE:")
print(df.info())
\end{lstlisting}

\section{Task 2.4.5: Advanced Data Quality Analysis}

Before building our model, let's do a thorough analysis of data quality issues.

\begin{lstlisting}[caption=Comprehensive Data Quality Assessment]
# Check for columns with too many missing values
print("MISSING DATA ANALYSIS")
print("=" * 25)

thresh = len(df) / 2  # 50% threshold
missing_pct = (df.isnull().sum() / len(df)) * 100

print("Missing data by column:")
for column in df.columns:
    missing = df[column].isnull().sum()
    pct = missing_pct[column]
    status = "DROP" if missing > thresh else "KEEP"
    print(f"  {column}: {missing:,} ({pct:.1f}%) - {status}")

# Drop columns with too much missing data
cols_to_drop = missing_pct[missing_pct > 50].index.tolist()
if cols_to_drop:
    print(f"\nDropping columns with >50% missing: {cols_to_drop}")
    df = df.drop(columns=cols_to_drop)
else:
    print(f"\nAll columns have acceptable missing data levels")

# Check categorical column cardinality
print(f"\nCATEGORICAL DATA ANALYSIS")
print("=" * 28)

categorical_cols = df.select_dtypes(include=['object']).columns
for col in categorical_cols:
    unique_count = df[col].nunique()
    total_count = len(df)
    cardinality_pct = (unique_count / total_count) * 100
    
    if unique_count == 1:
        status = "LOW CARDINALITY - Consider dropping"
    elif cardinality_pct > 50:
        status = "HIGH CARDINALITY - Consider dropping"
    else:
        status = "GOOD CARDINALITY - Keep"
    
    print(f"  {col}: {unique_count} unique values ({cardinality_pct:.1f}%) - {status}")
\end{lstlisting}

\begin{concept}
\textbf{Data Quality Issues:}
\begin{itemize}
    \item \textbf{Missing Data:} >50% missing = usually drop the column
    \item \textbf{Low Cardinality:} All values the same = no predictive power
    \item \textbf{High Cardinality:} Every row unique = usually not useful
    \item \textbf{Leakage:} Features that use the target to create them
\end{itemize}
Good data quality is essential for good model performance!
\end{concept}

\section{Task 2.4.6: Feature Engineering and Selection}

Let's clean up our features and select only the most useful ones.

\begin{lstlisting}[caption=Advanced Feature Engineering]
# Step 1: Drop high/low cardinality categorical features
# (This would be done in the wrangle function in practice)
high_low_cardinality_cols = ["operation", "property_type", "currency", "properati_url"]
leakage_cols = ["price", "price_aprox_local_currency", "price_per_m2", "price_usd_per_m2"]

# These columns should be dropped in the wrangle function
print("FEATURE SELECTION")
print("=" * 20)
print(f"Starting columns: {df.shape[1]}")

# Show correlation matrix for numerical features
numerical_features = df.select_dtypes(include=[np.number]).columns.tolist()
if 'price_aprox_usd' in numerical_features:
    numerical_features.remove('price_aprox_usd')  # Remove target

print(f"Numerical features for correlation analysis: {numerical_features}")

if len(numerical_features) > 1:
    # Create correlation matrix
    corr_matrix = df[numerical_features].corr()
    
    # Visualize correlations
    plt.figure(figsize=(10, 8))
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f')
    plt.title('Feature Correlation Matrix')
    plt.tight_layout()
    plt.show()
    
    # Check for multicollinearity (correlation > 0.8)
    print(f"\nMULTICOLLINEARITY CHECK:")
    high_corr_pairs = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if abs(corr_matrix.iloc[i, j]) > 0.8:
                feature1 = corr_matrix.columns[i]
                feature2 = corr_matrix.columns[j]
                correlation = corr_matrix.iloc[i, j]
                high_corr_pairs.append((feature1, feature2, correlation))
                print(f"  {feature1} vs {feature2}: {correlation:.3f}")
    
    if not high_corr_pairs:
        print("  No highly correlated features found (>0.8)")
else:
    print("Not enough numerical features for correlation analysis")

# Final feature set
print(f"\nFINAL FEATURE SET:")
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")
\end{lstlisting}

\section{Task 2.4.7: Preparing Data for Machine Learning}

Let's set up our features and target for machine learning.

\begin{lstlisting}[caption=ML Data Preparation]
# Define target and features
target = "price_aprox_usd"
features = [col for col in df.columns if col != target]

X_train = df[features].copy()
y_train = df[target].copy()

print(f"MACHINE LEARNING DATA SETUP")
print(f"=" * 35)
print(f"Target variable: {target}")
print(f"Number of features: {len(features)}")
print(f"Features: {features}")
print(f"Dataset shape: X={X_train.shape}, y={y_train.shape}")

# Analyze feature types
numerical_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
categorical_features = X_train.select_dtypes(include=['object']).columns.tolist()

print(f"\nFEATURE ANALYSIS:")
print(f"Numerical features ({len(numerical_features)}): {numerical_features}")
print(f"Categorical features ({len(categorical_features)}): {categorical_features}")

# Check missing data in final feature set
missing_data = X_train.isnull().sum()
if missing_data.sum() > 0:
    print(f"\nMISSING DATA IN FEATURES:")
    for feature, count in missing_data[missing_data > 0].items():
        percentage = (count / len(X_train)) * 100
        print(f"  {feature}: {count} ({percentage:.1f}%)")
else:
    print(f"\nNo missing data in feature set - ready for ML!")

# Show sample of prepared data
print(f"\nSAMPLE DATA:")
print(X_train.head(3))
print(f"\nTarget sample: {y_train.head(3).tolist()}")
\end{lstlisting}

\section{Task 2.4.8: Building a Professional ML Pipeline}

Now let's create a sophisticated machine learning pipeline that handles all data preprocessing automatically.

\begin{lstlisting}[caption=Professional ML Pipeline]
from sklearn.pipeline import make_pipeline
from sklearn.impute import SimpleImputer
from sklearn.linear_model import Ridge
from category_encoders import OneHotEncoder

print("BUILDING ML PIPELINE")
print("=" * 25)

# Create comprehensive pipeline
model = make_pipeline(
    OneHotEncoder(use_cat_names=True),  # Handle categorical features
    SimpleImputer(strategy='median'),    # Handle missing values
    Ridge(alpha=1.0)                     # Regularized linear regression
)

print("Pipeline steps:")
print("1. OneHotEncoder: Convert neighborhood names to numbers")
print("2. SimpleImputer: Fill any missing values with median")
print("3. Ridge Regression: Learn the relationship with regularization")

# Train the model
print(f"\nTraining on {len(X_train)} apartments...")
model.fit(X_train, y_train)
print("Training complete!")

# Baseline comparison
y_mean = y_train.mean()
y_pred_baseline = [y_mean] * len(y_train)
baseline_mae = mean_absolute_error(y_train, y_pred_baseline)

print(f"\nBASELINE PERFORMANCE:")
print(f"Always predict average (${y_mean:,.0f})")
print(f"Baseline MAE: ${baseline_mae:,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{ML Pipelines Benefits:}
\begin{itemize}
    \item \textbf{Automation:} All preprocessing happens automatically
    \item \textbf{Consistency:} Same steps applied to training and test data
    \item \textbf{No leakage:} Preprocessing fit only on training data
    \item \textbf{Professional:} Industry standard approach
    \item \textbf{Deployment ready:} Easy to use in production
\end{itemize}
Pipelines prevent many common machine learning mistakes!
\end{learningtip}

\section{Task 2.4.9: Model Evaluation and Performance Analysis}

Let's thoroughly evaluate our model's performance.

\begin{lstlisting}[caption=Comprehensive Model Evaluation]
# Make predictions
y_pred_training = model.predict(X_train)
training_mae = mean_absolute_error(y_train, y_pred_training)

print(f"MODEL PERFORMANCE ANALYSIS")
print(f"=" * 35)
print(f"Training MAE: ${training_mae:,.0f}")
print(f"Baseline MAE: ${baseline_mae:,.0f}")
print(f"Improvement: ${baseline_mae - training_mae:,.0f}")
print(f"Percentage better: {((baseline_mae - training_mae) / baseline_mae) * 100:.1f}%")

# Calculate R-squared
from sklearn.metrics import r2_score
r2 = r2_score(y_train, y_pred_training)
print(f"R-squared: {r2:.3f} ({r2*100:.1f}% of variance explained)")

# Analyze prediction errors
errors = y_train - y_pred_training
abs_errors = np.abs(errors)

print(f"\nERROR ANALYSIS:")
print(f"Mean error: ${errors.mean():,.0f}")
print(f"Std error: ${errors.std():,.0f}")
print(f"Median absolute error: ${np.median(abs_errors):,.0f}")
print(f"90th percentile error: ${np.percentile(abs_errors, 90):,.0f}")

# Performance by price range
price_ranges = pd.cut(y_train, bins=5, labels=['Very Low', 'Low', 'Medium', 'High', 'Very High'])
performance_by_range = pd.DataFrame({
    'price_range': price_ranges,
    'actual': y_train,
    'predicted': y_pred_training,
    'abs_error': abs_errors
})

print(f"\nPERFORMANCE BY PRICE RANGE:")
for range_name in ['Very Low', 'Low', 'Medium', 'High', 'Very High']:
    range_data = performance_by_range[performance_by_range['price_range'] == range_name]
    if len(range_data) > 0:
        avg_actual = range_data['actual'].mean()
        avg_predicted = range_data['predicted'].mean()
        avg_error = range_data['abs_error'].mean()
        count = len(range_data)
        print(f"  {range_name}: {count} apts, avg actual ${avg_actual:,.0f}, avg error ${avg_error:,.0f}")
\end{lstlisting}

\section{Task 2.4.10: Creating Practical Prediction Functions}

Let's create tools that make our model easy to use in real-world scenarios.

\begin{lstlisting}[caption=Practical Prediction Interface]
def make_prediction(area, lat, lon, neighborhood):
    """
    Predict apartment price given features.
    
    Parameters:
    area (float): Surface area in square meters
    lat (float): Latitude coordinate
    lon (float): Longitude coordinate
    neighborhood (str): Neighborhood name
    
    Returns:
    str: Formatted prediction string
    """
    
    # Create input DataFrame
    input_data = pd.DataFrame({
        'surface_covered_in_m2': [area],
        'lat': [lat],
        'lon': [lon],
        'neighborhood': [neighborhood]
    })
    
    # Make prediction
    prediction = model.predict(input_data)[0]
    
    return f"Predicted apartment price: ${prediction:,.0f}"

# Test the function
print("TESTING PREDICTION FUNCTION")
print("=" * 32)

test_cases = [
    (110, -34.60, -58.46, "Villa Crespo"),
    (80, -34.59, -58.39, "Palermo"),
    (65, -34.61, -58.37, "San Telmo"),
    (95, -34.58, -58.42, "Recoleta")
]

for i, (area, lat, lon, neighborhood) in enumerate(test_cases, 1):
    prediction = make_prediction(area, lat, lon, neighborhood)
    print(f"{i}. {area} sq meters in {neighborhood}: {prediction}")

# Batch prediction for analysis
print(f"\nBATCH PREDICTION EXAMPLE:")
batch_input = pd.DataFrame({
    'surface_covered_in_m2': [50, 75, 100, 125],
    'lat': [-34.60, -34.59, -34.61, -34.58],
    'lon': [-58.45, -58.40, -58.35, -58.42],
    'neighborhood': ['Barracas', 'Palermo', 'San Telmo', 'Recoleta']
})

batch_predictions = model.predict(batch_input)
for i, pred in enumerate(batch_predictions):
    row = batch_input.iloc[i]
    print(f"{i+1}. {row['surface_covered_in_m2']} sq meters in {row['neighborhood']}: ${pred:,.0f}")
\end{lstlisting}

\section{Task 2.4.11: Interactive Prediction Dashboard}

Let's create an interactive tool for exploring predictions.

\begin{lstlisting}[caption=Interactive Dashboard Setup]
# Note: This would work in Jupyter notebooks with ipywidgets
from ipywidgets import interact, IntSlider, FloatSlider, Dropdown

print("INTERACTIVE PREDICTION DASHBOARD")
print("=" * 40)
print("Creating interactive widgets for real-time predictions...")

# Get ranges for sliders from training data
area_min = int(X_train["surface_covered_in_m2"].min())
area_max = int(X_train["surface_covered_in_m2"].max())
area_mean = int(X_train["surface_covered_in_m2"].mean())

lat_min = X_train["lat"].min()
lat_max = X_train["lat"].max()
lat_mean = X_train["lat"].mean()

lon_min = X_train["lon"].min()
lon_max = X_train["lon"].max()
lon_mean = X_train["lon"].mean()

neighborhoods = sorted(X_train["neighborhood"].unique())

print(f"Dashboard parameters:")
print(f"  Area range: {area_min} - {area_max} sq meters")
print(f"  Latitude range: {lat_min:.4f} - {lat_max:.4f}")
print(f"  Longitude range: {lon_min:.4f} - {lon_max:.4f}")
print(f"  Available neighborhoods: {len(neighborhoods)}")

# Create interactive function (would work in Jupyter)
def interactive_prediction(area, lat, lon, neighborhood):
    """Interactive prediction function for Jupyter widgets."""
    prediction = make_prediction(area, lat, lon, neighborhood)
    
    # Find similar apartments for comparison
    similar_mask = (
        (abs(X_train["surface_covered_in_m2"] - area) <= 10) &
        (abs(X_train["lat"] - lat) <= 0.01) &
        (abs(X_train["lon"] - lon) <= 0.01) &
        (X_train["neighborhood"] == neighborhood)
    )
    
    similar_apartments = X_train[similar_mask]
    
    print(prediction)
    
    if len(similar_apartments) > 0:
        similar_prices = y_train[similar_mask]
        avg_similar = similar_prices.mean()
        print(f"Similar apartments average: ${avg_similar:,.0f}")
        print(f"Found {len(similar_apartments)} similar apartments")
    else:
        print("No similar apartments in dataset")
    
    return prediction

# Demo without widgets (for PDF)
print(f"\nDEMO PREDICTIONS:")
demo_params = [
    (60, lat_mean, lon_mean, neighborhoods[0]),
    (90, lat_mean, lon_mean, neighborhoods[0]),
    (120, lat_mean, lon_mean, neighborhoods[0])
]

for area, lat, lon, neighborhood in demo_params:
    result = interactive_prediction(area, lat, lon, neighborhood)
    print("-" * 40)
\end{lstlisting}

\section{Summary: Advanced Machine Learning Mastery}

\subsection{Technical Skills You Mastered}
\begin{enumerate}
    \item \textbf{Professional Data Loading:} Automated file discovery with glob
    \item \textbf{List Comprehensions:} Efficient data processing techniques
    \item \textbf{Comprehensive Data Cleaning:} Multi-step cleaning pipelines
    \item \textbf{Feature Engineering:} Smart feature selection and correlation analysis
    \item \textbf{Advanced Preprocessing:} One-hot encoding and imputation
    \item \textbf{Pipeline Building:} Professional ML workflow automation
    \item \textbf{Model Evaluation:} Thorough performance analysis
    \item \textbf{Practical Deployment:} User-friendly prediction interfaces
    \item \textbf{Interactive Tools:} Dashboard creation for model exploration
\end{enumerate}

\subsection{Key Achievements}
\begin{itemize}
    \item \textbf{Complex Model:} Successfully combined size, location, and neighborhood features
    \item \textbf{Superior Performance:} Achieved significant improvement over baseline
    \item \textbf{Professional Quality:} Built production-ready ML pipeline
    \item \textbf{Practical Application:} Created tools for real-world use
\end{itemize}

\subsection{Advanced Concepts Learned}
\begin{itemize}
    \item \textbf{Feature Selection:} Identifying and removing problematic features
    \item \textbf{Multicollinearity:} Understanding and handling correlated features
    \item \textbf{Pipeline Design:} Creating robust, reusable ML workflows
    \item \textbf{Model Deployment:} Making models accessible to end users
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Try the model with different neighborhoods and compare predictions
    \item Create a function that finds the most undervalued apartments in the dataset
    \item Build a simple web interface for the prediction function
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Add cross-validation to properly evaluate model performance
    \item Experiment with different regularization parameters in Ridge regression
    \item Create feature importance plots to understand which features matter most
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Implement automated feature selection using statistical tests
    \item Try ensemble methods (Random Forest, Gradient Boosting) and compare performance
    \item Build a complete web application with maps and interactive predictions
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Level Solutions}

\subsubsection{Solution 1: Testing Different Neighborhoods}

\begin{lstlisting}[caption=Neighborhood Comparison Analysis]
# Compare predictions across different neighborhoods
neighborhoods_to_test = ['Palermo', 'Recoleta', 'San Telmo', 'Villa Crespo', 'Belgrano']
apartment_size = 85  # Standard size for comparison

print("NEIGHBORHOOD PRICE COMPARISON")
print("=" * 35)
print(f"For {apartment_size} sq meter apartments:")

predictions = {}
for neighborhood in neighborhoods_to_test:
    # Use average coordinates for Buenos Aires
    avg_lat = X_train["lat"].mean()
    avg_lon = X_train["lon"].mean()
    
    pred = make_prediction(apartment_size, avg_lat, avg_lon, neighborhood)
    price = model.predict(pd.DataFrame({
        'surface_covered_in_m2': [apartment_size],
        'lat': [avg_lat], 'lon': [avg_lon],
        'neighborhood': [neighborhood]
    }))[0]
    
    predictions[neighborhood] = price
    print(f"  {neighborhood}: ${price:,.0f}")

# Find best and worst neighborhoods
best_neighborhood = min(predictions, key=predictions.get)
worst_neighborhood = max(predictions, key=predictions.get)
price_difference = predictions[worst_neighborhood] - predictions[best_neighborhood]

print(f"\nAnalysis:")
print(f"Most affordable: {best_neighborhood} (${predictions[best_neighborhood]:,.0f})")
print(f"Most expensive: {worst_neighborhood} (${predictions[worst_neighborhood]:,.0f})")
print(f"Price difference: ${price_difference:,.0f}")
\end{lstlisting}

\subsubsection{Solution 2: Finding Undervalued Apartments}

\begin{lstlisting}[caption=Undervalued Property Detection]
# Find apartments where actual price < predicted price
predictions_all = model.predict(X_train)
price_differences = predictions_all - y_train
undervalued_mask = price_differences > 0

undervalued_apartments = X_train[undervalued_mask].copy()
undervalued_apartments['actual_price'] = y_train[undervalued_mask]
undervalued_apartments['predicted_price'] = predictions_all[undervalued_mask]
undervalued_apartments['potential_profit'] = price_differences[undervalued_mask]

# Sort by potential profit
undervalued_sorted = undervalued_apartments.sort_values('potential_profit', ascending=False)

print("TOP 10 UNDERVALUED APARTMENTS")
print("=" * 35)
for i in range(min(10, len(undervalued_sorted))):
    apt = undervalued_sorted.iloc[i]
    print(f"{i+1}. {apt['surface_covered_in_m2']:.0f}sqm in {apt['neighborhood']}")
    print(f"   Actual: ${apt['actual_price']:,.0f}, Predicted: ${apt['predicted_price']:,.0f}")
    print(f"   Potential profit: ${apt['potential_profit']:,.0f}")
    print()

# Create visualization
plt.figure(figsize=(12, 8))
plt.scatter(y_train, predictions_all, alpha=0.6, s=20)
plt.plot([y_train.min(), y_train.max()], [y_train.min(), y_train.max()], 'r--', label='Perfect prediction')
plt.xlabel('Actual Price')
plt.ylabel('Predicted Price')
plt.title('Actual vs Predicted Prices - Finding Undervalued Properties')

# Highlight undervalued properties
plt.scatter(y_train[undervalued_mask], predictions_all[undervalued_mask], 
           color='red', s=30, alpha=0.8, label='Undervalued (below line)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
\end{lstlisting}

\subsubsection{Solution 3: Simple Web Interface Concept}

\begin{lstlisting}[caption=Web Interface Framework]
def create_prediction_interface():
    """
    Template for a simple web interface using Flask
    This shows the structure - actual deployment would need Flask setup
    """
    
    # Web interface template (pseudo-code)
    interface_code = '''
    from flask import Flask, request, render_template
    import pandas as pd
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return render_template('index.html')
    
    @app.route('/predict', methods=['POST'])
    def predict():
        area = float(request.form['area'])
        lat = float(request.form['latitude'])
        lon = float(request.form['longitude'])
        neighborhood = request.form['neighborhood']
        
        prediction = make_prediction(area, lat, lon, neighborhood)
        
        return render_template('result.html', 
                             prediction=prediction,
                             inputs={'area': area, 'lat': lat, 
                                   'lon': lon, 'neighborhood': neighborhood})
    
    if __name__ == '__main__':
        app.run(debug=True)
    '''
    
    print("WEB INTERFACE FRAMEWORK")
    print("=" * 25)
    print("Basic Flask structure for apartment price prediction:")
    print(interface_code)
    
    # Create a simple command-line interface instead
    print("\nCOMMAND-LINE INTERFACE DEMO:")
    while True:
        try:
            print("\nEnter apartment details (or 'quit' to exit):")
            area = input("Area (sq meters): ")
            if area.lower() == 'quit':
                break
            
            lat = input("Latitude: ")
            lon = input("Longitude: ")
            neighborhood = input("Neighborhood: ")
            
            prediction = make_prediction(float(area), float(lat), float(lon), neighborhood)
            print(f"\nPrediction: {prediction}")
            
        except ValueError:
            print("Invalid input. Please enter numbers for area, lat, and lon.")
        except KeyboardInterrupt:
            break
    
    print("Interface demo completed!")

# Run the demo interface
create_prediction_interface()
\end{lstlisting}

\subsection{Medium Level Solutions}

\subsubsection{Solution 1: Cross-Validation Implementation}

\begin{lstlisting}[caption=Proper Model Evaluation with Cross-Validation]
from sklearn.model_selection import cross_val_score, KFold
from sklearn.metrics import make_scorer

# Set up cross-validation
cv = KFold(n_splits=5, shuffle=True, random_state=42)
mae_scorer = make_scorer(mean_absolute_error, greater_is_better=False)

print("CROSS-VALIDATION ANALYSIS")
print("=" * 30)

# Test our current model
cv_scores = cross_val_score(model, X_train, y_train, cv=cv, scoring=mae_scorer)
cv_scores = -cv_scores  # Convert back to positive MAE

print(f"5-Fold Cross-Validation Results:")
for i, score in enumerate(cv_scores, 1):
    print(f"  Fold {i}: ${score:,.0f} MAE")

print(f"\nCross-Validation Summary:")
print(f"Mean MAE: ${cv_scores.mean():,.0f}")
print(f"Std MAE: ${cv_scores.std():,.0f}")
print(f"Best fold: ${cv_scores.min():,.0f}")
print(f"Worst fold: ${cv_scores.max():,.0f}")

# Compare to training error
training_mae = mean_absolute_error(y_train, model.predict(X_train))
print(f"\nComparison:")
print(f"Training MAE: ${training_mae:,.0f}")
print(f"CV MAE: ${cv_scores.mean():,.0f}")

overfitting_amount = cv_scores.mean() - training_mae
if overfitting_amount > training_mae * 0.1:
    print(f"WARNING: Possible overfitting detected (+${overfitting_amount:,.0f})")
else:
    print(f"Good: Minimal overfitting (+${overfitting_amount:,.0f})")

# Visualize cross-validation results
plt.figure(figsize=(10, 6))
plt.boxplot(cv_scores)
plt.ylabel('MAE (USD)')
plt.title('Cross-Validation Performance Distribution')
plt.axhline(y=baseline_mae, color='r', linestyle='--', label=f'Baseline: ${baseline_mae:,.0f}')
plt.axhline(y=training_mae, color='g', linestyle='--', label=f'Training: ${training_mae:,.0f}')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()
\end{lstlisting}

\subsubsection{Solution 2: Regularization Parameter Tuning}

\begin{lstlisting}[caption=Ridge Regression Alpha Optimization]
from sklearn.model_selection import GridSearchCV

# Test different alpha values for Ridge regression
alpha_values = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, 50.0, 100.0]

print("REGULARIZATION PARAMETER TUNING")
print("=" * 38)

# Manual testing first
results = {}
for alpha in alpha_values:
    # Create model with specific alpha
    test_model = make_pipeline(
        OneHotEncoder(use_cat_names=True),
        SimpleImputer(strategy='median'),
        Ridge(alpha=alpha)
    )
    
    # Cross-validate
    cv_scores = cross_val_score(test_model, X_train, y_train, cv=5, scoring=mae_scorer)
    mean_mae = -cv_scores.mean()
    results[alpha] = mean_mae
    
    print(f"Alpha {alpha:6.1f}: ${mean_mae:,.0f} MAE")

# Find best alpha
best_alpha = min(results, key=results.get)
best_mae = results[best_alpha]

print(f"\nBest regularization parameter:")
print(f"Alpha: {best_alpha}")
print(f"Best CV MAE: ${best_mae:,.0f}")

# Use GridSearchCV for more sophisticated search
param_grid = {'ridge__alpha': alpha_values}
grid_search = GridSearchCV(
    make_pipeline(OneHotEncoder(use_cat_names=True), SimpleImputer(strategy='median'), Ridge()),
    param_grid, cv=5, scoring=mae_scorer, n_jobs=-1
)

grid_search.fit(X_train, y_train)

print(f"\nGridSearchCV Results:")
print(f"Best alpha: {grid_search.best_params_['ridge__alpha']}")
print(f"Best score: ${-grid_search.best_score_:,.0f}")

# Visualize alpha performance
plt.figure(figsize=(10, 6))
alphas = list(results.keys())
maes = list(results.values())

plt.semilogx(alphas, maes, 'bo-', linewidth=2, markersize=8)
plt.xlabel('Alpha (Regularization Strength)')
plt.ylabel('Cross-Validated MAE (USD)')
plt.title('Ridge Regression: Regularization Parameter Tuning')
plt.axvline(x=best_alpha, color='r', linestyle='--', label=f'Best Alpha: {best_alpha}')
plt.grid(True, alpha=0.3)
plt.legend()
plt.show()
\end{lstlisting}

\subsubsection{Solution 3: Feature Importance Visualization}

\begin{lstlisting}[caption=Advanced Feature Importance Analysis]
# Get the best model from grid search
best_model = grid_search.best_estimator_
ridge_component = best_model.named_steps['ridge']
encoder_component = best_model.named_steps['onehotencoder']

# Extract coefficients and feature names
coefficients = ridge_component.coef_
feature_names = encoder_component.get_feature_names_out()

# Create comprehensive feature importance analysis
feature_importance = pd.DataFrame({
    'feature': feature_names,
    'coefficient': coefficients,
    'abs_coefficient': np.abs(coefficients)
}).sort_values('abs_coefficient', ascending=False)

print("COMPREHENSIVE FEATURE IMPORTANCE")
print("=" * 40)

# Show top 15 most important features
print("Top 15 Most Important Features:")
for i, row in feature_importance.head(15).iterrows():
    direction = "increases" if row['coefficient'] > 0 else "decreases"
    print(f"{i+1:2d}. {row['feature'][:30]:30s}: ${row['coefficient']:8,.0f} ({direction})")

# Categorize features by type
numerical_features = feature_importance[~feature_importance['feature'].str.contains('neighborhood_')]
neighborhood_features = feature_importance[feature_importance['feature'].str.contains('neighborhood_')]

# Create multiple visualizations
fig, axes = plt.subplots(2, 2, figsize=(16, 12))

# 1. Top 10 overall features
top_10 = feature_importance.head(10)
colors = ['red' if x < 0 else 'green' for x in top_10['coefficient']]
axes[0,0].barh(range(len(top_10)), top_10['coefficient'], color=colors, alpha=0.7)
axes[0,0].set_yticks(range(len(top_10)))
axes[0,0].set_yticklabels([f[:20] for f in top_10['feature']], fontsize=8)
axes[0,0].set_xlabel('Coefficient (USD)')
axes[0,0].set_title('Top 10 Most Important Features')
axes[0,0].grid(axis='x', alpha=0.3)

# 2. Numerical features only
if len(numerical_features) > 0:
    axes[0,1].bar(range(len(numerical_features)), numerical_features['coefficient'], alpha=0.7)
    axes[0,1].set_xticks(range(len(numerical_features)))
    axes[0,1].set_xticklabels(numerical_features['feature'], rotation=45, ha='right')
    axes[0,1].set_ylabel('Coefficient (USD)')
    axes[0,1].set_title('Numerical Features Impact')
    axes[0,1].grid(axis='y', alpha=0.3)

# 3. Top 10 neighborhood effects
top_neighborhoods = neighborhood_features.head(10)
neighborhood_names = [f.replace('neighborhood_', '') for f in top_neighborhoods['feature']]
colors_neigh = ['red' if x < 0 else 'green' for x in top_neighborhoods['coefficient']]
axes[1,0].barh(range(len(top_neighborhoods)), top_neighborhoods['coefficient'], color=colors_neigh, alpha=0.7)
axes[1,0].set_yticks(range(len(top_neighborhoods)))
axes[1,0].set_yticklabels(neighborhood_names, fontsize=8)
axes[1,0].set_xlabel('Coefficient (USD)')
axes[1,0].set_title('Top 10 Neighborhood Effects')
axes[1,0].grid(axis='x', alpha=0.3)

# 4. Feature importance distribution
axes[1,1].hist(feature_importance['abs_coefficient'], bins=20, alpha=0.7, edgecolor='black')
axes[1,1].set_xlabel('Absolute Coefficient Value')
axes[1,1].set_ylabel('Count')
axes[1,1].set_title('Distribution of Feature Importance')
axes[1,1].grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

# Summary statistics
print(f"\nFeature Importance Summary:")
print(f"Total features: {len(feature_importance)}")
print(f"Numerical features: {len(numerical_features)}")
print(f"Neighborhood features: {len(neighborhood_features)}")
print(f"Most important feature: {feature_importance.iloc[0]['feature']}")
print(f"Largest positive effect: ${feature_importance['coefficient'].max():,.0f}")
print(f"Largest negative effect: ${feature_importance['coefficient'].min():,.0f}")
\end{lstlisting}

\subsection{Challenging Level Solutions}

\subsubsection{Solution 1: Automated Feature Selection}

\begin{lstlisting}[caption=Statistical Feature Selection Implementation]
from sklearn.feature_selection import SelectKBest, f_regression, RFE
from sklearn.feature_selection import SelectFromModel

print("AUTOMATED FEATURE SELECTION")
print("=" * 35)

# Method 1: Statistical selection (SelectKBest)
print("1. Statistical Feature Selection (F-test)")
selector_stats = SelectKBest(score_func=f_regression, k=10)

# Create pipeline with feature selection
pipeline_stats = make_pipeline(
    OneHotEncoder(use_cat_names=True),
    SimpleImputer(strategy='median'),
    selector_stats,
    Ridge(alpha=best_alpha)
)

# Evaluate statistical selection
cv_scores_stats = cross_val_score(pipeline_stats, X_train, y_train, cv=5, scoring=mae_scorer)
mae_stats = -cv_scores_stats.mean()

print(f"Statistical selection MAE: ${mae_stats:,.0f}")

# Method 2: Recursive Feature Elimination
print("\n2. Recursive Feature Elimination (RFE)")
base_estimator = Ridge(alpha=best_alpha)
selector_rfe = RFE(estimator=base_estimator, n_features_to_select=10)

pipeline_rfe = make_pipeline(
    OneHotEncoder(use_cat_names=True),
    SimpleImputer(strategy='median'),
    selector_rfe
)

cv_scores_rfe = cross_val_score(pipeline_rfe, X_train, y_train, cv=5, scoring=mae_scorer)
mae_rfe = -cv_scores_rfe.mean()

print(f"RFE selection MAE: ${mae_rfe:,.0f}")

# Method 3: L1-based selection (Lasso)
print("\n3. L1-Based Feature Selection (Lasso)")
from sklearn.linear_model import LassoCV

lasso_selector = SelectFromModel(LassoCV(cv=5, random_state=42))

pipeline_lasso = make_pipeline(
    OneHotEncoder(use_cat_names=True),
    SimpleImputer(strategy='median'),
    lasso_selector,
    Ridge(alpha=best_alpha)
)

cv_scores_lasso = cross_val_score(pipeline_lasso, X_train, y_train, cv=5, scoring=mae_scorer)
mae_lasso = -cv_scores_lasso.mean()

print(f"L1-based selection MAE: ${mae_lasso:,.0f}")

# Compare all methods
print(f"\nFEATURE SELECTION COMPARISON:")
print(f"Original model: ${best_mae:,.0f}")
print(f"Statistical (F-test): ${mae_stats:,.0f}")
print(f"RFE: ${mae_rfe:,.0f}")
print(f"L1-based (Lasso): ${mae_lasso:,.0f}")

# Find best method
methods = {
    'Original': best_mae,
    'Statistical': mae_stats,
    'RFE': mae_rfe,
    'L1-based': mae_lasso
}

best_method = min(methods, key=methods.get)
print(f"\nBest method: {best_method} (${methods[best_method]:,.0f})")

# Visualize comparison
plt.figure(figsize=(10, 6))
method_names = list(methods.keys())
method_scores = list(methods.values())

bars = plt.bar(method_names, method_scores, alpha=0.7, 
               color=['blue', 'green', 'orange', 'red'])
plt.ylabel('Cross-Validated MAE (USD)')
plt.title('Feature Selection Methods Comparison')
plt.grid(axis='y', alpha=0.3)

# Add value labels on bars
for bar, score in zip(bars, method_scores):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 500,
             f'${score:,.0f}', ha='center', va='bottom')

plt.show()
\end{lstlisting}

\subsubsection{Solution 2: Ensemble Methods Comparison}

\begin{lstlisting}[caption=Advanced Algorithm Comparison]
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.ensemble import VotingRegressor

print("ENSEMBLE METHODS COMPARISON")
print("=" * 35)

# Prepare data for tree-based models (they handle categorical data differently)
X_encoded = best_model[:-1].fit_transform(X_train)  # All steps except Ridge

# 1. Random Forest
print("1. Training Random Forest...")
rf_model = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
rf_scores = cross_val_score(rf_model, X_encoded, y_train, cv=5, scoring=mae_scorer)
rf_mae = -rf_scores.mean()
print(f"Random Forest MAE: ${rf_mae:,.0f}")

# 2. Gradient Boosting
print("2. Training Gradient Boosting...")
gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
gb_scores = cross_val_score(gb_model, X_encoded, y_train, cv=5, scoring=mae_scorer)
gb_mae = -gb_scores.mean()
print(f"Gradient Boosting MAE: ${gb_mae:,.0f}")

# 3. Voting Regressor (Ensemble of all methods)
print("3. Training Voting Ensemble...")
voting_model = VotingRegressor([
    ('ridge', Ridge(alpha=best_alpha)),
    ('rf', RandomForestRegressor(n_estimators=50, random_state=42)),
    ('gb', GradientBoostingRegressor(n_estimators=50, random_state=42))
])

voting_scores = cross_val_score(voting_model, X_encoded, y_train, cv=5, scoring=mae_scorer)
voting_mae = -voting_scores.mean()
print(f"Voting Ensemble MAE: ${voting_mae:,.0f}")

# Performance comparison
algorithms = {
    'Ridge Regression': best_mae,
    'Random Forest': rf_mae,
    'Gradient Boosting': gb_mae,
    'Voting Ensemble': voting_mae,
    'Baseline': baseline_mae
}

print(f"\nALGORITHM PERFORMANCE COMPARISON:")
print("-" * 45)
for name, score in sorted(algorithms.items(), key=lambda x: x[1]):
    improvement = ((baseline_mae - score) / baseline_mae) * 100
    print(f"{name:18s}: ${score:8,.0f} ({improvement:5.1f}% better than baseline)")

# Best algorithm
best_algorithm = min(algorithms, key=algorithms.get)
if best_algorithm != 'Baseline':
    print(f"\nBest algorithm: {best_algorithm}")

# Detailed analysis of best performer
if rf_mae == min(rf_mae, gb_mae, voting_mae, best_mae):
    print(f"\nRANDOM FOREST FEATURE IMPORTANCE:")
    rf_model.fit(X_encoded, y_train)
    rf_importance = pd.DataFrame({
        'feature': encoder_component.get_feature_names_out(),
        'importance': rf_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("Top 10 features by Random Forest importance:")
    for i, row in rf_importance.head(10).iterrows():
        print(f"{i+1:2d}. {row['feature'][:30]:30s}: {row['importance']:.4f}")

# Visualization
plt.figure(figsize=(12, 8))
alg_names = [name for name in algorithms.keys() if name != 'Baseline']
alg_scores = [algorithms[name] for name in alg_names]

bars = plt.bar(alg_names, alg_scores, alpha=0.7)
plt.axhline(y=baseline_mae, color='red', linestyle='--', 
           label=f'Baseline: ${baseline_mae:,.0f}')
plt.ylabel('Cross-Validated MAE (USD)')
plt.title('Machine Learning Algorithms Comparison')
plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', alpha=0.3)
plt.legend()

# Add value labels
for bar, score in zip(bars, alg_scores):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${score:,.0f}', ha='center', va='bottom', rotation=90)

plt.tight_layout()
plt.show()
\end{lstlisting}

\subsubsection{Solution 3: Complete Web Application Framework}

\begin{lstlisting}[caption=Production-Ready Web Application Structure]
print("COMPLETE WEB APPLICATION FRAMEWORK")
print("=" * 42)

# Create comprehensive web application structure
web_app_structure = '''
# File: app.py
from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
import pickle
import folium
from folium import plugins

app = Flask(__name__)

# Load trained model (saved earlier)
with open('apartment_price_model.pkl', 'rb') as f:
    model = pickle.load(f)

@app.route('/')
def home():
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # Get input data
        area = float(request.form['area'])
        lat = float(request.form['latitude'])  
        lon = float(request.form['longitude'])
        neighborhood = request.form['neighborhood']
        
        # Create input DataFrame
        input_data = pd.DataFrame({
            'surface_covered_in_m2': [area],
            'lat': [lat], 'lon': [lon],
            'neighborhood': [neighborhood]
        })
        
        # Make prediction
        prediction = model.predict(input_data)[0]
        
        # Find similar apartments
        similar_mask = (
            (abs(X_train["surface_covered_in_m2"] - area) <= 15) &
            (abs(X_train["lat"] - lat) <= 0.01) &
            (abs(X_train["lon"] - lon) <= 0.01)
        )
        similar_count = similar_mask.sum()
        
        return jsonify({
            'prediction': f'${prediction:,.0f}',
            'similar_count': similar_count,
            'price_per_sqm': f'${prediction/area:,.0f}',
            'success': True
        })
        
    except Exception as e:
        return jsonify({'error': str(e), 'success': False})

@app.route('/map')
def show_map():
    # Create interactive map with apartment locations
    center_lat = X_train['lat'].mean()
    center_lon = X_train['lon'].mean()
    
    m = folium.Map(location=[center_lat, center_lon], zoom_start=11)
    
    # Add sample points (limit for performance)
    sample_data = X_train.sample(500)
    sample_prices = y_train[sample_data.index]
    
    for idx, row in sample_data.iterrows():
        price = sample_prices[idx]
        color = 'red' if price > sample_prices.median() else 'blue'
        
        folium.CircleMarker(
            location=[row['lat'], row['lon']],
            radius=5,
            popup=f"{row['neighborhood']}: ${price:,.0f}",
            color=color,
            fillColor=color,
            fillOpacity=0.7
        ).add_to(m)
    
    return m._repr_html_()

@app.route('/analytics')
def analytics():
    # Provide market analytics dashboard
    analytics_data = {
        'avg_price': f"${y_train.mean():,.0f}",
        'avg_size': f"{X_train['surface_covered_in_m2'].mean():.0f} sqm",
        'price_per_sqm': f"${(y_train / X_train['surface_covered_in_m2']).mean():,.0f}",
        'total_listings': len(X_train),
        'neighborhoods': X_train['neighborhood'].nunique()
    }
    
    return render_template('analytics.html', data=analytics_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
'''

print("Flask Application Structure:")
print(web_app_structure)

# Create HTML templates structure
html_templates = '''
<!-- File: templates/index.html -->
<!DOCTYPE html>
<html>
<head>
    <title>Buenos Aires Apartment Price Predictor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Buenos Aires Apartment Price Predictor</h1>
        
        <div class="row">
            <div class="col-md-6 offset-md-3">
                <form id="predictionForm">
                    <div class="mb-3">
                        <label class="form-label">Area (square meters)</label>
                        <input type="number" class="form-control" name="area" 
                               min="20" max="300" step="1" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Latitude</label>
                        <input type="number" class="form-control" name="latitude" 
                               step="0.0001" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Longitude</label>
                        <input type="number" class="form-control" name="longitude" 
                               step="0.0001" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Neighborhood</label>
                        <select class="form-control" name="neighborhood" required>
                            <option value="">Select neighborhood...</option>
                            <option value="Palermo">Palermo</option>
                            <option value="Recoleta">Recoleta</option>
                            <option value="San Telmo">San Telmo</option>
                            <!-- More neighborhoods... -->
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        Predict Price
                    </button>
                </form>
                
                <div id="result" class="mt-4" style="display:none;">
                    <div class="alert alert-success">
                        <h4>Prediction Result</h4>
                        <p id="prediction-text"></p>
                        <p id="additional-info"></p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-5">
            <div class="col-md-12 text-center">
                <a href="/map" class="btn btn-secondary">View Map</a>
                <a href="/analytics" class="btn btn-info">Market Analytics</a>
            </div>
        </div>
    </div>
    
    <script>
        $('#predictionForm').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: '/predict',
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.success) {
                        $('#prediction-text').text('Predicted Price: ' + response.prediction);
                        $('#additional-info').text(
                            'Price per sqm: ' + response.price_per_sqm + 
                            ' | Similar apartments: ' + response.similar_count
                        );
                        $('#result').show();
                    } else {
                        alert('Error: ' + response.error);
                    }
                }
            });
        });
    </script>
</body>
</html>
'''

print(f"\nHTML Template Structure:")
print(html_templates)

# Deployment considerations
deployment_info = '''
DEPLOYMENT CHECKLIST:
1. Save trained model: pickle.dump(model, open('apartment_price_model.pkl', 'wb'))
2. Create requirements.txt with all dependencies
3. Set up PostgreSQL database for production data
4. Configure environment variables for API keys
5. Set up Redis for caching predictions
6. Implement user authentication if needed
7. Add rate limiting and input validation
8. Set up monitoring and logging
9. Deploy to cloud platform (AWS, GCP, Azure)
10. Configure domain and SSL certificate

PERFORMANCE OPTIMIZATIONS:
- Cache common predictions
- Use database connection pooling
- Implement asynchronous processing for batch predictions
- Add CDN for static assets
- Use Gunicorn with multiple workers
- Implement database indexing for location queries
'''

print(f"\nDeployment Guidelines:")
print(deployment_info)

# Create model persistence example
print(f"\nMODEL PERSISTENCE EXAMPLE:")
print("# Save the trained model for production use")
print("import pickle")
print("with open('apartment_price_model.pkl', 'wb') as f:")
print("    pickle.dump(best_model, f)")
print()
print("# Load model in production")  
print("with open('apartment_price_model.pkl', 'rb') as f:")
print("    loaded_model = pickle.load(f)")
print()
print("# Verify model works")
print("test_prediction = loaded_model.predict(X_train.head(1))")
print("print(f'Test prediction: ${test_prediction[0]:,.0f}')")
\end{lstlisting}

Congratulations! You've built a sophisticated, production-ready machine learning model that combines multiple data types and handles real-world complexity. This represents professional-level data science skills that are highly valued in industry.

\end{document} 