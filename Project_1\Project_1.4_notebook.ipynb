{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project 1.4: Location or Size - What Influences House Prices in Mexico?\n", "## WorldQuant University - Applied Data Science Lab\n", "\n", "### Learning Objectives\n", "- Investigate research questions using data analysis\n", "- Analyze state-level price variations\n", "- Explore relationships between house size and price\n", "- Calculate and interpret correlation coefficients\n", "- Create comparative visualizations\n", "- Draw insights from data patterns\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(\"Ready to investigate house price factors! 🏠📊\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Data\n", "\n", "Let's start by importing the clean dataset from our previous analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import clean dataset\n", "df = pd.read_csv(\"data/mexico-real-estate-clean.csv\")\n", "\n", "print(\"df type:\", type(df))\n", "print(\"df shape:\", df.shape)\n", "print(\"\\nDataset info:\")\n", "print(df.info())\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Question 1: Which state has the most expensive real estate market?\n", "\n", "Let's investigate how housing prices vary by state and identify the most expensive markets."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate mean house price by state\n", "mean_price_by_state = df.groupby(\"state\")[\"price_usd\"].mean().sort_values(ascending=False)\n", "\n", "print(\"Mean price by state (top 10):\")\n", "print(mean_price_by_state.head(10))\n", "\n", "print(f\"\\nMost expensive state: {mean_price_by_state.index[0]}\")\n", "print(f\"Average price: ${mean_price_by_state.iloc[0]:,.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create bar chart of mean prices by state\n", "plt.figure(figsize=(12, 8))\n", "top_10 = mean_price_by_state.head(10)\n", "bars = plt.bar(range(len(top_10)), top_10.values, color='lightblue', alpha=0.7)\n", "\n", "plt.title('Mean House Price by State (Top 10)', fontsize=16, fontweight='bold')\n", "plt.xlabel('State', fontsize=12)\n", "plt.ylabel('Mean Price [USD]', fontsize=12)\n", "plt.xticks(range(len(top_10)), top_10.index, rotation=45, ha='right')\n", "\n", "# Add value labels\n", "for bar, value in zip(bars, top_10.values):\n", "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000, \n", "             f'${value:,.0f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Price per Square Meter Analysis\n", "\n", "Raw price might be misleading due to house size variations. Let's analyze price per square meter."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create price per m2 column\n", "df[\"price_per_m2\"] = df[\"price_usd\"] / df[\"area_m2\"]\n", "\n", "# Calculate mean price per m2 by state\n", "mean_price_per_m2_by_state = df.groupby(\"state\")[\"price_per_m2\"].mean().sort_values(ascending=False)\n", "\n", "print(\"Mean price per m² by state (top 10):\")\n", "print(mean_price_per_m2_by_state.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create bar chart of price per m2 by state\n", "plt.figure(figsize=(12, 8))\n", "top_10_per_m2 = mean_price_per_m2_by_state.head(10)\n", "bars = plt.bar(range(len(top_10_per_m2)), top_10_per_m2.values, color='coral', alpha=0.7)\n", "\n", "plt.title('Mean House Price per M² by State (Top 10)', fontsize=16, fontweight='bold')\n", "plt.xlabel('State', fontsize=12)\n", "plt.ylabel('Mean Price per M² [USD]', fontsize=12)\n", "plt.xticks(range(len(top_10_per_m2)), top_10_per_m2.index, rotation=45, ha='right')\n", "\n", "# Add value labels\n", "for bar, value in zip(bars, top_10_per_m2.values):\n", "    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 5, \n", "             f'${value:.0f}', ha='center', va='bottom', fontweight='bold')\n", "\n", "plt.grid(axis='y', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"\\nMost expensive per m²: {mean_price_per_m2_by_state.index[0]}\")\n", "print(f\"Price per m²: ${mean_price_per_m2_by_state.iloc[0]:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Question 2: Is there a relationship between home size and price?\n", "\n", "Let's explore whether larger homes cost more using scatter plots and correlation analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create scatter plot of price vs area\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(df[\"area_m2\"], df[\"price_usd\"], alpha=0.6, color='purple')\n", "plt.xlabel(\"Area [sq meters]\", fontsize=12)\n", "plt.ylabel(\"Price [USD]\", fontsize=12)\n", "plt.title(\"Price vs Area - All Mexico\", fontsize=14, fontweight='bold')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate correlation\n", "correlation_all = df[\"area_m2\"].corr(df[\"price_usd\"])\n", "print(f\"\\nCorrelation between area and price (all Mexico): {correlation_all:.4f}\")\n", "print(f\"Interpretation: {'Strong' if abs(correlation_all) > 0.7 else 'Moderate' if abs(correlation_all) > 0.4 else 'Weak'} positive relationship\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### State-Level Analysis: Morelos\n", "\n", "Let's examine the relationship in a specific state to see if patterns vary geographically."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create DataFrame for Morelos\n", "df_morelos = df[df[\"state\"] == \"Morelos\"]\n", "\n", "print(\"df_morelos type:\", type(df_morelos))\n", "print(\"df_morelos shape:\", df_morelos.shape)\n", "print(f\"\\nProperties in Morelos: {len(df_morelos)}\")\n", "df_morelos.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create scatter plot for <PERSON><PERSON>\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(df_morelos[\"area_m2\"], df_morelos[\"price_usd\"], alpha=0.7, color='orange')\n", "plt.xlabel(\"Area [sq meters]\", fontsize=12)\n", "plt.ylabel(\"Price [USD]\", fontsize=12)\n", "plt.title(\"Morelos: Price vs Area\", fontsize=14, fontweight='bold')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate correlation for Morelos\n", "correlation_morelos = df_morelos[\"area_m2\"].corr(df_morelos[\"price_usd\"])\n", "print(f\"\\nCorrelation between area and price (Morelos): {correlation_morelos:.4f}\")\n", "print(f\"Interpretation: {'Strong' if abs(correlation_morelos) > 0.7 else 'Moderate' if abs(correlation_morelos) > 0.4 else 'Weak'} positive relationship\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### State-Level Analysis: Mexico City (Distrito Federal)\n", "\n", "Let's compare with Mexico's capital city to see how urban areas differ."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create DataFrame for Mexico City\n", "df_mexico_city = df[df[\"state\"] == \"Distrito Federal\"]\n", "\n", "print(\"df_mexico_city type:\", type(df_mexico_city))\n", "print(\"df_mexico_city shape:\", df_mexico_city.shape)\n", "print(f\"\\nProperties in Mexico City: {len(df_mexico_city)}\")\n", "\n", "# Create scatter plot for Mexico City\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(df_mexico_city[\"area_m2\"], df_mexico_city[\"price_usd\"], alpha=0.7, color='red')\n", "plt.xlabel(\"Area [sq meters]\", fontsize=12)\n", "plt.ylabel(\"Price [USD]\", fontsize=12)\n", "plt.title(\"Mexico City: Price vs Area\", fontsize=14, fontweight='bold')\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Calculate correlation for Mexico City\n", "correlation_mexico_city = df_mexico_city[\"area_m2\"].corr(df_mexico_city[\"price_usd\"])\n", "print(f\"\\nCorrelation between area and price (Mexico City): {correlation_mexico_city:.4f}\")\n", "print(f\"Interpretation: {'Strong' if abs(correlation_mexico_city) > 0.7 else 'Moderate' if abs(correlation_mexico_city) > 0.4 else 'Weak'} positive relationship\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparative Analysis\n", "\n", "Let's compare the relationships across different regions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comparison visualization\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 6))\n", "\n", "# All Mexico\n", "axes[0].scatter(df[\"area_m2\"], df[\"price_usd\"], alpha=0.6, color='purple')\n", "axes[0].set_xlabel(\"Area [sq meters]\")\n", "axes[0].set_ylabel(\"Price [USD]\")\n", "axes[0].set_title(f\"All Mexico\\nCorrelation: {correlation_all:.3f}\")\n", "axes[0].grid(True, alpha=0.3)\n", "\n", "# Morelos\n", "axes[1].scatter(df_morelos[\"area_m2\"], df_morelos[\"price_usd\"], alpha=0.7, color='orange')\n", "axes[1].set_xlabel(\"Area [sq meters]\")\n", "axes[1].set_ylabel(\"Price [USD]\")\n", "axes[1].set_title(f\"Morelos\\nCorrelation: {correlation_morelos:.3f}\")\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "# Mexico City\n", "axes[2].scatter(df_mexico_city[\"area_m2\"], df_mexico_city[\"price_usd\"], alpha=0.7, color='red')\n", "axes[2].set_xlabel(\"Area [sq meters]\")\n", "axes[2].set_ylabel(\"Price [USD]\")\n", "axes[2].set_title(f\"Mexico City\\nCorrelation: {correlation_mexico_city:.3f}\")\n", "axes[2].grid(True, alpha=0.3)\n", "\n", "plt.suptitle('Price vs Area Comparison Across Regions', fontsize=16, fontweight='bold')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary Analysis\n", "\n", "Let's summarize our key findings about what influences house prices in Mexico."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive summary\n", "print(\"=\" * 60)\n", "print(\"🏠 HOUSE PRICE ANALYSIS SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🏛️ RESEARCH QUESTION 1: Most Expensive States\")\n", "print(f\"  • By total price: {mean_price_by_state.index[0]} (${mean_price_by_state.iloc[0]:,.0f})\")\n", "print(f\"  • By price per m²: {mean_price_per_m2_by_state.index[0]} (${mean_price_per_m2_by_state.iloc[0]:.0f}/m²)\")\n", "print(f\"  • Location matters: Different rankings suggest size vs location trade-offs\")\n", "\n", "print(\"\\n📏 RESEARCH QUESTION 2: Size vs Price Relationship\")\n", "print(f\"  • All Mexico correlation: {correlation_all:.3f} ({'Strong' if abs(correlation_all) > 0.7 else 'Moderate' if abs(correlation_all) > 0.4 else 'Weak'})\")\n", "print(f\"  • Morelos correlation: {correlation_morelos:.3f} ({'Strong' if abs(correlation_morelos) > 0.7 else 'Moderate' if abs(correlation_morelos) > 0.4 else 'Weak'})\")\n", "print(f\"  • Mexico City correlation: {correlation_mexico_city:.3f} ({'Strong' if abs(correlation_mexico_city) > 0.7 else 'Moderate' if abs(correlation_mexico_city) > 0.4 else 'Weak'})\")\n", "\n", "print(\"\\n🔍 KEY INSIGHTS:\")\n", "print(f\"  • Regional variation: Size-price relationship varies by location\")\n", "print(f\"  • Urban vs rural: Mexico City shows weaker size correlation\")\n", "print(f\"  • Market factors: Location premium affects pricing patterns\")\n", "print(f\"  • Investment implications: Different strategies for different regions\")\n", "\n", "print(\"\\n💡 BUSINESS IMPLICATIONS:\")\n", "print(f\"  • For buyers: Consider both size AND location factors\")\n", "print(f\"  • For investors: Regional analysis essential for ROI\")\n", "print(f\"  • For developers: Location premium varies significantly\")\n", "print(f\"  • For analysts: Multi-factor models needed for accurate pricing\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "🎉 **Excellent work!** You've conducted a thorough analysis of house price factors in Mexico!\n", "\n", "### What You've Discovered:\n", "\n", "1. **Location Impact:**\n", "   - Different states show significant price variations\n", "   - Price per m² reveals true market premiums\n", "   - Urban areas command higher prices per unit area\n", "\n", "2. **Size-Price Relationships:**\n", "   - **Positive correlation** exists between size and price\n", "   - **Regional variation** in relationship strength\n", "   - **Urban markets** show different patterns than rural areas\n", "\n", "3. **Market Insights:**\n", "   - Mexico City has unique pricing dynamics\n", "   - Size matters more in some regions than others\n", "   - Multiple factors influence real estate values\n", "\n", "### Skills Developed:\n", "- Research question formulation\n", "- Comparative data analysis\n", "- Correlation interpretation\n", "- Geographic market analysis\n", "- Data-driven insight generation\n", "\n", "**You're ready for advanced predictive modeling!** 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}