Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
3.5. Air Quality in Dar es Salaam 🇹🇿

import warnings
​
import wqet_grader
​
warnings.simplefilter(action="ignore", category=FutureWarning)
wqet_grader.init("Project 3 Assessment")
# Import libraries here
​
Prepare Data
Connect
Task 3.5.1: Connect to MongoDB server running at host "localhost" on port 27017. Then connect to the "air-quality" database and assign the collection for Dar es Salaam to the variable name dar.

client = ...
db = ...
dar = ...
wqet_grader.grade("Project 3 Assessment", "Task 3.5.1", [dar.name])
Explore
Task 3.5.2: Determine the numbers assigned to all the sensor sites in the Dar es Salaam collection. Your submission should be a list of integers. WQU WorldQuant University Applied Data Science Lab QQQQ

sites = ...
sites
wqet_grader.grade("Project 3 Assessment", "Task 3.5.2", sites)
Task 3.5.3: Determine which site in the Dar es Salaam collection has the most sensor readings (of any type, not just PM2.5 readings). You submission readings_per_site should be a list of dictionaries that follows this format:

[{'_id': 6, 'count': 70360}, {'_id': 29, 'count': 131852}]
Note that the values here ☝️ are from the Nairobi collection, so your values will look different.

result = ...
readings_per_site = list(result)
readings_per_site
wqet_grader.grade("Project 3 Assessment", "Task 3.5.3", readings_per_site)
Import
Task 3.5.4: Create a wrangle function that will extract the PM2.5 readings from the site that has the most total readings in the Dar es Salaam collection. Your function should do the following steps:

Localize reading time stamps to the timezone for "Africa/Dar_es_Salaam".
Remove all outlier PM2.5 readings that are above 100.
Resample the data to provide the mean PM2.5 reading for each hour.
Impute any missing values using the forward-fill method.
Return a Series y.
def wrangle(collection):
    
    return y
Use your wrangle function to query the dar collection and return your cleaned results.

y = ...
y.head()
wqet_grader.grade("Project 3 Assessment", "Task 3.5.4", wrangle(dar))
​
Explore Some More
Task 3.5.5: Create a time series plot of the readings in y. Label your x-axis "Date" and your y-axis "PM2.5 Level". Use the title "Dar es Salaam PM2.5 Levels".

fig, ax = plt.subplots(figsize=(15, 6))
​
# Don't delete the code below 👇
plt.savefig("images/3-5-5.png", dpi=150)
​
with open("images/3-5-5.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.5", file)
Task 3.5.6: Plot the rolling average of the readings in y. Use a window size of 168 (the number of hours in a week). Label your x-axis "Date" and your y-axis "PM2.5 Level". Use the title "Dar es Salaam PM2.5 Levels, 7-Day Rolling Average".

fig, ax = plt.subplots(figsize=(15, 6))
​
# Don't delete the code below 👇
plt.savefig("images/3-5-6.png", dpi=150)
​
with open("images/3-5-6.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.6", file)
Task 3.5.7: Create an ACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient". Use the title "Dar es Salaam PM2.5 Readings, ACF".

fig, ax = plt.subplots(figsize=(15, 6))
​
# Don't delete the code below 👇
plt.savefig("images/3-5-7.png", dpi=150)
​
with open("images/3-5-7.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.7", file)
Task 3.5.8: Create an PACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient". Use the title "Dar es Salaam PM2.5 Readings, PACF".

fig, ax = plt.subplots(figsize=(15, 6))
​
# Don't delete the code below 👇
plt.savefig("images/3-5-8.png", dpi=150)
​
with open("images/3-5-8.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.8", file)
Split
Task 3.5.9: Split y into training and test sets. The first 90% of the data should be in your training set. The remaining 10% should be in the test set.

cutoff_test = ...
y_train = ...
y_test = ...
print("y_train shape:", y_train.shape)
print("y_test shape:", y_test.shape)
wqet_grader.grade("Project 3 Assessment", "Task 3.5.9a", y_train)
​
wqet_grader.grade("Project 3 Assessment", "Task 3.5.9b", y_test)
​
Build Model
Baseline
Task 3.5.10: Establish the baseline mean absolute error for your model.

y_train_mean = ...
y_pred_baseline = ...
mae_baseline = ...
​
print("Mean P2 Reading:", y_train_mean)
print("Baseline MAE:", mae_baseline)
wqet_grader.grade("Project 3 Assessment", "Task 3.5.10", mae_baseline)
Iterate
Task 3.5.11: You're going to use an AutoReg model to predict PM2.5 readings, but which hyperparameter settings will give you the best performance? Use a for loop to train your AR model on using settings for lags from 1 to 30. Each time you train a new model, calculate its mean absolute error and append the result to the list maes. Then store your results in the Series mae_series.

Tip: In this task, you'll need to combine the model you learned about in Task 3.3.8 with the hyperparameter tuning technique you learned in Task 3.4.9.

# Create range to test different lags
p_params = range(1, 31)
​
# Create empty list to hold mean absolute error scores
maes = []
​
# Iterate through all values of p in `p_params`
for p in p_params:
    # Build model
    model = ...
​
    # Make predictions on training data, dropping null values caused by lag
    y_pred = model.predict().dropna()
​
    # Calculate mean absolute error for training data vs predictions
    mae = mean_absolute_error(y_train.iloc[p:], y_pred)
​
    # Append `mae` to list `maes`
    maes.append(mae)
​
# Put list `maes` into Series with index `p_params`
mae_series = pd.Series(maes, name="mae", index=p_params)
​
# Inspect head of Series
mae_series.head()
​
wqet_grader.grade("Project 3 Assessment", "Task 3.5.11", mae_series)
Task 3.5.12: Look through the results in mae_series and determine what value for p provides the best performance. Then build and train best_model using the best hyperparameter value.

Note: Make sure that you build and train your model in one line of code, and that the data type of best_model is statsmodels.tsa.ar_model.AutoRegResultsWrapper.

best_p = ...
best_model = ...
wqet_grader.grade(
    "Project 3 Assessment", "Task 3.5.12", [isinstance(best_model.model, AutoReg)]
)
Task 3.5.13: Calculate the training residuals for best_model and assign the result to y_train_resid. Note that your name of your Series should be "residuals".

y_train_resid = ...
y_train_resid.name = "residuals"
y_train_resid.head()
wqet_grader.grade("Project 3 Assessment", "Task 3.5.13", y_train_resid.tail(1500))
​
Task 3.5.14: Create a histogram of y_train_resid. Be sure to label the x-axis as "Residuals" and the y-axis as "Frequency". Use the title "Best Model, Training Residuals".

# Plot histogram of residuals
​
# Don't delete the code below 👇
plt.savefig("images/3-5-14.png", dpi=150)
​
with open("images/3-5-14.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.14", file)
Task 3.5.15: Create an ACF plot for y_train_resid. Be sure to label the x-axis as "Lag [hours]" and y-axis as "Correlation Coefficient". Use the title "Dar es Salaam, Training Residuals ACF".

fig, ax = plt.subplots(figsize=(15, 6))
​
# Don't delete the code below 👇
plt.savefig("images/3-5-15.png", dpi=150)
​
with open("images/3-5-15.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.15", file)
Evaluate
Task 3.5.16: Perform walk-forward validation for your model for the entire test set y_test. Store your model's predictions in the Series y_pred_wfv. Make sure the name of your Series is "prediction" and the name of your Series index is "timestamp".

y_pred_wfv = ...
history = ...
for i in range(len(y_test)):
    
y_pred_wfv.name = "prediction"
y_pred_wfv.index.name = "timestamp"
y_pred_wfv.head()
wqet_grader.grade("Project 3 Assessment", "Task 3.5.16", y_pred_wfv)
​
Task 3.5.17: Submit your walk-forward validation predictions to the grader to see the test mean absolute error for your model.

wqet_grader.grade("Project 3 Assessment", "Task 3.5.17", y_pred_wfv)
​
Communicate Results
Task 3.5.18: Put the values for y_test and y_pred_wfv into the DataFrame df_pred_test (don't forget the index). Then plot df_pred_test using plotly express. In the legend, your lines should be labeled "y_test" and "y_pred_wfv". Be sure to label the x-axis as "Date" and the y-axis as "PM2.5 Level". Use the title "Dar es Salaam, WFV Predictions".

df_pred_test = ...
fig = ...
fig.update_layout(
    title="Dar es Salaam, WFV Predictions",
    xaxis_title="Date",
    yaxis_title="PM2.5 Level",
)
# Don't delete the code below 👇
fig.write_image("images/3-5-18.png", scale=1, height=500, width=700)
​
fig.show()
with open("images/3-5-18.png", "rb") as file:
    wqet_grader.grade("Project 3 Assessment", "Task 3.5.18", file)
Copyright 2024 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
7
Python 3 (ipykernel) | Idle
0
035-assignment.ipynb
Ln 1, Col 1
Mode: Command