Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
1.3. Exploratory Data Analysis

import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
from IPython.display import VimeoVideo
df1 = pd.read_csv('data/mexico-real-estate-1.csv')
df1.dropna(inplace=True)
df1.head()
property_type	state	lat	lon	area_m2	price_usd
0	house	Estado de México	19.560181	-99.233528	150.0	$67,965.56
1	house	Nuevo León	25.688436	-100.198807	186.0	$63,223.78
2	apartment	Guerrero	16.767704	-99.764383	82.0	$84,298.37
3	apartment	Guerrero	16.829782	-99.911012	150.0	$94,308.80
5	house	Yucatán	21.052583	-89.538639	205.0	$105,191.37
df1["price_usd"]=df1["price_usd"].str.replace(",","",regex=False).str.replace("$","",regex=False).astype(float)
df1
property_type	state	lat	lon	area_m2	price_usd
0	house	Estado de México	19.560181	-99.233528	150.0	67965.56
1	house	Nuevo León	25.688436	-100.198807	186.0	63223.78
2	apartment	Guerrero	16.767704	-99.764383	82.0	84298.37
3	apartment	Guerrero	16.829782	-99.911012	150.0	94308.80
5	house	Yucatán	21.052583	-89.538639	205.0	105191.37
...	...	...	...	...	...	...
693	house	Puebla	19.051815	-98.277669	198.0	115910.26
694	apartment	Distrito Federal	19.309208	-99.171906	70.0	77572.89
696	house	Yucatán	21.050653	-89.558841	334.0	137017.34
697	house	Yucatán	21.343796	-89.262060	130.0	110404.35
699	house	Querétaro	20.587378	-100.418361	106.0	56637.97
583 rows × 6 columns

df2 =pd.read_csv('data/mexico-real-estate-2.csv')
df2.dropna(inplace=True)
df2["price_usd"]=(df2["price_mxn"]/19).round(2)
df2
property_type	state	lat	lon	area_m2	price_mxn	price_usd
0	apartment	Nuevo León	25.721081	-100.345581	72.0	1300000.0	68421.05
2	house	Morelos	23.634501	-102.552788	360.0	5300000.0	278947.37
6	apartment	Estado de México	19.272040	-99.572013	85.0	1250000.0	65789.47
7	house	San Luis Potosí	22.138882	-100.996510	158.0	2120000.0	111578.95
8	apartment	Distrito Federal	19.394558	-99.129707	65.0	758190.0	39904.74
...	...	...	...	...	...	...	...
695	house	Morelos	18.917542	-98.963181	140.0	1450000.0	76315.79
696	house	Distrito Federal	19.472128	-99.146697	190.0	1943000.0	102263.16
697	house	Estado de México	19.234984	-99.558175	115.0	2100000.0	110526.32
698	house	Puebla	18.918714	-98.426639	90.0	890000.0	46842.11
699	house	Yucatán	21.075163	-89.516731	185.0	1695000.0	89210.53
571 rows × 7 columns

df2.drop(columns=["price_mxn"],inplace=True)
df3 = pd.read_csv("data/mexico-real-estate-3.csv")
df3["state"]=df3["place_with_parent_names"].str.split("|",expand=True)[2]
df3.drop(columns=["place_with_parent_names"],inplace=True)
df3
property_type	lat-lon	area_m2	price_usd	state
0	apartment	19.52589,-99.151703	71.0	48550.59	Distrito Federal
1	house	19.2640539,-99.5727534	233.0	168636.73	Estado de México
2	house	19.268629,-99.671722	300.0	86932.69	Estado de México
3	house	NaN	275.0	263432.41	Morelos
4	apartment	19.511938,-96.871956	84.0	68508.67	Veracruz de Ignacio de la Llave
...	...	...	...	...	...
695	house	20.532264,-103.484418	175.0	121178.91	Jalisco
696	house	18.9289862,-99.1802147	100.0	47417.83	Morelos
697	house	21.0284038368,-89.6530058049	81.0	39524.23	Yucatán
698	house	22.11830417,-101.0321938992	360.0	245050.24	San Luis Potosí
699	house	19.233201,-99.558519	115.0	110667.85	Estado de México
700 rows × 5 columns

df3[["lat","lon"]]=df3["lat-lon"].str.split(",",expand=True)
df3[["lat","lon"]]=df3["lat-lon"].str.split(",",expand=True)
df3.dropna(inplace=True)
df3.drop(columns=["lat-lon"],inplace=True)
df3.info()
<class 'pandas.core.frame.DataFrame'>
Int64Index: 582 entries, 0 to 699
Data columns (total 6 columns):
 #   Column         Non-Null Count  Dtype  
---  ------         --------------  -----  
 0   property_type  582 non-null    object 
 1   area_m2        582 non-null    float64
 2   price_usd      582 non-null    float64
 3   state          582 non-null    object 
 4   lat            582 non-null    object 
 5   lon            582 non-null    object 
dtypes: float64(2), object(4)
memory usage: 31.8+ KB
df = pd.concat([df1,df2,df3])
df
property_type	state	lat	lon	area_m2	price_usd
0	house	Estado de México	19.560181	-99.233528	150.0	67965.56
1	house	Nuevo León	25.688436	-100.198807	186.0	63223.78
2	apartment	Guerrero	16.767704	-99.764383	82.0	84298.37
3	apartment	Guerrero	16.829782	-99.911012	150.0	94308.80
5	house	Yucatán	21.052583	-89.538639	205.0	105191.37
...	...	...	...	...	...	...
695	house	Jalisco	20.532264	-103.484418	175.0	121178.91
696	house	Morelos	18.9289862	-99.1802147	100.0	47417.83
697	house	Yucatán	21.0284038368	-89.6530058049	81.0	39524.23
698	house	San Luis Potosí	22.11830417	-101.0321938992	360.0	245050.24
699	house	Estado de México	19.233201	-99.558519	115.0	110667.85
1736 rows × 6 columns

VimeoVideo("656355010", h="3cc6a34eba", width=600)
After importing, the next step in many data science projects is exploratory data analysis (EDA), where you get a feel for your data by summarizing its main characteristics using descriptive statistics and data visualization. A good way to plan your EDA is by looking each column and asking yourself questions what it says about your dataset.

WQU WorldQuant University Applied Data Science Lab QQQQ

Import Data
VimeoVideo("656354357", h="8d99bdbfcd", width=600)
Task 1.3.1: Read the CSV file that you created in the last notebook ("../small-data/mexico-real-estate-clean.csv") into a DataFrame named df. Be sure to check that all your columns are the correct data type before you go to the next task.

What's a DataFrame?
What's a CSV file?
Read a CSV file into a DataFrame using pandas.
df1= pd.read_csv("data/mexico-real-estate-1.csv")
df1.dropna(inplace=True)
df1["price_usd"]=df1["price_usd"].str.replace("$","",regex=False).str.replace(",","").astype(float)
df1.info()
<class 'pandas.core.frame.DataFrame'>
Int64Index: 583 entries, 0 to 699
Data columns (total 6 columns):
 #   Column         Non-Null Count  Dtype  
---  ------         --------------  -----  
 0   property_type  583 non-null    object 
 1   state          583 non-null    object 
 2   lat            583 non-null    float64
 3   lon            583 non-null    float64
 4   area_m2        583 non-null    float64
 5   price_usd      583 non-null    float64
dtypes: float64(4), object(2)
memory usage: 31.9+ KB
df2=pd.read_csv("data/mexico-real-estate-2.csv")
df2.dropna(inplace=True)
df2["price_usd"]=(df2["price_mxn"]/19).round(2)
df2.head()
property_type	state	lat	lon	area_m2	price_mxn	price_usd
0	apartment	Nuevo León	25.721081	-100.345581	72.0	1300000.0	68421.05
2	house	Morelos	23.634501	-102.552788	360.0	5300000.0	278947.37
6	apartment	Estado de México	19.272040	-99.572013	85.0	1250000.0	65789.47
7	house	San Luis Potosí	22.138882	-100.996510	158.0	2120000.0	111578.95
8	apartment	Distrito Federal	19.394558	-99.129707	65.0	758190.0	39904.74
#read "data/mexico-real-estate-clean.csv"
df = pd.read_csv("data/mexico-real-estate-clean.csv")
#df["price_usd"]=df["price_usd"].str.replace("$","",regex=False).str.replace(",","").astype(float)
df
property_type	state	lat	lon	area_m2	price_usd
0	house	Estado de México	19.560181	-99.233528	150.0	67965.56
1	house	Nuevo León	25.688436	-100.198807	186.0	63223.78
2	apartment	Guerrero	16.767704	-99.764383	82.0	84298.37
3	apartment	Guerrero	16.829782	-99.911012	150.0	94308.80
4	house	Yucatán	21.052583	-89.538639	205.0	105191.37
...	...	...	...	...	...	...
1731	house	Jalisco	20.532264	-103.484418	175.0	121178.91
1732	house	Morelos	18.928986	-99.180215	100.0	47417.83
1733	house	Yucatán	21.028404	-89.653006	81.0	39524.23
1734	house	San Luis Potosí	22.118304	-101.032194	360.0	245050.24
1735	house	Estado de México	19.233201	-99.558519	115.0	110667.85
1736 rows × 6 columns

# Print object type, shape, and head
print("df type:", type(df))
print("df shape:", df.shape)
df.info()
df type: <class 'pandas.core.frame.DataFrame'>
df shape: (1736, 6)
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 1736 entries, 0 to 1735
Data columns (total 6 columns):
 #   Column         Non-Null Count  Dtype  
---  ------         --------------  -----  
 0   property_type  1736 non-null   object 
 1   state          1736 non-null   object 
 2   lat            1736 non-null   float64
 3   lon            1736 non-null   float64
 4   area_m2        1736 non-null   float64
 5   price_usd      1736 non-null   float64
dtypes: float64(4), object(2)
memory usage: 81.5+ KB
# Median
med_lat,med_lon = df['lat'].median(),df['lon'].median()
#med_lat,med_lon = df['lat'].mean(),df['lon'].mean()
print(med_lat,med_lon)
19.620518 -99.204001
While there are only two dtypes in our DataFrame (object and float64), there are three categories of data: location, categorical, and numeric. Each of these require a different kind of exploration in our analysis.

Location Data: "lat" and "lon"
They say that the most important thing in real estate is location, and we can see where where in Mexico our houses are located by using the "lat" and "lon" columns. Since latitude and longitude are based on a coordinate system, a good way to visualize them is to create a scatter plot on top of a map. A great tool for this is the scatter_mapbox from the plotly library.

VimeoVideo("656353826", h="236e9c5d43", width=600)
Task 1.3.2: Add "lat" and "lon" to the code below, and run the code. You'll see a map that's centered on Mexico City, and you can use the "Zoom Out" button in the upper-right corner of the map so that you can see the whole country.

What's location data?
What's a scatter plot?
# Use plotly express to create figure
fig = px.scatter_mapbox(
    df,  # Our DataFrame
    lat="lat",
    lon="lon",
    center={"lat": 19.43, "lon": -99.13},  # Map will be centered on Mexico City
    width=600,  # Width of map
    height=600,  # Height of map
    hover_data=["price_usd"],  # Display price when hovering mouse over house
)
​
# Add mapbox_style to figure layout
fig.update_layout(mapbox_style="open-street-map")
​
# Show figure
fig.show()

Looking at this map, are the houses in our dataset distributed evenly throughout the country, or are there states or regions that are more prevalent? Can you guess where Mexico's biggest cities are based on this distribution?

Categorical Data: "state"
Even though we can get a good idea of which states are most common in our dataset from looking at a map, we can also get the exact count by using the "state" column.

VimeoVideo("656353463", h="ee8bffd02b", width=600)
Task 1.3.3: Use the value_counts method on the "state" column to determine the 10 most prevalent states in our dataset.

What's categorical data?
What's a Series?
Aggregate data in a Series using value_counts in pandas.
# Get value counts of "state" column
df["state"].value_counts().head(10)
​
Distrito Federal                   303
Estado de México                   179
Yucatán                            171
Morelos                            160
Querétaro                          128
Veracruz de Ignacio de la Llave    117
Puebla                              95
Nuevo León                          83
Jalisco                             60
San Luis Potosí                     55
Name: state, dtype: int64
Numerical Data: "area_m2" and "price_usd"
We have a sense for where the houses in our dataset are located, but how much do they cost? How big are they? The best way to answer those questions is looking at descriptive statistics.

VimeoVideo("656353149", h="2d5b273746", width=600)
Task 1.3.4: Use the describe method to print the mean, standard deviation, and quartiles for the "area_m2" and "price_usd" columns.

What's numerical data?
What's a mean?
What's a standard deviation?
What are quartiles?
Print the summary statistics for a DataFrame using pandas.
# Describe "area_m2", "price_usd" columns
df[["area_m2","price_usd"]].describe()
​
area_m2	price_usd
count	1736.000000	1736.000000
mean	170.261521	115331.980766
std	80.594539	65426.173873
min	60.000000	33157.890000
25%	101.750000	65789.470000
50%	156.000000	99262.130000
75%	220.000000	150846.665000
max	385.000000	326733.660000
Let's start by looking at "area_m2". It's interesting that the mean is larger than the median (another name for the 50% quartile). Both of these statistics are supposed to give an idea of the "typical" value for the column, so why is there a difference of almost 15 m2 between them? To answer this question, we need to see how house sizes are distributed in our dataset. Let's look at two ways to visualize the distribution: a histogram and a boxplot.

VimeoVideo("656352616", h="6075fbacb5", width=600)
Task 1.3.5: Create a histogram of "area_m2". Make sure that the x-axis has the label "Area [sq meters]", the y-axis has the label "Frequency", and the plot has the title "Distribution of Home Sizes".

What's a histogram?
Create a histogram using Matplotlib.
# Use Matplotlib to create histogram of "area_m2"
plt.hist(df["area_m2"])
​
# Add x-axis label
plt.xlabel("Area [sq meters]")
​
# Add y-axis label
plt.ylabel("Frequency")
​
# Add title
plt.title("Distribution of Home Sizes");

Looking at our histogram, we can see that "area_m2" skews right. In other words, there are more houses at the lower end of the distribution (50–200m2) than at the higher end (250–400m2). That explains the difference between the mean and the median.

VimeoVideo("656352166", h="5531b6e160", width=600)
Task 1.3.6: Create a horizontal boxplot of "area_m2". Make sure that the x-axis has the label "Area [sq meters]" and the plot has the title "Distribution of Home Sizes". How is the distribution and its left skew represented differently here than in your histogram?

What's a boxplot?
What's a skewed distribution?
Create a boxplot using Matplotlib.
# Use Matplotlib to create boxplot of "area_m2"
​
plt.boxplot(df["area_m2"],vert = False)
plt.grid(axis='x')
# Add x-axis label
plt.xlabel("Area [sq meters]")
​
# Add title
plt.title("Distribution of Home Sizes");

Does "price_usd" have the same distribution as "price_per_m2"? Let's use the same two visualization tools to find out.

VimeoVideo("656351977", h="a0868bd01e", width=600)
Task 1.3.7: Create a histogram of "price_usd". Make sure that the x-axis has the label "Price [USD]", the y-axis has the label "Frequency", and the plot has the title "Distribution of Home Prices".

What's a histogram?
Create a histogram using Matplotlib.
# Use Matplotlib to create histogram of "price_usd"
plt.hist(df["price_usd"])
​
# Add x-axis label
plt.xlabel("Price [usd]")
​
# Add y-axis label
plt.ylabel("Frequency")
​
# Add title
plt.title("Distribution of Home Prices");

Looks like "price_usd" is even more skewed than "area_m2". What does this bigger skew look like in a boxplot?

VimeoVideo("656351234", h="44ca8af7ac", width=600)
Task 1.3.8: Create a horizontal boxplot of "price_usd". Make sure that the x-axis has the label "Price [USD]" and the plot has the title "Distribution of Home Prices".

What's a boxplot?
What's an outlier?
Create a boxplot using Matplotlib.
# Use Matplotlib to create boxplot of "price_usd"
plt.boxplot(df["price_usd"],vert=False)
plt.grid(axis='x')
# Add x-label axis
plt.xlabel("Price [USD]")
​
# Add y-label axis
plt.title("Distribution of Home Prices");

Excellent job! Now that you have a sense of for the dataset, let's move to the next notebook and start answering some research questions about the relationship between house size, price, and location.

Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
12
Python 3 (ipykernel) | Idle
0
013-exploratory-data-analysis.ipynb
Ln 1, Col 1
Mode: Command