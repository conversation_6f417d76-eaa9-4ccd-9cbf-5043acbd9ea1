{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comprehensive Data Science Guide\n", "## From Data Organization to Machine Learning\n", "\n", "**WorldQuant University - Applied Data Science Lab**\n", "\n", "This notebook demonstrates key concepts from Projects 1.1 through 2.5:\n", "- Data organization and structures\n", "- Data cleaning and preparation\n", "- Exploratory data analysis\n", "- Machine learning and prediction\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Organization Fundamentals\n", "\n", "Let's start with the basics of organizing data using Python lists, dictionaries, and pandas DataFrames."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import essential libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from glob import glob\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Working with Lists and Dictionaries\n", "\n", "Understanding Python's core data structures is fundamental for data science."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# House data using lists (Project 1.1 approach)\n", "house_list = [115910.26, 128, 4]  # [price, area, rooms]\n", "\n", "print(\"House as list:\", house_list)\n", "print(f\"Price: ${house_list[0]:,.2f}\")\n", "print(f\"Area: {house_list[1]} sqm\")\n", "print(f\"Rooms: {house_list[2]}\")\n", "\n", "# Calculate price per square meter\n", "price_per_sqm = house_list[0] / house_list[1]\n", "print(f\"Price per sqm: ${price_per_sqm:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Same house using dictionary (much clearer!)\n", "house_dict = {\n", "    \"price_usd\": 115910.26,\n", "    \"area_sqm\": 128,\n", "    \"rooms\": 4,\n", "    \"location\": \"Mexico City\"\n", "}\n", "\n", "print(\"House as dictionary:\")\n", "for key, value in house_dict.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# Much more readable calculations\n", "price_per_sqm = house_dict[\"price_usd\"] / house_dict[\"area_sqm\"]\n", "print(f\"\\nPrice per sqm: ${price_per_sqm:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating DataFrames from Real Estate Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample real estate dataset\n", "houses_data = [\n", "    {\"price_usd\": 115910.26, \"area_sqm\": 128, \"rooms\": 4, \"location\": \"Mexico City\"},\n", "    {\"price_usd\": 48718.17, \"area_sqm\": 210, \"rooms\": 3, \"location\": \"Guadalajara\"},\n", "    {\"price_usd\": 28977.56, \"area_sqm\": 58, \"rooms\": 2, \"location\": \"Monterrey\"},\n", "    {\"price_usd\": 36932.27, \"area_sqm\": 79, \"rooms\": 3, \"location\": \"Puebla\"},\n", "    {\"price_usd\": 83903.51, \"area_sqm\": 111, \"rooms\": 3, \"location\": \"Tijuana\"}\n", "]\n", "\n", "# Convert to DataFrame\n", "df = pd.DataFrame(houses_data)\n", "\n", "print(\"Real Estate DataFrame:\")\n", "print(df)\n", "\n", "print(f\"\\nDataset shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Analysis and Feature Engineering\n", "\n", "Let's add calculated columns and perform basic analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add calculated features\n", "df['price_per_sqm'] = df['price_usd'] / df['area_sqm']\n", "\n", "# Create size categories\n", "def categorize_size(area):\n", "    if area < 80:\n", "        return \"Small\"\n", "    elif area < 150:\n", "        return \"Medium\"\n", "    else:\n", "        return \"Large\"\n", "\n", "df['size_category'] = df['area_sqm'].apply(categorize_size)\n", "\n", "print(\"Enhanced DataFrame:\")\n", "print(df)\n", "\n", "print(f\"\\nBasic Statistics:\")\n", "print(df.describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analysis by location\n", "print(\"Average price by location:\")\n", "location_stats = df.groupby('location').agg({\n", "    'price_usd': 'mean',\n", "    'area_sqm': 'mean',\n", "    'price_per_sqm': 'mean'\n", "}).round(2)\n", "\n", "print(location_stats)\n", "\n", "# Find most expensive location\n", "most_expensive = location_stats['price_per_sqm'].idxmax()\n", "print(f\"\\nMost expensive location: {most_expensive}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Visualization\n", "\n", "Visual exploration helps us understand patterns in the data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualizations\n", "fig, axes = plt.subplots(2, 2, figsize=(12, 10))\n", "\n", "# Price distribution\n", "axes[0,0].hist(df['price_usd'], bins=5, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0,0].set_title('Price Distribution')\n", "axes[0,0].set_xlabel('Price (USD)')\n", "axes[0,0].set_ylabel('Count')\n", "\n", "# Price vs Area scatter\n", "axes[0,1].scatter(df['area_sqm'], df['price_usd'], alpha=0.7, s=100)\n", "axes[0,1].set_title('Price vs Area')\n", "axes[0,1].set_xlabel('Area (sqm)')\n", "axes[0,1].set_ylabel('Price (USD)')\n", "\n", "# Price by location\n", "df.groupby('location')['price_usd'].mean().plot(kind='bar', ax=axes[1,0], color='lightgreen')\n", "axes[1,0].set_title('Average Price by Location')\n", "axes[1,0].set_ylabel('Price (USD)')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "\n", "# Size category distribution\n", "df['size_category'].value_counts().plot(kind='pie', ax=axes[1,1], autopct='%1.1f%%')\n", "axes[1,1].set_title('Size Category Distribution')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Cleaning Pipeline\n", "\n", "Real-world data is messy. Let's demonstrate cleaning techniques."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create messy data to demonstrate cleaning\n", "messy_data = {\n", "    'price_text': ['$115,910.26', '$48,718', '28977.56', '$36,932.27', '83903'],\n", "    'area_sqm': [128, 210, np.nan, 79, 111],\n", "    'location': [' Mexico City ', 'GUADALAJARA', ' monterrey', 'Puebla', 'tijuana '],\n", "    'rooms': [4, 3, 2, 3, np.nan]\n", "}\n", "\n", "messy_df = pd.DataFrame(messy_data)\n", "print(\"Messy data:\")\n", "print(messy_df)\n", "print(f\"\\nMissing values:\\n{messy_df.isnull().sum()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data cleaning pipeline\n", "def clean_real_estate_data(df):\n", "    \"\"\"Comprehensive data cleaning function\"\"\"\n", "    \n", "    df_clean = df.copy()\n", "    \n", "    # Clean price column\n", "    df_clean['price_usd'] = (df_clean['price_text']\n", "                            .str.replace('$', '', regex=False)\n", "                            .str.replace(',', '', regex=False)\n", "                            .astype(float))\n", "    \n", "    # Clean location names\n", "    df_clean['location_clean'] = (df_clean['location']\n", "                                 .str.strip()\n", "                                 .str.title())\n", "    \n", "    # Handle missing values\n", "    df_clean['area_sqm'] = df_clean['area_sqm'].fillna(df_clean['area_sqm'].median())\n", "    df_clean['rooms'] = df_clean['rooms'].fillna(df_clean['rooms'].mode()[0])\n", "    \n", "    # Add calculated features\n", "    df_clean['price_per_sqm'] = df_clean['price_usd'] / df_clean['area_sqm']\n", "    \n", "    return df_clean\n", "\n", "# Apply cleaning\n", "clean_df = clean_real_estate_data(messy_df)\n", "\n", "print(\"Cleaned data:\")\n", "print(clean_df[['price_usd', 'area_sqm', 'location_clean', 'rooms', 'price_per_sqm']])\n", "print(f\"\\nMissing values after cleaning:\\n{clean_df.isnull().sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Machine Learning - Price Prediction\n", "\n", "Let's build a simple model to predict apartment prices."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import ML libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_absolute_error, r2_score\n", "from sklearn.preprocessing import LabelEncoder\n", "\n", "# Prepare larger dataset for ML\n", "np.random.seed(42)\n", "n_samples = 100\n", "\n", "# Generate synthetic real estate data\n", "locations = ['Mexico City', 'Guadalajara', 'Monterrey', 'Puebla', 'Tijuana']\n", "ml_data = {\n", "    'area_sqm': np.random.normal(100, 30, n_samples),\n", "    'rooms': np.random.choice([1, 2, 3, 4, 5], n_samples, p=[0.1, 0.2, 0.4, 0.2, 0.1]),\n", "    'location': np.random.choice(locations, n_samples),\n", "    'age_years': np.random.randint(0, 50, n_samples)\n", "}\n", "\n", "ml_df = pd.DataFrame(ml_data)\n", "\n", "# Create realistic price based on features\n", "location_multiplier = {'Mexico City': 1.5, 'Guadalajara': 1.0, 'Monterrey': 1.2, \n", "                      'Puebla': 0.8, 'Tijuana': 1.1}\n", "ml_df['location_mult'] = ml_df['location'].map(location_multiplier)\n", "\n", "ml_df['price_usd'] = (ml_df['area_sqm'] * 800 * ml_df['location_mult'] + \n", "                     ml_df['rooms'] * 5000 - \n", "                     ml_df['age_years'] * 100 + \n", "                     np.random.normal(0, 5000, n_samples))\n", "\n", "# Ensure positive prices\n", "ml_df['price_usd'] = ml_df['price_usd'].clip(lower=10000)\n", "\n", "print(f\"ML Dataset shape: {ml_df.shape}\")\n", "print(ml_df.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Prepare features for machine learning\n", "le = LabelEncoder()\n", "ml_df['location_encoded'] = le.fit_transform(ml_df['location'])\n", "\n", "# Feature matrix and target\n", "features = ['area_sqm', 'rooms', 'age_years', 'location_encoded']\n", "X = ml_df[features]\n", "y = ml_df['price_usd']\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "print(f\"Training set: {X_train.shape}\")\n", "print(f\"Test set: {X_test.shape}\")\n", "\n", "# Train model\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "# Make predictions\n", "y_pred = model.predict(X_test)\n", "\n", "# Evaluate model\n", "mae = mean_absolute_error(y_test, y_pred)\n", "r2 = r2_score(y_test, y_pred)\n", "\n", "print(f\"\\nModel Performance:\")\n", "print(f\"Mean Absolute Error: ${mae:,.0f}\")\n", "print(f\"R-squared Score: {r2:.3f}\")\n", "print(f\"Model explains {r2*100:.1f}% of price variation\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Feature importance analysis\n", "feature_importance = pd.DataFrame({\n", "    'feature': features,\n", "    'coefficient': model.coef_\n", "})\n", "\n", "print(\"Feature Importance (Coefficients):\")\n", "print(feature_importance)\n", "\n", "# Visualize predictions vs actual\n", "plt.figure(figsize=(10, 6))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.scatter(y_test, y_pred, alpha=0.7)\n", "plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "plt.xlabel('Actual Price')\n", "plt.ylabel('Predicted Price')\n", "plt.title('Predictions vs Actual')\n", "\n", "plt.subplot(1, 2, 2)\n", "feature_importance_sorted = feature_importance.reindex(\n", "    feature_importance['coefficient'].abs().sort_values(ascending=True).index\n", ")\n", "plt.barh(feature_importance_sorted['feature'], feature_importance_sorted['coefficient'])\n", "plt.xlabel('Coefficient Value')\n", "plt.title('Feature Importance')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Practical Example: Price Prediction Function\n", "\n", "Let's create a user-friendly function to predict apartment prices."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_apartment_price(area_sqm, rooms, age_years, location):\n", "    \"\"\"\n", "    Predict apartment price based on features\n", "    \n", "    Parameters:\n", "    - area_sqm: Area in square meters\n", "    - rooms: Number of rooms\n", "    - age_years: Age of the building in years\n", "    - location: City name\n", "    \n", "    Returns:\n", "    - Predicted price in USD\n", "    \"\"\"\n", "    \n", "    # Encode location\n", "    location_mapping = {name: code for code, name in enumerate(le.classes_)}\n", "    \n", "    if location not in location_mapping:\n", "        print(f\"Warning: Unknown location '{location}'. Using average location effect.\")\n", "        location_encoded = 2  # Use middle value\n", "    else:\n", "        location_encoded = location_mapping[location]\n", "    \n", "    # Create feature array\n", "    features_array = np.array([[area_sqm, rooms, age_years, location_encoded]])\n", "    \n", "    # Make prediction\n", "    predicted_price = model.predict(features_array)[0]\n", "    \n", "    return max(predicted_price, 0)  # Ensure non-negative price\n", "\n", "# Test the function\n", "print(\"Price Prediction Examples:\")\n", "print(\"=\" * 30)\n", "\n", "examples = [\n", "    (120, 3, 5, 'Mexico City'),\n", "    (80, 2, 10, 'Guadalajara'),\n", "    (150, 4, 2, '<PERSON><PERSON>')\n", "]\n", "\n", "for area, rooms, age, location in examples:\n", "    price = predict_apartment_price(area, rooms, age, location)\n", "    print(f\"{area}sqm, {rooms} rooms, {age} years old in {location}: ${price:,.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Advanced Analysis: Market Insights\n", "\n", "Let's derive business insights from our analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Market analysis\n", "market_analysis = ml_df.groupby('location').agg({\n", "    'price_usd': ['mean', 'median', 'std'],\n", "    'area_sqm': 'mean',\n", "    'rooms': 'mean',\n", "    'age_years': 'mean'\n", "}).round(2)\n", "\n", "# Flatten column names\n", "market_analysis.columns = ['_'.join(col).strip() for col in market_analysis.columns]\n", "\n", "# Calculate price per sqm\n", "market_analysis['price_per_sqm'] = market_analysis['price_usd_mean'] / market_analysis['area_sqm_mean']\n", "\n", "print(\"Market Analysis by Location:\")\n", "print(market_analysis)\n", "\n", "# Find best value locations\n", "best_value = market_analysis['price_per_sqm'].idxmin()\n", "most_expensive = market_analysis['price_per_sqm'].idxmax()\n", "\n", "print(f\"\\nMarket Insights:\")\n", "print(f\"Best value location: {best_value}\")\n", "print(f\"Most expensive location: {most_expensive}\")\n", "\n", "# Investment recommendations\n", "print(f\"\\nInvestment Recommendations:\")\n", "for location in market_analysis.index:\n", "    avg_price = market_analysis.loc[location, 'price_usd_mean']\n", "    price_per_sqm = market_analysis.loc[location, 'price_per_sqm']\n", "    \n", "    if price_per_sqm < market_analysis['price_per_sqm'].median():\n", "        recommendation = \"BUY - Good value\"\n", "    elif price_per_sqm > market_analysis['price_per_sqm'].quantile(0.75):\n", "        recommendation = \"HOLD - Expensive market\"\n", "    else:\n", "        recommendation = \"CONSIDER - Average pricing\"\n", "    \n", "    print(f\"{location}: {recommendation} (${price_per_sqm:.0f}/sqm)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. <PERSON><PERSON><PERSON> and <PERSON> Takeaways\n", "\n", "### What We've Learned:\n", "\n", "1. **Data Organization**: From lists to dictionaries to DataFrames\n", "2. **Data Cleaning**: Handling messy real-world data\n", "3. **Feature Engineering**: Creating meaningful variables\n", "4. **Visualization**: Understanding data through charts\n", "5. **Machine Learning**: Building predictive models\n", "6. **Business Insights**: Translating analysis into recommendations\n", "\n", "### Next Steps:\n", "\n", "- Advanced ML techniques (Random Forest, XGBoost)\n", "- Time series analysis for price trends\n", "- Web scraping for real-time data\n", "- Interactive dashboards with Plotly/Streamlit\n", "- Deep learning for complex patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 1. Price distribution by location\n", "ml_df.boxplot(column='price_usd', by='location', ax=axes[0,0])\n", "axes[0,0].set_title('Price Distribution by Location')\n", "axes[0,0].set_xlabel('Location')\n", "axes[0,0].set_ylabel('Price (USD)')\n", "\n", "# 2. Area vs Price relationship\n", "scatter = axes[0,1].scatter(ml_df['area_sqm'], ml_df['price_usd'], \n", "                           c=ml_df['location_encoded'], alpha=0.6, cmap='viridis')\n", "axes[0,1].set_xlabel('Area (sqm)')\n", "axes[0,1].set_ylabel('Price (USD)')\n", "axes[0,1].set_title('Price vs Area (colored by location)')\n", "\n", "# 3. Model performance\n", "axes[1,0].scatter(y_test, y_pred, alpha=0.7)\n", "axes[1,0].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)\n", "axes[1,0].set_xlabel('Actual Price')\n", "axes[1,0].set_ylabel('Predicted Price')\n", "axes[1,0].set_title(f'Model Predictions (R² = {r2:.3f})')\n", "\n", "# 4. Feature importance\n", "feature_importance_sorted = feature_importance.reindex(\n", "    feature_importance['coefficient'].abs().sort_values(ascending=True).index\n", ")\n", "axes[1,1].barh(feature_importance_sorted['feature'], feature_importance_sorted['coefficient'])\n", "axes[1,1].set_xlabel('Coefficient Value')\n", "axes[1,1].set_title('Feature Importance in Price Prediction')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n🎉 Comprehensive Data Science Analysis Complete!\")\n", "print(f\"📊 Analyzed {len(ml_df)} properties across {ml_df['location'].nunique()} locations\")\n", "print(f\"🤖 Built ML model with {r2:.1%} accuracy\")\n", "print(f\"💡 Generated actionable market insights\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}