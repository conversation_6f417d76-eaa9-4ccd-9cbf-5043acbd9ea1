{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project 2.4: Advanced Price Prediction with Size, Location, and Neighborhood\n", "## WorldQuant University - Applied Data Science Lab\n", "\n", "### Learning Objectives\n", "- Build comprehensive machine learning pipelines\n", "- Handle multiple data types (numerical, categorical, location)\n", "- Implement advanced data cleaning and feature selection\n", "- Use regularization techniques (Ridge regression)\n", "- Create interactive prediction tools\n", "- Deploy models for real-world use\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import comprehensive libraries for advanced ML\n", "import warnings\n", "from glob import glob\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from category_encoders import OneHotEncoder\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import Ridge\n", "from sklearn.metrics import mean_absolute_error\n", "from sklearn.pipeline import make_pipeline\n", "from sklearn.utils.validation import check_is_fitted\n", "from ipywidgets import Dropdown, FloatSlider, IntSlider, interact\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "\n", "print(\"🚀 Advanced ML libraries imported!\")\n", "print(\"Ready for comprehensive price prediction modeling!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Data Wrangling Function\n", "\n", "Let's create a sophisticated wrangle function that handles all data quality issues."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    \"\"\"\n", "    Advanced data cleaning and preparation function.\n", "    \n", "    Handles:\n", "    - Basic filtering and subsetting\n", "    - Missing value management\n", "    - Feature extraction and engineering\n", "    - Data leakage prevention\n", "    - Multicollinearity detection\n", "    \"\"\"\n", "    # Read CSV file\n", "    df = pd.read_csv(filepath)\n", "\n", "    # Subset data: Apartments in \"Capital Federal\", less than 400,000\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_price = df[\"price_aprox_usd\"] < 400_000\n", "    df = df[mask_ba & mask_apt & mask_price]\n", "\n", "    # Remove outliers for surface area (10th to 90th percentile)\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "\n", "    # Split \"lat-lon\" column into separate coordinates\n", "    df[[\"lat\", \"lon\"]] = df[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "    df.drop(columns=\"lat-lon\", inplace=True)\n", "\n", "    # Extract neighborhood information\n", "    df[\"neighborhood\"] = df[\"place_with_parent_names\"].str.split(\"|\", expand=True)[3]\n", "    df.drop(columns=\"place_with_parent_names\", inplace=True)\n", "    \n", "    # Drop columns with >50% missing values\n", "    thresh = len(df) / 2\n", "    df = df.dropna(axis=1, thresh=thresh)\n", "\n", "    # Remove high/low cardinality categorical features\n", "    df.drop(columns=[\"operation\", \"property_type\", \"currency\", \"properati_url\"], \n", "            inplace=True, errors='ignore')\n", "    \n", "    # Remove leakage features (derived from target)\n", "    leakage_cols = ['price', 'price_aprox_local_currency', 'price_per_m2', 'price_usd_per_m2']\n", "    df.drop(columns=leakage_cols, inplace=True, errors='ignore')\n", "    \n", "    # Handle multicollinearity\n", "    corr_cols = [\"surface_total_in_m2\", \"rooms\"]\n", "    df.drop(columns=corr_cols, inplace=True, errors='ignore')\n", "    \n", "    return df\n", "\n", "print(\"✅ Advanced wrangle function defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Import and Preparation\n", "\n", "Let's use advanced techniques to handle multiple data files efficiently."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use glob for efficient file discovery\n", "files = glob(\"data/buenos-aires-real-estate-*.csv\")\n", "print(f\"Found {len(files)} data files:\")\n", "for file in files:\n", "    print(f\"  • {file}\")\n", "\n", "# List comprehension for efficient data loading\n", "frames = [wrangle(file) for file in files]\n", "print(f\"\\nLoaded {len(frames)} DataFrames\")\n", "\n", "# Concatenate all DataFrames\n", "df = pd.concat(frames, ignore_index=True)\n", "print(f\"\\nCombined dataset:\")\n", "print(f\"  • Shape: {df.shape}\")\n", "print(f\"  • Columns: {list(df.columns)}\")\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Data Quality Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive data quality assessment\n", "print(\"=== ADVANCED DATA QUALITY REPORT ===\")\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(f\"\\nMissing values:\")\n", "missing_info = df.isnull().sum()\n", "missing_info = missing_info[missing_info > 0]\n", "if len(missing_info) > 0:\n", "    print(missing_info)\n", "else:\n", "    print(\"No missing values detected! ✅\")\n", "\n", "print(f\"\\nData types:\")\n", "print(df.dtypes)\n", "\n", "print(f\"\\nNumerical feature summary:\")\n", "print(df.select_dtypes(include=['number']).describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Correlation Analysis\n", "\n", "Let's create a correlation heatmap to understand feature relationships."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create correlation matrix for numerical features (excluding target)\n", "numerical_features = df.select_dtypes(include=['number']).drop(columns='price_aprox_usd', errors='ignore')\n", "if len(numerical_features.columns) > 1:\n", "    corr_matrix = numerical_features.corr()\n", "    \n", "    plt.figure(figsize=(10, 8))\n", "    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, \n", "                square=True, linewidths=0.5)\n", "    plt.title('Feature Correlation Heatmap', fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Identify highly correlated pairs\n", "    high_corr_pairs = []\n", "    for i in range(len(corr_matrix.columns)):\n", "        for j in range(i+1, len(corr_matrix.columns)):\n", "            if abs(corr_matrix.iloc[i, j]) > 0.8:\n", "                high_corr_pairs.append((corr_matrix.columns[i], corr_matrix.columns[j], corr_matrix.iloc[i, j]))\n", "    \n", "    if high_corr_pairs:\n", "        print(\"🚨 High correlation pairs detected:\")\n", "        for pair in high_corr_pairs:\n", "            print(f\"  • {pair[0]} - {pair[1]}: {pair[2]:.3f}\")\n", "    else:\n", "        print(\"✅ No multicollinearity issues detected!\")\n", "else:\n", "    print(\"Insufficient numerical features for correlation analysis.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Building Pipeline\n", "\n", "Let's create our feature matrix and target vector for machine learning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define target and features\n", "target = \"price_aprox_usd\"\n", "X_train = df.drop(columns=target)\n", "y_train = df[target]\n", "\n", "print(\"Feature matrix (X_train):\")\n", "print(f\"  • Shape: {X_train.shape}\")\n", "print(f\"  • Features: {list(X_train.columns)}\")\n", "print(f\"\\nTarget vector (y_train):\")\n", "print(f\"  • Shape: {y_train.shape}\")\n", "print(f\"  • Target: {target}\")\n", "\n", "# Quick target analysis\n", "print(f\"\\nTarget statistics:\")\n", "print(f\"  • Mean: ${y_train.mean():,.2f}\")\n", "print(f\"  • Median: ${y_train.median():,.2f}\")\n", "print(f\"  • Range: ${y_train.min():,.0f} - ${y_train.max():,.0f}\")\n", "\n", "X_train.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Baseline Model Performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate baseline MAE (using mean as prediction)\n", "y_mean = y_train.mean()\n", "y_pred_baseline = [y_mean] * len(y_train)\n", "baseline_mae = mean_absolute_error(y_train, y_pred_baseline)\n", "\n", "print(\"📊 BASELINE MODEL PERFORMANCE\")\n", "print(f\"Mean apartment price: ${y_mean:,.2f}\")\n", "print(f\"Baseline MAE: ${baseline_mae:,.2f}\")\n", "print(f\"\\nBaseline represents the error if we always predicted the mean price.\")\n", "print(f\"Our ML model should significantly beat this baseline!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced ML Pipeline Creation\n", "\n", "Let's build a sophisticated pipeline with multiple transformers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comprehensive ML pipeline\n", "model = make_pipeline(\n", "    OneHotEncoder(use_cat_names=True),  # Encode categorical variables\n", "    SimpleImputer(strategy='median'),   # Handle missing values\n", "    Ridge(alpha=1.0)                    # Regularized linear regression\n", ")\n", "\n", "print(\"🔧 Advanced ML Pipeline Created:\")\n", "print(\"  1. OneHotEncoder - Convert categorical to numerical\")\n", "print(\"  2. Simple<PERSON><PERSON><PERSON> - <PERSON><PERSON> missing values\")\n", "print(\"  3. Ridge Regression - Regularized linear model\")\n", "\n", "# Fit the model\n", "print(\"\\n🏋️ Training model...\")\n", "model.fit(X_train, y_train)\n", "print(\"✅ Model training completed!\")\n", "\n", "# Verify model is fitted\n", "check_is_fitted(model[-1])\n", "print(\"✅ Model validation passed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Evaluation and Performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate training predictions\n", "y_pred_training = model.predict(X_train)\n", "training_mae = mean_absolute_error(y_train, y_pred_training)\n", "\n", "print(\"📈 MODEL PERFORMANCE ANALYSIS\")\n", "print(f\"Training MAE: ${training_mae:,.2f}\")\n", "print(f\"Baseline MAE: ${baseline_mae:,.2f}\")\n", "print(f\"Improvement: ${baseline_mae - training_mae:,.2f} ({((baseline_mae - training_mae) / baseline_mae * 100):.1f}%)\")\n", "\n", "if training_mae < baseline_mae:\n", "    print(\"🎉 Our model beats the baseline! Great success!\")\n", "else:\n", "    print(\"⚠️ Model needs improvement - not beating baseline.\")\n", "\n", "# Residual analysis\n", "residuals = y_train - y_pred_training\n", "print(f\"\\nResidual Analysis:\")\n", "print(f\"  • Mean residual: ${residuals.mean():,.2f}\")\n", "print(f\"  • Std residual: ${residuals.std():,.2f}\")\n", "print(f\"  • Max underestimate: ${residuals.max():,.2f}\")\n", "print(f\"  • Max overestimate: ${abs(residuals.min()):,.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Importance Analysis\n", "\n", "Let's understand which features drive our predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract feature importance from trained model\n", "regressor = model.named_steps[\"ridge\"]\n", "encoder = model.named_steps[\"onehotencoder\"]\n", "\n", "# Get feature names and coefficients\n", "feature_names = encoder.get_feature_names_out()\n", "coefficients = regressor.coef_\n", "\n", "# Create feature importance Series\n", "feat_imp = pd.Series(data=coefficients, index=feature_names)\n", "feat_imp = feat_imp.sort_values(key=abs, ascending=False)\n", "\n", "print(f\"📊 FEATURE IMPORTANCE ANALYSIS\")\n", "print(f\"Total features: {len(feat_imp)}\")\n", "print(f\"\\nTop 10 Most Important Features:\")\n", "for i, (feature, importance) in enumerate(feat_imp.head(10).items(), 1):\n", "    print(f\"{i:2d}. {feature:<30} {importance:>10,.2f}\")\n", "\n", "# Visualize top features\n", "top_15_features = feat_imp.head(15)\n", "plt.figure(figsize=(12, 8))\n", "top_15_features.plot(kind='barh', color='skyblue', alpha=0.7)\n", "plt.title('Top 15 Features by Absolute Coefficient Value', fontsize=14, fontweight='bold')\n", "plt.xlabel('Coefficient Value (USD)')\n", "plt.y<PERSON><PERSON>('Feature')\n", "plt.grid(axis='x', alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Deployment: Prediction Function\n", "\n", "Let's create a user-friendly prediction function for real-world deployment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def make_prediction(area, lat, lon, neighborhood):\n", "    \"\"\"\n", "    Make apartment price prediction using trained model.\n", "    \n", "    Parameters:\n", "    -----------\n", "    area : float\n", "        Apartment area in square meters\n", "    lat : float\n", "        Latitude coordinate\n", "    lon : float\n", "        Longitude coordinate\n", "    neighborhood : str\n", "        Neighborhood name\n", "    \n", "    Returns:\n", "    --------\n", "    str : Formatted prediction result\n", "    \"\"\"\n", "    # Create input DataFrame with proper column names\n", "    input_data = pd.DataFrame({\n", "        'surface_covered_in_m2': [area],\n", "        'lat': [lat], \n", "        'lon': [lon],\n", "        'neighborhood': [neighborhood]\n", "    })\n", "    \n", "    # Make prediction\n", "    prediction = model.predict(input_data)[0]\n", "    \n", "    # Format result\n", "    return f\"💰 Predicted apartment price: ${prediction:,.2f}\"\n", "\n", "# Test the function\n", "test_prediction = make_prediction(110, -34.60, -58.46, \"Villa Crespo\")\n", "print(\"🧪 Test Prediction:\")\n", "print(test_prediction)\n", "print(\"\\n✅ Prediction function ready for deployment!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Dashboard Creation\n", "\n", "Let's create an interactive widget for real-time predictions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get value ranges for sliders\n", "area_min, area_max = X_train[\"surface_covered_in_m2\"].min(), X_train[\"surface_covered_in_m2\"].max()\n", "lat_min, lat_max = X_train[\"lat\"].min(), X_train[\"lat\"].max()\n", "lon_min, lon_max = X_train[\"lon\"].min(), X_train[\"lon\"].max()\n", "neighborhoods = sorted(X_train[\"neighborhood\"].unique())\n", "\n", "print(\"🎛️ INTERACTIVE PREDICTION DASHBOARD\")\n", "print(f\"Area range: {area_min:.0f} - {area_max:.0f} m²\")\n", "print(f\"Latitude range: {lat_min:.3f} - {lat_max:.3f}\")\n", "print(f\"Longitude range: {lon_min:.3f} - {lon_max:.3f}\")\n", "print(f\"Neighborhoods available: {len(neighborhoods)}\")\n", "\n", "# Create interactive widget\n", "dashboard = interact(\n", "    make_prediction,\n", "    area=IntSlider(\n", "        min=int(area_min),\n", "        max=int(area_max),\n", "        value=int(X_train[\"surface_covered_in_m2\"].median()),\n", "        description='Area (m²):'\n", "    ),\n", "    lat=FloatSlider(\n", "        min=lat_min,\n", "        max=lat_max,\n", "        step=0.01,\n", "        value=X_train[\"lat\"].median(),\n", "        description='Latitude:'\n", "    ),\n", "    lon=FloatSlider(\n", "        min=lon_min,\n", "        max=lon_max,\n", "        step=0.01,\n", "        value=X_train[\"lon\"].median(),\n", "        description='Longitude:'\n", "    ),\n", "    neighborhood=Dropdown(\n", "        options=neighborhoods,\n", "        value=neighborhoods[0],\n", "        description='Neighborhood:'\n", "    )\n", ")\n", "\n", "print(\"\\n🎉 Interactive dashboard created!\")\n", "print(\"Use the sliders above to get real-time price predictions!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model Summary and Insights\n", "\n", "Let's create a comprehensive summary of our advanced model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\" * 80)\n", "print(\"🏆 ADVANCED PRICE PREDICTION MODEL SUMMARY\")\n", "print(\"=\" * 80)\n", "\n", "print(\"\\n📊 DATASET CHARACTERISTICS:\")\n", "print(f\"  • Properties analyzed: {len(df):,}\")\n", "print(f\"  • Features used: {X_train.shape[1]}\")\n", "print(f\"  • Price range: ${y_train.min():,.0f} - ${y_train.max():,.0f}\")\n", "print(f\"  • Average price: ${y_train.mean():,.0f}\")\n", "\n", "print(\"\\n🔧 MODEL ARCHITECTURE:\")\n", "print(f\"  • Pipeline steps: OneHotEncoder → SimpleImputer → Ridge\")\n", "print(f\"  • Algorithm: Regularized Linear Regression (Ridge)\")\n", "print(f\"  • Features after encoding: {len(feature_names)}\")\n", "print(f\"  • Regularization: L2 penalty for overfitting prevention\")\n", "\n", "print(\"\\n📈 PERFORMANCE METRICS:\")\n", "print(f\"  • Training MAE: ${training_mae:,.2f}\")\n", "print(f\"  • Baseline MAE: ${baseline_mae:,.2f}\")\n", "print(f\"  • Improvement: {((baseline_mae - training_mae) / baseline_mae * 100):.1f}%\")\n", "print(f\"  • Model R²: {model.score(X_train, y_train):.3f}\")\n", "\n", "print(\"\\n🏆 TOP PRICE DRIVERS:\")\n", "for i, (feature, coef) in enumerate(feat_imp.head(5).items(), 1):\n", "    impact = \"increases\" if coef > 0 else \"decreases\"\n", "    print(f\"  {i}. {feature}: ${abs(coef):,.0f} {impact} price\")\n", "\n", "print(\"\\n💼 BUSINESS APPLICATIONS:\")\n", "print(f\"  • Real estate valuation and pricing\")\n", "print(f\"  • Investment opportunity assessment\")\n", "print(f\"  • Market analysis and trend identification\")\n", "print(f\"  • Automated property appraisal systems\")\n", "\n", "print(\"\\n🚀 DEPLOYMENT FEATURES:\")\n", "print(f\"  • Interactive prediction dashboard\")\n", "print(f\"  • RESTful API-ready prediction function\")\n", "print(f\"  • Real-time price estimation capability\")\n", "print(f\"  • User-friendly interface with sliders\")\n", "\n", "print(\"\\n✨ MODEL STRENGTHS:\")\n", "print(f\"  • Handles multiple data types (numerical, categorical, geographic)\")\n", "print(f\"  • Robust to missing values and outliers\")\n", "print(f\"  • Prevents overfitting with regularization\")\n", "print(f\"  • Interpretable feature importance\")\n", "print(f\"  • Ready for production deployment\")\n", "\n", "print(\"\\n\" + \"=\" * 80)\n", "print(\"🎯 SUCCESS: Advanced ML model ready for real-world deployment!\")\n", "print(\"=\" * 80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Key Takeaways\n", "\n", "🎉 **Outstanding Achievement!** You've built a production-ready machine learning system!\n", "\n", "### Advanced Skills Mastered:\n", "\n", "1. **End-to-End ML Pipeline:**\n", "   - **Data ingestion** with automated file discovery\n", "   - **Advanced preprocessing** with multiple transformers\n", "   - **Feature engineering** and selection\n", "   - **Model training** with regularization\n", "   - **Performance evaluation** and validation\n", "\n", "2. **Production-Ready Deployment:**\n", "   - **Interactive dashboards** for user engagement\n", "   - **API-ready functions** for system integration\n", "   - **Real-time predictions** with immediate feedback\n", "   - **Scalable architecture** for production use\n", "\n", "3. **Professional Data Science:**\n", "   - **Data quality assessment** and monitoring\n", "   - **Feature importance analysis** for interpretability\n", "   - **Model validation** and performance metrics\n", "   - **Business impact** communication\n", "\n", "### Technical Excellence:\n", "- Built sophisticated ML pipelines with multiple transformers\n", "- Implemented regularization to prevent overfitting\n", "- Created interpretable and actionable model insights\n", "- Developed user-friendly deployment interfaces\n", "\n", "### Business Impact:\n", "Your model provides immediate value for:\n", "- **Property valuations** with accurate price estimates\n", "- **Investment decisions** based on data-driven insights\n", "- **Market analysis** with feature importance understanding\n", "- **User applications** through interactive interfaces\n", "\n", "**You're now ready to tackle complex, real-world ML projects!** 🚀"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}