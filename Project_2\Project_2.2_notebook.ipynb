{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Project 2.2: Multiple Linear Regression 📊\n", "\n", "## Learning Objectives\n", "By the end of this lesson, you will be able to:\n", "- Build multiple linear regression models with multiple features\n", "- Handle categorical variables using one-hot encoding\n", "- Select features based on correlation analysis\n", "- Compare model performance across different feature sets\n", "- Interpret coefficients in multiple regression\n", "- Implement cross-validation for model evaluation\n", "- Handle multicollinearity issues\n", "\n", "---"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import numpy as np\n", "import seaborn as sns\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import OneHotEncoder\n", "from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score\n", "from sklearn.model_selection import cross_val_score, train_test_split\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.compose import ColumnTransformer\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "\n", "# Set visualization style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## Enhanced Data Import Function\n", "\n", "We'll modify our wrangle function to retain more features for multiple regression."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    \"\"\"\n", "    Import and clean real estate data, keeping multiple features.\n", "    \n", "    Parameters:\n", "    -----------\n", "    filepath : str\n", "        Path to the CSV file to import\n", "        \n", "    Returns:\n", "    --------\n", "    pd.DataFrame\n", "        Cleaned DataFrame with multiple features\n", "    \"\"\"\n", "    # Import data\n", "    df = pd.read_csv(filepath)\n", "    \n", "    # Basic filters\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_cost = df[\"price_aprox_usd\"] < 400_000\n", "    \n", "    # Apply basic filters\n", "    df = df[mask_ba & mask_apt & mask_cost]\n", "    \n", "    # Remove outliers in surface area\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "    \n", "    # Create neighborhood feature from place_with_parent_names\n", "    df[\"neighborhood\"] = df[\"place_with_parent_names\"].str.split(\"|\").str[3]\n", "    \n", "    # Clean price per m2 if it exists\n", "    if 'price_usd_per_m2' in df.columns:\n", "        df['price_usd_per_m2'] = pd.to_numeric(df['price_usd_per_m2'], errors='coerce')\n", "    \n", "    return df\n", "\n", "# Import multiple datasets for better coverage\n", "file_paths = [\n", "    \"data/buenos-aires-real-estate-1.csv\",\n", "    \"data/buenos-aires-real-estate-2.csv\",\n", "    \"data/buenos-aires-real-estate-3.csv\"\n", "]\n", "\n", "# Try to load all available files\n", "dfs = []\n", "for i, filepath in enumerate(file_paths, 1):\n", "    try:\n", "        df_temp = wrangle(filepath)\n", "        dfs.append(df_temp)\n", "        print(f\"Loaded file {i}: {len(df_temp)} records\")\n", "    except FileNotFoundError:\n", "        print(f\"File {i} not found, skipping...\")\n", "\n", "# Combine all datasets\n", "if dfs:\n", "    df = pd.concat(dfs, ignore_index=True)\n", "    print(f\"\\nCombined dataset: {len(df)} total records\")\n", "else:\n", "    print(\"No files found, using sample data...\")\n", "    df = wrangle(\"data/buenos-aires-real-estate-1.csv\")\n", "\n", "print(f\"Final dataset shape: {df.shape}\")"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# Explore the available features\n", "print(\"Available columns:\")\n", "print(list(df.columns))\n", "\n", "print(\"\\nDataset info:\")\n", "df.info()\n", "\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## Feature Selection and Analysis\n", "\n", "### Identifying Numerical Features"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["# Identify numerical features that could be useful\n", "numerical_features = []\n", "target = \"price_aprox_usd\"\n", "\n", "# Check which numerical features are available\n", "potential_features = [\n", "    'surface_covered_in_m2',\n", "    'surface_total_in_m2', \n", "    'rooms',\n", "    'floor',\n", "    'expenses',\n", "    'price_per_m2',\n", "    'price_usd_per_m2'\n", "]\n", "\n", "for feature in potential_features:\n", "    if feature in df.columns:\n", "        # Check if feature has enough non-null values\n", "        non_null_ratio = df[feature].notna().sum() / len(df)\n", "        if non_null_ratio > 0.5:  # At least 50% non-null\n", "            numerical_features.append(feature)\n", "            print(f\"✓ {feature}: {non_null_ratio:.1%} complete\")\n", "        else:\n", "            print(f\"✗ {feature}: {non_null_ratio:.1%} complete (skipped)\")\n", "    else:\n", "        print(f\"✗ {feature}: not available\")\n", "\n", "print(f\"\\nSelected numerical features: {numerical_features}\")"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# Calculate correlation matrix for numerical features\n", "if numerical_features:\n", "    correlation_data = df[numerical_features + [target]].corr()\n", "    \n", "    # Create correlation heatmap\n", "    plt.figure(figsize=(10, 8))\n", "    mask = np.triu(np.ones_like(correlation_data, dtype=bool))\n", "    sns.heatmap(correlation_data, \n", "                mask=mask,\n", "                annot=True, \n", "                cmap='coolwarm', \n", "                center=0,\n", "                square=True,\n", "                linewidths=0.5)\n", "    plt.title('Feature Correlation Matrix')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Show correlations with target variable\n", "    target_correlations = correlation_data[target].drop(target).sort_values(key=abs, ascending=False)\n", "    print(\"\\nCorrelations with target variable (price):\")\n", "    print(target_correlations)\n", "else:\n", "    print(\"No numerical features available for correlation analysis\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["### Categorical Features Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# Analyze neighborhood as a categorical feature\n", "if 'neighborhood' in df.columns:\n", "    # Get neighborhood value counts\n", "    neighborhood_counts = df['neighborhood'].value_counts()\n", "    print(f\"Number of unique neighborhoods: {len(neighborhood_counts)}\")\n", "    print(\"\\nTop 10 neighborhoods by count:\")\n", "    print(neighborhood_counts.head(10))\n", "    \n", "    # Filter to neighborhoods with enough data\n", "    min_properties = 30  # Minimum properties per neighborhood\n", "    major_neighborhoods = neighborhood_counts[neighborhood_counts >= min_properties].index\n", "    print(f\"\\nNeighborhoods with {min_properties}+ properties: {len(major_neighborhoods)}\")\n", "    \n", "    # Calculate average price by neighborhood\n", "    if len(major_neighborhoods) > 0:\n", "        neighborhood_prices = df[df['neighborhood'].isin(major_neighborhoods)].groupby('neighborhood')[target].agg(['mean', 'count', 'std']).round(0)\n", "        neighborhood_prices = neighborhood_prices.sort_values('mean', ascending=False)\n", "        \n", "        print(\"\\nAverage prices by neighborhood:\")\n", "        print(neighborhood_prices.head(10))\n", "        \n", "        # Create visualization\n", "        plt.figure(figsize=(12, 6))\n", "        top_neighborhoods = neighborhood_prices.head(10)\n", "        plt.bar(range(len(top_neighborhoods)), top_neighborhoods['mean'], color='skyblue')\n", "        plt.xlabel('Neighborhood')\n", "        plt.ylabel('Average Price (USD)')\n", "        plt.title('Average Price by Neighborhood (Top 10)')\n", "        plt.xticks(range(len(top_neighborhoods)), top_neighborhoods.index, rotation=45, ha='right')\n", "        plt.tight_layout()\n", "        plt.show()\n", "else:\n", "    print(\"Neighborhood feature not available\")\n", "    major_neighborhoods = []"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## Model Building and Comparison\n", "\n", "### Single Feature Model (Baseline)"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# Prepare data for modeling\n", "# Remove rows with missing values in key features\n", "model_data = df.dropna(subset=[target, 'surface_covered_in_m2'])\n", "print(f\"Model data shape: {model_data.shape}\")\n", "\n", "# Model 1: Single feature (baseline)\n", "X_simple = model_data[['surface_covered_in_m2']]\n", "y = model_data[target]\n", "\n", "model_simple = LinearRegression()\n", "model_simple.fit(X_simple, y)\n", "\n", "# Evaluate simple model\n", "y_pred_simple = model_simple.predict(X_simple)\n", "r2_simple = r2_score(y, y_pred_simple)\n", "mae_simple = mean_absolute_error(y, y_pred_simple)\n", "\n", "print(f\"\\nSingle Feature Model (Area only):\")\n", "print(f\"R² Score: {r2_simple:.4f}\")\n", "print(f\"MAE: ${mae_simple:,.0f}\")\n", "print(f\"Coefficient: ${model_simple.coef_[0]:,.0f} per m²\")\n", "print(f\"Intercept: ${model_simple.intercept_:,.0f}\")"]}, {"cell_type": "markdown", "id": "13", "metadata": {}, "source": ["### Multiple Numerical Features Model"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# Model 2: Multiple numerical features\n", "if len(numerical_features) > 1:\n", "    # Use features with good correlation and minimal missing values\n", "    available_features = []\n", "    for feature in numerical_features:\n", "        if feature != target:  # Don't include target in features\n", "            feature_data = model_data[feature].dropna()\n", "            if len(feature_data) > len(model_data) * 0.7:  # At least 70% coverage\n", "                available_features.append(feature)\n", "    \n", "    if available_features:\n", "        # Prepare data for multiple features\n", "        multi_data = model_data[available_features + [target]].dropna()\n", "        X_multi = multi_data[available_features]\n", "        y_multi = multi_data[target]\n", "        \n", "        model_multi = LinearRegression()\n", "        model_multi.fit(X_multi, y_multi)\n", "        \n", "        # Evaluate multiple features model\n", "        y_pred_multi = model_multi.predict(X_multi)\n", "        r2_multi = r2_score(y_multi, y_pred_multi)\n", "        mae_multi = mean_absolute_error(y_multi, y_pred_multi)\n", "        \n", "        print(f\"\\nMultiple Features Model:\")\n", "        print(f\"Features used: {available_features}\")\n", "        print(f\"Data points: {len(multi_data)}\")\n", "        print(f\"R² Score: {r2_multi:.4f}\")\n", "        print(f\"MAE: ${mae_multi:,.0f}\")\n", "        \n", "        # Show feature coefficients\n", "        print(f\"\\nFeature Coefficients:\")\n", "        for feature, coef in zip(available_features, model_multi.coef_):\n", "            print(f\"{feature}: {coef:,.2f}\")\n", "        print(f\"Intercept: ${model_multi.intercept_:,.0f}\")\n", "        \n", "        # Compare models\n", "        print(f\"\\nModel Comparison:\")\n", "        print(f\"R² improvement: {(r2_multi - r2_simple):.4f} ({((r2_multi/r2_simple)-1)*100:.1f}% increase)\")\n", "        print(f\"MAE improvement: ${mae_simple - mae_multi:,.0f} (${mae_multi:,.0f} vs ${mae_simple:,.0f})\")\n", "    else:\n", "        print(\"Not enough numerical features with sufficient data coverage\")\n", "        X_multi, y_multi, model_multi = None, None, None\n", "else:\n", "    print(\"Only one numerical feature available\")\n", "    X_multi, y_multi, model_multi = None, None, None"]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["### Model with Categorical Features"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# Model 3: Include neighborhood as categorical feature\n", "if len(major_neighborhoods) > 1:\n", "    # Filter data to major neighborhoods only\n", "    neighborhood_data = model_data[model_data['neighborhood'].isin(major_neighborhoods)].copy()\n", "    \n", "    # Prepare features\n", "    base_features = ['surface_covered_in_m2']\n", "    X_base = neighborhood_data[base_features]\n", "    \n", "    # One-hot encode neighborhoods\n", "    encoder = OneHotEncoder(sparse_output=False, drop='first')  # Drop first to avoid multicollinearity\n", "    neighborhood_encoded = encoder.fit_transform(neighborhood_data[['neighborhood']])\n", "    \n", "    # Get feature names\n", "    neighborhood_feature_names = encoder.get_feature_names_out(['neighborhood'])\n", "    \n", "    # Combine numerical and categorical features\n", "    X_with_neighborhood = np.column_stack([X_base.values, neighborhood_encoded])\n", "    feature_names = base_features + list(neighborhood_feature_names)\n", "    \n", "    y_neighborhood = neighborhood_data[target]\n", "    \n", "    # Train model\n", "    model_neighborhood = LinearRegression()\n", "    model_neighborhood.fit(X_with_neighborhood, y_neighborhood)\n", "    \n", "    # Evaluate\n", "    y_pred_neighborhood = model_neighborhood.predict(X_with_neighborhood)\n", "    r2_neighborhood = r2_score(y_neighborhood, y_pred_neighborhood)\n", "    mae_neighborhood = mean_absolute_error(y_neighborhood, y_pred_neighborhood)\n", "    \n", "    print(f\"\\nModel with Neighborhoods:\")\n", "    print(f\"Data points: {len(neighborhood_data)}\")\n", "    print(f\"Neighborhoods included: {len(major_neighborhoods)}\")\n", "    print(f\"R² Score: {r2_neighborhood:.4f}\")\n", "    print(f\"MAE: ${mae_neighborhood:,.0f}\")\n", "    \n", "    # Show most important neighborhood effects\n", "    neighborhood_effects = dict(zip(neighborhood_feature_names, model_neighborhood.coef_[1:]))  # Skip area coefficient\n", "    sorted_effects = sorted(neighborhood_effects.items(), key=lambda x: abs(x[1]), reverse=True)\n", "    \n", "    print(f\"\\nTop 5 neighborhood effects (vs baseline):\")\n", "    for name, effect in sorted_effects[:5]:\n", "        clean_name = name.replace('neighborhood_', '')\n", "        print(f\"{clean_name}: ${effect:,.0f}\")\n", "    \n", "    # Compare with simple model\n", "    print(f\"\\nImprovement over simple model:\")\n", "    print(f\"R² improvement: {(r2_neighborhood - r2_simple):.4f}\")\n", "    print(f\"MAE improvement: ${mae_simple - mae_neighborhood:,.0f}\")\n", "    \n", "else:\n", "    print(\"Not enough neighborhood data for categorical modeling\")\n", "    model_neighborhood = None"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Model Visualization and Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["# Compare all models visually\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Plot 1: Simple model\n", "plt.subplot(1, 3, 1)\n", "plt.scatter(X_simple, y, alpha=0.6, color='lightcoral')\n", "plt.plot(X_simple, y_pred_simple, color='red', linewidth=2)\n", "plt.xlabel('Area (m²)')\n", "plt.ylabel('Price (USD)')\n", "plt.title(f'Simple Model\\nR² = {r2_simple:.3f}')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Plot 2: Residuals comparison\n", "plt.subplot(1, 3, 2)\n", "residuals_simple = y - y_pred_simple\n", "plt.scatter(y_pred_simple, residuals_simple, alpha=0.6, color='orange', label='Simple')\n", "\n", "if model_multi is not None:\n", "    residuals_multi = y_multi - y_pred_multi\n", "    plt.scatter(y_pred_multi, residuals_multi, alpha=0.6, color='blue', label='Multi Features')\n", "\n", "if model_neighborhood is not None:\n", "    residuals_neighborhood = y_neighborhood - y_pred_neighborhood\n", "    plt.scatter(y_pred_neighborhood, residuals_neighborhood, alpha=0.6, color='green', label='With Neighborhoods')\n", "\n", "plt.axhline(y=0, color='red', linestyle='--')\n", "plt.xlabel('Predicted Price (USD)')\n", "plt.ylabel('Residuals (USD)')\n", "plt.title('Residuals Comparison')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Plot 3: Model performance comparison\n", "plt.subplot(1, 3, 3)\n", "models = ['Simple']\n", "r2_scores = [r2_simple]\n", "mae_scores = [mae_simple]\n", "\n", "if model_multi is not None:\n", "    models.append('Multi Features')\n", "    r2_scores.append(r2_multi)\n", "    mae_scores.append(mae_multi)\n", "\n", "if model_neighborhood is not None:\n", "    models.append('With Neighborhoods')\n", "    r2_scores.append(r2_neighborhood)\n", "    mae_scores.append(mae_neighborhood)\n", "\n", "x_pos = np.arange(len(models))\n", "plt.bar(x_pos, r2_scores, color=['lightcoral', 'lightblue', 'lightgreen'][:len(models)])\n", "plt.xlabel('Model Type')\n", "plt.ylabel('R² Score')\n", "plt.title('Model Performance Comparison')\n", "plt.xticks(x_pos, models, rotation=45)\n", "plt.ylim(0, max(r2_scores) * 1.1)\n", "\n", "# Add R² values on bars\n", "for i, score in enumerate(r2_scores):\n", "    plt.text(i, score + 0.01, f'{score:.3f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## Cross-Validation Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# Perform cross-validation on our models\n", "from sklearn.model_selection import cross_val_score\n", "\n", "cv_folds = 5\n", "scoring = 'r2'\n", "\n", "print(f\"Cross-Validation Results ({cv_folds}-fold):\")\n", "print(\"=\" * 50)\n", "\n", "# Simple model CV\n", "cv_scores_simple = cross_val_score(LinearRegression(), X_simple, y, cv=cv_folds, scoring=scoring)\n", "print(f\"Simple Model:\")\n", "print(f\"  Mean R²: {cv_scores_simple.mean():.4f} (±{cv_scores_simple.std():.4f})\")\n", "print(f\"  Range: {cv_scores_simple.min():.4f} to {cv_scores_simple.max():.4f}\")\n", "\n", "# Multiple features model CV (if available)\n", "if model_multi is not None:\n", "    cv_scores_multi = cross_val_score(LinearRegression(), X_multi, y_multi, cv=cv_folds, scoring=scoring)\n", "    print(f\"\\nMultiple Features Model:\")\n", "    print(f\"  Mean R²: {cv_scores_multi.mean():.4f} (±{cv_scores_multi.std():.4f})\")\n", "    print(f\"  Range: {cv_scores_multi.min():.4f} to {cv_scores_multi.max():.4f}\")\n", "\n", "# Neighborhood model CV (if available)\n", "if model_neighborhood is not None:\n", "    cv_scores_neighborhood = cross_val_score(LinearRegression(), X_with_neighborhood, y_neighborhood, cv=cv_folds, scoring=scoring)\n", "    print(f\"\\nWith Neighborhoods Model:\")\n", "    print(f\"  Mean R²: {cv_scores_neighborhood.mean():.4f} (±{cv_scores_neighborhood.std():.4f})\")\n", "    print(f\"  Range: {cv_scores_neighborhood.min():.4f} to {cv_scores_neighborhood.max():.4f}\")\n", "\n", "# Visualize CV results\n", "plt.figure(figsize=(10, 6))\n", "cv_results = [cv_scores_simple]\n", "labels = ['Simple']\n", "\n", "if model_multi is not None:\n", "    cv_results.append(cv_scores_multi)\n", "    labels.append('Multi Features')\n", "\n", "if model_neighborhood is not None:\n", "    cv_results.append(cv_scores_neighborhood)\n", "    labels.append('With Neighborhoods')\n", "\n", "plt.boxplot(cv_results, labels=labels)\n", "plt.ylabel('R² Score')\n", "plt.title('Cross-Validation Performance Comparison')\n", "plt.grid(True, alpha=0.3)\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "21", "metadata": {}, "source": ["## Feature Importance Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["# Analyze feature importance for the best model\n", "best_model = None\n", "best_features = None\n", "best_r2 = r2_simple\n", "best_name = \"Simple\"\n", "\n", "if model_multi is not None and r2_multi > best_r2:\n", "    best_model = model_multi\n", "    best_features = available_features\n", "    best_r2 = r2_multi\n", "    best_name = \"Multi Features\"\n", "\n", "if model_neighborhood is not None and r2_neighborhood > best_r2:\n", "    best_model = model_neighborhood\n", "    best_features = feature_names\n", "    best_r2 = r2_neighborhood\n", "    best_name = \"With Neighborhoods\"\n", "\n", "print(f\"Best Model: {best_name} (R² = {best_r2:.4f})\")\n", "\n", "if best_model is not None and best_features is not None:\n", "    # Calculate feature importance (absolute coefficient values)\n", "    feature_importance = pd.DataFrame({\n", "        'Feature': best_features,\n", "        'Coefficient': best_model.coef_,\n", "        'Abs_Coefficient': np.abs(best_model.coef_)\n", "    })\n", "    \n", "    feature_importance = feature_importance.sort_values('Abs_Coefficient', ascending=False)\n", "    \n", "    print(\"\\nFeature Importance (by absolute coefficient):\")\n", "    print(feature_importance.head(10))\n", "    \n", "    # Visualize top features\n", "    plt.figure(figsize=(12, 6))\n", "    top_features = feature_importance.head(10)\n", "    colors = ['red' if coef < 0 else 'blue' for coef in top_features['Coefficient']]\n", "    \n", "    plt.barh(range(len(top_features)), top_features['Coefficient'], color=colors, alpha=0.7)\n", "    plt.yticks(range(len(top_features)), top_features['Feature'])\n", "    plt.xlabel('Coefficient Value')\n", "    plt.title('Top 10 Feature Coefficients')\n", "    plt.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Model interpretation\n", "print(f\"\\nModel Interpretation:\")\n", "print(f\"• Intercept: ${best_model.intercept_:,.0f}\" if best_model else \"• Simple area-only model\")\n", "print(f\"• This represents the base value when all features are zero\")\n", "print(f\"• Each coefficient shows the expected price change for a 1-unit increase in that feature\")\n", "print(f\"• Red bars indicate negative effects, blue bars indicate positive effects\")"]}, {"cell_type": "markdown", "id": "23", "metadata": {}, "source": ["## Prediction Examples"]}, {"cell_type": "code", "execution_count": null, "id": "24", "metadata": {}, "outputs": [], "source": ["# Make predictions for sample apartments\n", "print(\"Sample Predictions:\")\n", "print(\"=\" * 40)\n", "\n", "# Define sample apartments\n", "sample_apartments = [\n", "    {\"area\": 50, \"description\": \"Small 1-bedroom\"},\n", "    {\"area\": 75, \"description\": \"Standard 2-bedroom\"},\n", "    {\"area\": 100, \"description\": \"Large 3-bedroom\"},\n", "    {\"area\": 150, \"description\": \"Luxury apartment\"}\n", "]\n", "\n", "for apt in sample_apartments:\n", "    area = apt[\"area\"]\n", "    desc = apt[\"description\"]\n", "    \n", "    # Simple model prediction\n", "    simple_pred = model_simple.predict([[area]])[0]\n", "    \n", "    print(f\"\\n{desc} ({area} m²):\")\n", "    print(f\"  Simple model: ${simple_pred:,.0f}\")\n", "    \n", "    # Multi-feature model prediction (if available)\n", "    if model_multi is not None and len(available_features) > 1:\n", "        # Create sample data with default values for other features\n", "        sample_data = {feature: area if 'surface' in feature else \n", "                      df[feature].median() for feature in available_features}\n", "        sample_df = pd.DataFrame([sample_data])\n", "        multi_pred = model_multi.predict(sample_df)[0]\n", "        print(f\"  Multi-feature model: ${multi_pred:,.0f}\")\n", "    \n", "    # Neighborhood model prediction (if available)\n", "    if model_neighborhood is not None:\n", "        # Use most common neighborhood\n", "        common_neighborhood = neighborhood_counts.index[0]\n", "        sample_encoded = encoder.transform([[common_neighborhood]])\n", "        sample_with_neighborhood = np.column_stack([[area], sample_encoded])\n", "        neighborhood_pred = model_neighborhood.predict(sample_with_neighborhood)[0]\n", "        print(f\"  With neighborhood ({common_neighborhood}): ${neighborhood_pred:,.0f}\")\n", "\n", "# Price per square meter analysis\n", "print(f\"\\n\\nPrice per Square Meter Analysis:\")\n", "print(f\"=\" * 40)\n", "for apt in sample_apartments:\n", "    area = apt[\"area\"]\n", "    simple_pred = model_simple.predict([[area]])[0]\n", "    price_per_m2 = simple_pred / area\n", "    print(f\"{apt['description']}: ${price_per_m2:,.0f}/m²\")\n", "\n", "# Compare with actual data\n", "actual_avg_price_per_m2 = (df['price_aprox_usd'] / df['surface_covered_in_m2']).mean()\n", "print(f\"\\nActual average in data: ${actual_avg_price_per_m2:,.0f}/m²\")"]}, {"cell_type": "markdown", "id": "25", "metadata": {}, "source": ["## Model Summary and Insights"]}, {"cell_type": "code", "execution_count": null, "id": "26", "metadata": {}, "outputs": [], "source": ["# Final model comparison summary\n", "print(\"FINAL MODEL COMPARISON SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "models_summary = [\n", "    {\"Model\": \"Simple (Area only)\", \"R²\": r2_simple, \"MAE\": mae_simple, \"Features\": 1}\n", "]\n", "\n", "if model_multi is not None:\n", "    models_summary.append({\n", "        \"Model\": \"Multi-feature\", \n", "        \"R²\": r2_multi, \n", "        \"MAE\": mae_multi, \n", "        \"Features\": len(available_features)\n", "    })\n", "\n", "if model_neighborhood is not None:\n", "    models_summary.append({\n", "        \"Model\": \"With Neighborhoods\", \n", "        \"R²\": r2_neighborhood, \n", "        \"MAE\": mae_neighborhood, \n", "        \"Features\": len(feature_names)\n", "    })\n", "\n", "summary_df = pd.DataFrame(models_summary)\n", "summary_df['MAE_formatted'] = summary_df['MAE'].apply(lambda x: f\"${x:,.0f}\")\n", "summary_df['R²_formatted'] = summary_df['R²'].apply(lambda x: f\"{x:.4f}\")\n", "\n", "print(summary_df[['Model', 'R²_formatted', 'MAE_formatted', 'Features']].to_string(index=False))\n", "\n", "print(f\"\\n\\nKEY INSIGHTS:\")\n", "print(f\"=\" * 20)\n", "print(f\"1. Best performing model: {best_name}\")\n", "print(f\"2. Best R² score: {best_r2:.4f} ({best_r2*100:.1f}% variance explained)\")\n", "if len(models_summary) > 1:\n", "    improvement = (best_r2 - r2_simple) / r2_simple * 100\n", "    print(f\"3. Improvement over simple model: {improvement:.1f}%\")\n", "print(f\"4. Location (neighborhood) {'does' if model_neighborhood and r2_neighborhood > r2_simple*1.1 else 'may not'} significantly impact price\")\n", "print(f\"5. Multiple features {'help' if model_multi and r2_multi > r2_simple*1.05 else 'provide limited benefit'} improve prediction accuracy\")\n", "\n", "print(f\"\\n\\nRECOMMENDATIONS:\")\n", "print(f\"=\" * 20)\n", "print(f\"• Use {best_name.lower()} model for best predictions\")\n", "print(f\"• Consider collecting more data on property features\")\n", "print(f\"• Location data {'is' if model_neighborhood else 'would be'} valuable for pricing\")\n", "print(f\"• Model explains {best_r2*100:.1f}% of price variation - other factors matter too\")"]}, {"cell_type": "markdown", "id": "27", "metadata": {}, "source": ["## Practice Exercises\n", "\n", "Try these exercises to deepen your understanding:\n", "\n", "### Exercise 1: Feature Engineering\n", "Create new features like 'price_per_room', 'total_to_covered_ratio', or 'luxury_score' and test their impact on model performance.\n", "\n", "### Exercise 2: Regularization\n", "Implement Ridge or Lasso regression to handle potential multicollinearity and compare with standard linear regression.\n", "\n", "### Exercise 3: Interaction Features\n", "Create interaction terms between area and neighborhood, or area and number of rooms, and evaluate their effectiveness.\n", "\n", "### Exercise 4: Model Validation\n", "Implement proper train/validation/test splits and evaluate model generalization to unseen data.\n", "\n", "### Exercise 5: Ensemble Methods\n", "Combine predictions from multiple models to create an ensemble predictor and test its performance."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}