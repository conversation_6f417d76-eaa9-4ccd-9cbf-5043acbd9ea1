{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 2.2. Predicting <PERSON> with Location\n", "\n", "## Usage Guidelines\n", "This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.\n", "\n", "This means:\n", "\n", "- ⓧ No downloading this notebook.\n", "- ⓧ No re-sharing of this notebook with friends or colleagues.\n", "- ⓧ No downloading the embedded videos in this notebook.\n", "- ⓧ No re-sharing embedded videos with friends or colleagues.\n", "- ⓧ No adding this notebook to public or private repositories.\n", "- ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import warnings\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "import wqet_grader\n", "from IPython.display import VimeoVideo\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_absolute_error\n", "from sklearn.pipeline import Pipeline, make_pipeline\n", "from sklearn.utils.validation import check_is_fitted\n", "\n", "warnings.simplefilter(action=\"ignore\", category=FutureWarning)\n", "wqet_grader.init(\"Project 2 Assessment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this lesson, we're going to build on the work we did in the previous lesson. We're going to create a more complex wrangle function, use it to clean more data, and build a model that considers more features when predicting apartment price."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656752925\", h=\"701f3f4081\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prepare Data\n", "### Import"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def wrangle(filepath):\n", "    # Read CSV file\n", "    df = pd.read_csv(filepath)\n", "\n", "    # Subset data: Apartments in \"Capital Federal\", less than 400,000\n", "    mask_ba = df[\"place_with_parent_names\"].str.contains(\"Capital Federal\")\n", "    mask_apt = df[\"property_type\"] == \"apartment\"\n", "    mask_price = df[\"price_aprox_usd\"] < 400_000\n", "    df = df[mask_ba & mask_apt & mask_price]\n", "\n", "    # Subset data: Remove outliers for \"surface_covered_in_m2\"\n", "    low, high = df[\"surface_covered_in_m2\"].quantile([0.1, 0.9])\n", "    mask_area = df[\"surface_covered_in_m2\"].between(low, high)\n", "    df = df[mask_area]\n", "    \n", "    # Split \"lat-lon\" into \"lat\" and \"lon\"\n", "    df[[\"lat\", \"lon\"]] = df[\"lat-lon\"].str.split(\",\", expand=True).astype(float)\n", "\n", "    # Drop the original \"lat-lon\" column\n", "    df = df.drop(columns=\"lat-lon\")\n", "\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656752771\", h=\"3a42896eb6\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.2.1: Create DataFrame from First File\n", "\n", "Use your wrangle function to create a DataFrame frame1 from the CSV file data/buenos-aires-real-estate-1.csv."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame1 = wrangle('data/buenos-aires-real-estate-1.csv')\n", "print(frame1.info())\n", "frame1.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For our model, we're going to consider apartment location, specifically, latitude and longitude. Looking at the output from frame1.info(), we can see that the location information is in a single column where the data type is object (pandas term for str in this case). In order to build our model, we need latitude and longitude to each be in their own column where the data type is float."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656751955\", h=\"e47002428d\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.2.2: Split Location Data\n", "\n", "Add to the wrangle function below so that, in the DataFrame it returns, the \"lat-lon\" column is replaced by separate \"lat\" and \"lon\" columns. Don't forget to also drop the \"lat-lon\" column. Be sure to rerun all the cells above before you continue.\n", "\n", "**What's a function?**\n", "\n", "**Split the strings in one column to create another using pandas.**\n", "**Drop a column from a DataFrame using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    frame1.shape[0] == 1343\n", "), f\"`frame1` should have 1343 rows, not {frame1.shape[0]}.\"\n", "assert frame1.shape[1] == 17, f\"`frame1` should have 17 columns, not {frame1.shape[1]}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that our wrangle function is working, let's use it to clean more data!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656751853\", h=\"da40b0a474\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.2.3: Create Second DataFrame\n", "\n", "Use you revised wrangle function create a DataFrames frame2 from the file data/buenos-aires-real-estate-2.csv."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["frame2 = wrangle(\"data/buenos-aires-real-estate-2.csv\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert (\n", "    frame2.shape[0] == 1315\n", "), f\"`frame1` should have 1315 rows, not {frame2.shape[0]}.\"\n", "assert frame2.shape[1] == 17, f\"`frame1` should have 17 columns, not {frame2.shape[1]}.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["As you can see, using a function is much quicker than cleaning each file individually like we did in the last project. Let's combine our DataFrames so we can use then to train our model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656751405\", h=\"d1f95ab108\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.2.4: Concatenate DataFrames\n", "\n", "Use pd.concat to concatenate frame1 and frame2 into a new DataFrame df. Make sure you set the ignore_index argument to True.\n", "\n", "**Concatenate two or more DataFrames using pandas.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.concat([frame1, frame2], ignore_index=True)\n", "\n", "print(df.info())\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check your work\n", "assert df.shape == (2658, 17), f\"`df` is the wrong size: {df.shape}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Explore\n", "\n", "In the last lesson, we built a simple linear model that predicted apartment price based on one feature, \"surface_covered_in_m2\". In this lesson, we're building a multiple linear regression model that predicts price based on two features, \"lon\" and \"lat\". This means that our data visualizations now have to communicate three pieces of information: Longitude, latitude, and price. How can we represent these three attributes on a two-dimensional screen?\n", "\n", "One option is to incorporate color into our scatter plot. For example, in the Mapbox scatter plot below, the location of each point represents latitude and longitude, and color represents price."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VimeoVideo(\"656751031\", h=\"367be02e14\", width=600)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 2.2.5: Create Mapbox Scatter Plot\n", "\n", "Complete the code below to create a Mapbox scatter plot that shows the location of the apartments in df.\n", "\n", "**What's a scatter plot?**\n", "\n", "**Create a Mapbox scatter plot in plotly express.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = px.scatter_mapbox(\n", "    df,  # Our DataFrame\n", "    lat=\"lat\",\n", "    lon=\"lon\",\n", "    width=600,  # Width of map\n", "    height=600,  # Height of map\n", "    color=\"price_aprox_usd\",\n", "    hover_data=[\"price_aprox_usd\"],  # Display price when hovering mouse over house\n", ")\n", "\n", "fig.update_layout(mapbox_style=\"open-street-map\")\n", "\n", "fig.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}