\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 1.2: Data Preparation}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 1.2: Data Preparation and Cleaning} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Preparing Real-World Data for Analysis}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Welcome to Data Cleaning!}

Welcome to the world of data preparation! In the real world, data is messy - like finding your room after a tornado. Today we'll learn how to clean and organize Mexico real estate data so it's ready for analysis.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item Reading data from CSV files
    \item Inspecting data to understand what you have
    \item Cleaning messy data (fixing formats, handling missing values)
    \item Combining multiple datasets
    \item Creating new calculated columns
    \item Saving cleaned data for future use
\end{itemize}

\begin{concept}
\textbf{Why Clean Data?}
Real-world data is almost never ready to use immediately. It might have:
\begin{itemize}
    \item Missing values (empty cells)
    \item Wrong formats (\$1,000 vs 1000)
    \item Inconsistent naming (Mexico vs MEX)
    \item Data spread across multiple files
\end{itemize}
Data cleaning makes analysis possible and reliable!
\end{concept}

\section{Task 1.2.1: Reading CSV Files}

Let's start by reading our Mexico real estate data from CSV files.

\begin{lstlisting}[caption=Reading Multiple CSV Files]
import pandas as pd

# Read three different CSV files
df1 = pd.read_csv("data/mexico-real-estate-1.csv")
df2 = pd.read_csv("data/mexico-real-estate-2.csv") 
df3 = pd.read_csv("data/mexico-real-estate-3.csv")

print("Dataset 1 shape:", df1.shape)
print("Dataset 2 shape:", df2.shape)
print("Dataset 3 shape:", df3.shape)

# Look at what's in each dataset
print("\nDataset 1 columns:", list(df1.columns))
print("Dataset 2 columns:", list(df2.columns))
print("Dataset 3 columns:", list(df3.columns))
\end{lstlisting}

\begin{learningtip}
\textbf{Understanding CSV Files:}
\begin{itemize}
    \item CSV = Comma-Separated Values (like a simple Excel file)
    \item \texttt{pd.read\_csv()} automatically detects data types
    \item \texttt{shape} tells us (rows, columns)
    \item Always check column names first to understand your data
\end{itemize}
\end{learningtip}

\section{Task 1.2.2: Inspecting Your Data}

Before cleaning, we need to understand what we're working with.

\begin{lstlisting}[caption=Basic Data Inspection]
# Look at the first few rows of each dataset
print("Dataset 1 sample:")
print(df1.head())

print("\nDataset 2 sample:")
print(df2.head())

print("\nDataset 3 sample:")
print(df3.head())

# Check for missing values
print("\nMissing values in Dataset 1:")
print(df1.isnull().sum())

print("\nData types in Dataset 1:")
print(df1.dtypes)
\end{lstlisting}

\begin{concept}
\textbf{Key Inspection Questions:}
\begin{itemize}
    \item What does each column represent?
    \item Are there missing values (NaN)?
    \item Are data types correct (numbers vs text)?
    \item Do the data ranges make sense?
    \item Are there obvious errors or outliers?
\end{itemize}
\end{concept}

\section{Task 1.2.3: Cleaning Dataset 1 - Price Formatting}

Dataset 1 has prices in a messy format like "\$100,000.00". Let's clean this.

\begin{lstlisting}[caption=Cleaning Price Data]
# Look at the messy price column
print("Original price format:")
print(df1["price"].head())

# Clean the price column step by step
# Remove dollar signs, commas, and convert to numbers
df1["price_usd"] = (df1["price"]
                   .str.replace("$", "", regex=False)
                   .str.replace(",", "", regex=False)
                   .astype(float))

print("\nCleaned price format:")
print(df1["price_usd"].head())

# Verify the cleaning worked
print(f"\nPrice range: ${df1['price_usd'].min():,.0f} to ${df1['price_usd'].max():,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{String Cleaning Steps:}
\begin{itemize}
    \item \texttt{.str.replace()} removes unwanted characters
    \item \texttt{regex=False} treats text literally (not as patterns)
    \item \texttt{.astype(float)} converts text to numbers
    \item Always verify your cleaning worked correctly!
\end{itemize}
\end{learningtip}

\section{Task 1.2.4: Cleaning Dataset 2 - Currency Conversion}

Dataset 2 has prices in Mexican pesos. Let's convert them to USD.

\begin{lstlisting}[caption=Currency Conversion]
# Check the peso prices
print("Peso prices sample:")
print(df2["price_mxn"].head())

# Convert Mexican pesos to USD (using approximate exchange rate)
# Note: In real projects, you'd get current exchange rates
peso_to_usd_rate = 0.056  # Approximate rate

df2["price_usd"] = df2["price_mxn"] * peso_to_usd_rate

print("\nConverted to USD:")
print(df2["price_usd"].head())

print(f"\nPrice range: ${df2['price_usd'].min():,.0f} to ${df2['price_usd'].max():,.0f}")

# Round to reasonable precision
df2["price_usd"] = df2["price_usd"].round(2)
\end{lstlisting}

\begin{concept}
\textbf{Currency Conversion Tips:}
\begin{itemize}
    \item Always document which exchange rate you used
    \item Exchange rates change daily - note the date
    \item Round to reasonable precision (2 decimal places for money)
    \item Check if the converted values make sense
\end{itemize}
\end{concept}

\section{Task 1.2.5: Cleaning Dataset 3 - Location Data}

Dataset 3 has location information that needs to be split up.

\begin{lstlisting}[caption=Splitting Location Data]
# Look at the combined location column
print("Combined location data:")
print(df3["place_with_parent_names"].head())

# Split the location string into state and city
# Format is usually "City, State, Country"
location_parts = df3["place_with_parent_names"].str.split(", ", expand=True)

# Assign meaningful names to the split parts
df3["city"] = location_parts[0]
df3["state"] = location_parts[1] 
df3["country"] = location_parts[2]

print("\nSplit location data:")
print(df3[["city", "state", "country"]].head())

# Check what states we have
print("\nStates in the data:")
print(df3["state"].value_counts())
\end{lstlisting}

\begin{learningtip}
\textbf{Splitting Text Data:}
\begin{itemize}
    \item \texttt{.str.split()} breaks text at specified characters
    \item \texttt{expand=True} creates separate columns
    \item Always check the results - splitting doesn't always work perfectly
    \item Use \texttt{value\_counts()} to see what values you have
\end{itemize}
\end{learningtip}

\section{Task 1.2.6: Handling Missing Values}

Let's see what to do with missing data in our datasets.

\begin{lstlisting}[caption=Dealing with Missing Values]
# Check for missing values in latitude/longitude
print("Missing coordinates in Dataset 3:")
print(f"Missing latitude: {df3['lat'].isnull().sum()}")
print(f"Missing longitude: {df3['lon'].isnull().sum()}")

# Strategy 1: Remove rows with missing coordinates
df3_clean = df3.dropna(subset=['lat', 'lon'])
print(f"\nRows before cleaning: {len(df3)}")
print(f"Rows after cleaning: {len(df3_clean)}")

# Strategy 2: Fill missing area values with median
median_area = df1['area_m2'].median()
print(f"\nMedian area: {median_area} square meters")

df1['area_m2'] = df1['area_m2'].fillna(median_area)
print(f"Missing area values after filling: {df1['area_m2'].isnull().sum()}")
\end{lstlisting}

\begin{concept}
\textbf{Missing Value Strategies:}
\begin{itemize}
    \item \textbf{Drop:} Remove rows/columns with missing data
    \item \textbf{Fill:} Replace with mean, median, or most common value
    \item \textbf{Interpolate:} Estimate based on nearby values
    \item \textbf{Keep:} Sometimes missing values are meaningful
\end{itemize}
Choose based on how much data you'd lose and why values are missing.
\end{concept}

\section{Task 1.2.7: Standardizing Column Names}

Let's make all our datasets have consistent column names.

\begin{lstlisting}[caption=Standardizing Column Names]
# Check current column names
print("Dataset 1 columns:", df1.columns.tolist())
print("Dataset 2 columns:", df2.columns.tolist())
print("Dataset 3 columns:", df3_clean.columns.tolist())

# Rename columns to be consistent across datasets
df1 = df1.rename(columns={
    'area_m2': 'area_sqm',
    'price': 'price_original'
})

df2 = df2.rename(columns={
    'area_m2': 'area_sqm',
    'price_mxn': 'price_original'
})

df3_clean = df3_clean.rename(columns={
    'area_m2': 'area_sqm'
})

print("\nStandardized columns:")
print("All datasets now have 'area_sqm' and 'price_usd'")

# Keep only the columns we need for analysis
cols_to_keep = ['area_sqm', 'price_usd', 'state', 'lat', 'lon']
\end{lstlisting}

\begin{learningtip}
\textbf{Column Naming Best Practices:}
\begin{itemize}
    \item Use consistent naming across datasets
    \item Include units in names (sqm, usd, etc.)
    \item Use lowercase with underscores
    \item Make names descriptive but not too long
\end{itemize}
\end{learningtip}

\section{Task 1.2.8: Creating New Calculated Columns}

Let's add some useful calculated columns to our data.

\begin{lstlisting}[caption=Adding Calculated Columns]
# Add price per square meter for all datasets
df1['price_per_sqm'] = df1['price_usd'] / df1['area_sqm']
df2['price_per_sqm'] = df2['price_usd'] / df2['area_sqm']  
df3_clean['price_per_sqm'] = df3_clean['price_usd'] / df3_clean['area_sqm']

print("Price per square meter statistics:")
print(f"Dataset 1: ${df1['price_per_sqm'].mean():.2f} avg")
print(f"Dataset 2: ${df2['price_per_sqm'].mean():.2f} avg")
print(f"Dataset 3: ${df3_clean['price_per_sqm'].mean():.2f} avg")

# Categorize properties by size
def categorize_size(area):
    if area < 100:
        return "Small"
    elif area < 200:
        return "Medium" 
    else:
        return "Large"

df1['size_category'] = df1['area_sqm'].apply(categorize_size)
print("\nSize distribution in Dataset 1:")
print(df1['size_category'].value_counts())
\end{lstlisting}

\begin{concept}
\textbf{Creating Useful Features:}
\begin{itemize}
    \item Calculate ratios (price per unit area)
    \item Create categories from continuous variables
    \item Add flags for special conditions
    \item Derive time-based features (day, month, season)
\end{itemize}
New features often provide more insight than raw data alone.
\end{concept}

\section{Task 1.2.9: Combining Multiple Datasets}

Now let's combine our three cleaned datasets into one.

\begin{lstlisting}[caption=Combining Datasets]
# First, ensure all datasets have the same columns
common_columns = ['area_sqm', 'price_usd', 'price_per_sqm', 'lat', 'lon', 'state']

# Keep only common columns and add dataset identifier
df1_final = df1[common_columns].copy()
df1_final['dataset'] = 'dataset_1'

df2_final = df2[common_columns].copy()
df2_final['dataset'] = 'dataset_2'

df3_final = df3_clean[common_columns].copy()
df3_final['dataset'] = 'dataset_3'

# Combine all datasets
df_combined = pd.concat([df1_final, df2_final, df3_final], 
                       ignore_index=True)

print(f"Combined dataset shape: {df_combined.shape}")
print(f"Data from {df_combined['dataset'].nunique()} sources")
print("\nDataset distribution:")
print(df_combined['dataset'].value_counts())
\end{lstlisting}

\begin{learningtip}
\textbf{Combining Data Tips:}
\begin{itemize}
    \item Ensure consistent column names and types
    \item Add source identifiers to track data origin
    \item Use \texttt{ignore\_index=True} to get new row numbers
    \item Always verify the combination worked correctly
\end{itemize}
\end{learningtip}

\section{Task 1.2.10: Final Data Quality Checks}

Let's verify our cleaned data is ready for analysis.

\begin{lstlisting}[caption=Quality Assurance Checks]
# Check for any remaining missing values
print("Missing values in final dataset:")
print(df_combined.isnull().sum())

# Check data types
print("\nData types:")
print(df_combined.dtypes)

# Check for reasonable ranges
print("\nData ranges:")
print(f"Price: ${df_combined['price_usd'].min():,.0f} to ${df_combined['price_usd'].max():,.0f}")
print(f"Area: {df_combined['area_sqm'].min():.0f} to {df_combined['area_sqm'].max():.0f} sqm")
print(f"Price/sqm: ${df_combined['price_per_sqm'].min():.0f} to ${df_combined['price_per_sqm'].max():.0f}")

# Check for duplicate rows
duplicates = df_combined.duplicated().sum()
print(f"\nDuplicate rows: {duplicates}")

# Final summary
print(f"\nFinal dataset summary:")
print(f"- {len(df_combined):,} properties")
print(f"- {df_combined['state'].nunique()} different states")
print(f"- {len(df_combined.columns)} features per property")
\end{lstlisting}

\begin{concept}
\textbf{Data Quality Checklist:}
\begin{itemize}
    \item No missing values in critical columns
    \item Correct data types (numbers as numbers, dates as dates)
    \item Reasonable value ranges (no negative prices)
    \item No unexpected duplicates
    \item Consistent formatting across all data
\end{itemize}
\end{concept}

\section{Task 1.2.11: Saving Your Cleaned Data}

Finally, let's save our cleaned data for future use.

\begin{lstlisting}[caption=Saving Cleaned Data]
# Save the cleaned dataset
df_combined.to_csv("data/mexico-real-estate-clean.csv", index=False)
print("Saved cleaned data to 'mexico-real-estate-clean.csv'")

# Create a summary report
summary_stats = df_combined.describe()
print("\nSummary statistics:")
print(summary_stats)

# Save summary to file
with open("data/cleaning_report.txt", "w") as f:
    f.write("Mexico Real Estate Data Cleaning Report\n")
    f.write("=" * 50 + "\n\n")
    f.write(f"Total properties: {len(df_combined):,}\n")
    f.write(f"States covered: {df_combined['state'].nunique()}\n")
    f.write(f"Price range: ${df_combined['price_usd'].min():,.0f} - ${df_combined['price_usd'].max():,.0f}\n")
    f.write(f"Average price per sqm: ${df_combined['price_per_sqm'].mean():.2f}\n")

print("Saved cleaning report to 'cleaning_report.txt'")
\end{lstlisting}

\begin{learningtip}
\textbf{Saving Data Best Practices:}
\begin{itemize}
    \item Use \texttt{index=False} to avoid saving row numbers
    \item Save in a common format like CSV for compatibility
    \item Create documentation about what you did
    \item Include summary statistics for quick reference
\end{itemize}
\end{learningtip}

\section{Summary: What We Accomplished}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Data Import:} Reading multiple CSV files
    \item \textbf{Data Inspection:} Understanding structure and quality
    \item \textbf{String Cleaning:} Removing unwanted characters and formatting
    \item \textbf{Missing Values:} Strategies for handling incomplete data
    \item \textbf{Data Transformation:} Creating new calculated columns
    \item \textbf{Data Combination:} Merging multiple datasets
    \item \textbf{Quality Assurance:} Verifying data is analysis-ready
    \item \textbf{Data Export:} Saving cleaned data for future use
\end{enumerate}

\subsection{Key Concepts}
\begin{itemize}
    \item \textbf{Data Quality:} Clean data is essential for reliable analysis
    \item \textbf{Consistency:} Standardize formats across all datasets
    \item \textbf{Documentation:} Always record what cleaning steps you took
    \item \textbf{Validation:} Check that cleaning worked as expected
    \item \textbf{Reproducibility:} Save code and results for others to use
\end{itemize}

\subsection{Real-World Applications}
\begin{itemize}
    \item Preparing customer data for marketing analysis
    \item Cleaning sales data from multiple store locations
    \item Standardizing survey responses for research
    \item Consolidating financial data from different systems
    \item Preparing sensor data for machine learning models
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Read a CSV file and display basic information (shape, columns, first 5 rows)
    \item Find and count missing values in each column
    \item Clean one text column by removing extra spaces and converting to lowercase
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Create a function that standardizes different price formats in a dataset
    \item Handle missing values using different strategies (mean, median, mode)
    \item Export cleaned data to both CSV and Excel formats
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Build a complete data validation pipeline with error reporting
    \item Create a data quality dashboard showing before/after statistics
    \item Implement automatic outlier detection and handling
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Basic CSV file exploration}
\begin{lstlisting}[caption=CSV File Exploration]
import pandas as pd

# Read CSV file
df = pd.read_csv("data/real_estate_data.csv")

# Display basic information
print("DATASET OVERVIEW")
print("=" * 30)
print(f"Shape: {df.shape}")
print(f"Rows: {df.shape[0]:,}")
print(f"Columns: {df.shape[1]}")

print(f"\nColumn Names:")
for i, col in enumerate(df.columns, 1):
    print(f"  {i}. {col}")

print(f"\nData Types:")
print(df.dtypes)

print(f"\nFirst 5 Rows:")
print(df.head())

print(f"\nMemory Usage:")
print(f"Total: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
\end{lstlisting}

\textbf{Exercise 2: Missing values analysis}
\begin{lstlisting}[caption=Missing Values Analysis]
# Count missing values
missing_counts = df.isnull().sum()
missing_percentages = (df.isnull().sum() / len(df)) * 100

print("MISSING VALUES ANALYSIS")
print("=" * 35)

# Create summary table
missing_summary = pd.DataFrame({
    'Column': df.columns,
    'Missing_Count': missing_counts.values,
    'Missing_Percentage': missing_percentages.values
})

# Sort by missing percentage
missing_summary = missing_summary.sort_values('Missing_Percentage', ascending=False)

print("Missing Values by Column:")
for _, row in missing_summary.iterrows():
    if row['Missing_Count'] > 0:
        print(f"  {row['Column']}: {row['Missing_Count']} ({row['Missing_Percentage']:.1f}%)")

# Identify columns with high missing rates
high_missing = missing_summary[missing_summary['Missing_Percentage'] > 20]
if len(high_missing) > 0:
    print(f"\nColumns with >20% missing data:")
    for _, row in high_missing.iterrows():
        print(f"  - {row['Column']}: {row['Missing_Percentage']:.1f}%")

# Visualize missing data pattern
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
missing_data = missing_summary[missing_summary['Missing_Count'] > 0]
plt.bar(missing_data['Column'], missing_data['Missing_Percentage'])
plt.title('Missing Data Percentage by Column')
plt.ylabel('Missing Percentage (%)')
plt.xlabel('Columns')
plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 3: Text column cleaning}
\begin{lstlisting}[caption=Text Data Cleaning]
# Assume we have a 'location' or 'address' column to clean
# Let's create sample messy text data first
sample_locations = [
    " Mexico City ",
    "GUADALAJARA",
    " monterrey ",
    "Mexico City ",
    "  PUEBLA  ",
    "guadalajara",
    "MEXICO CITY"
]

# Create a test DataFrame
test_df = pd.DataFrame({'location': sample_locations})

print("BEFORE CLEANING:")
print(test_df['location'].tolist())

# Clean the text column
def clean_text_column(series):
    """
    Clean text data by removing spaces and standardizing case
    """
    # Remove leading/trailing spaces and convert to lowercase
    cleaned = series.str.strip().str.lower()
    
    # Remove extra internal spaces
    cleaned = cleaned.str.replace(r'\s+', ' ', regex=True)
    
    return cleaned

# Apply cleaning
test_df['location_clean'] = clean_text_column(test_df['location'])

print("\nAFTER CLEANING:")
print(test_df['location_clean'].tolist())

# Show before and after comparison
print("\nCLEANING COMPARISON:")
comparison = pd.DataFrame({
    'Original': test_df['location'],
    'Cleaned': test_df['location_clean']
})
print(comparison.to_string(index=False))

# Count unique values before and after
print(f"\nUnique values before cleaning: {test_df['location'].nunique()}")
print(f"Unique values after cleaning: {test_df['location_clean'].nunique()}")
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Price format standardization function}
\begin{lstlisting}[caption=Price Standardization Function]
import re

def standardize_prices(price_series):
    """
    Standardize different price formats to numeric values
    Handles: $1,000, 1000 USD, 1K, 1.5M, etc.
    """
    
    def clean_single_price(price_str):
        if pd.isna(price_str):
            return None
            
        # Convert to string and clean
        price_str = str(price_str).strip().upper()
        
        # Remove currency symbols and extra spaces
        price_str = re.sub(r'[$,USD\s]', '', price_str)
        
        # Handle abbreviations
        if 'K' in price_str:
            price_str = price_str.replace('K', '')
            multiplier = 1000
        elif 'M' in price_str:
            price_str = price_str.replace('M', '')
            multiplier = 1000000
        else:
            multiplier = 1
        
        try:
            # Convert to float and apply multiplier
            numeric_value = float(price_str) * multiplier
            return numeric_value
        except ValueError:
            return None
    
    # Apply cleaning to entire series
    return price_series.apply(clean_single_price)

# Test the function with sample data
sample_prices = [
    "$150,000",
    "200000",
    "175K",
    "$1.2M",
    "300,000 USD",
    "baddata",
    "500K",
    None
]

test_price_df = pd.DataFrame({'original_price': sample_prices})
test_price_df['standardized_price'] = standardize_prices(test_price_df['original_price'])

print("PRICE STANDARDIZATION RESULTS")
print("=" * 40)
print(test_price_df.to_string(index=False))

# Show statistics
valid_prices = test_price_df['standardized_price'].dropna()
print(f"\nPrice Statistics:")
print(f"  Valid prices: {len(valid_prices)}/{len(test_price_df)}")
print(f"  Average: ${valid_prices.mean():,.0f}")
print(f"  Range: ${valid_prices.min():,.0f} - ${valid_prices.max():,.0f}")
\end{lstlisting}

\textbf{Exercise 2: Missing value handling strategies}
\begin{lstlisting}[caption=Missing Value Handling Strategies]
import numpy as np

def handle_missing_values(df, strategy_map):
    """
    Handle missing values using different strategies for different columns
    
    strategy_map: dict mapping column names to strategies
    Strategies: 'mean', 'median', 'mode', 'drop', 'forward_fill', 'custom_value'
    """
    
    df_clean = df.copy()
    cleaning_report = {}
    
    for column, strategy in strategy_map.items():
        if column not in df_clean.columns:
            continue
            
        missing_count = df_clean[column].isnull().sum()
        if missing_count == 0:
            cleaning_report[column] = f"No missing values"
            continue
        
        if strategy == 'mean':
            fill_value = df_clean[column].mean()
            df_clean[column].fillna(fill_value, inplace=True)
            cleaning_report[column] = f"Filled {missing_count} missing values with mean: {fill_value:.2f}"
            
        elif strategy == 'median':
            fill_value = df_clean[column].median()
            df_clean[column].fillna(fill_value, inplace=True)
            cleaning_report[column] = f"Filled {missing_count} missing values with median: {fill_value:.2f}"
            
        elif strategy == 'mode':
            fill_value = df_clean[column].mode().iloc[0] if not df_clean[column].mode().empty else 'Unknown'
            df_clean[column].fillna(fill_value, inplace=True)
            cleaning_report[column] = f"Filled {missing_count} missing values with mode: {fill_value}"
            
        elif strategy == 'drop':
            initial_rows = len(df_clean)
            df_clean.dropna(subset=[column], inplace=True)
            dropped_rows = initial_rows - len(df_clean)
            cleaning_report[column] = f"Dropped {dropped_rows} rows with missing values"
            
        elif strategy == 'forward_fill':
            df_clean[column].fillna(method='ffill', inplace=True)
            remaining_na = df_clean[column].isnull().sum()
            cleaning_report[column] = f"Forward filled {missing_count - remaining_na} values, {remaining_na} still missing"
            
        elif isinstance(strategy, (int, float, str)):
            df_clean[column].fillna(strategy, inplace=True)
            cleaning_report[column] = f"Filled {missing_count} missing values with custom value: {strategy}"
    
    return df_clean, cleaning_report

# Create sample data with missing values
sample_data = {
    'price': [150000, np.nan, 200000, 180000, np.nan],
    'area': [100, 120, np.nan, 90, 110],
    'rooms': [3, np.nan, 4, 3, 2],
    'location': ['City A', 'City B', np.nan, 'City A', 'City C'],
    'year_built': [2020, 2019, 2021, np.nan, 2020]
}

sample_df = pd.DataFrame(sample_data)

print("ORIGINAL DATA WITH MISSING VALUES:")
print(sample_df)

# Define strategies for each column
strategies = {
    'price': 'median',
    'area': 'mean', 
    'rooms': 'mode',
    'location': 'Unknown',
    'year_built': 'forward_fill'
}

# Apply missing value handling
cleaned_df, report = handle_missing_values(sample_df, strategies)

print(f"\nCLEANED DATA:")
print(cleaned_df)

print(f"\nCLEANING REPORT:")
for column, message in report.items():
    print(f"  {column}: {message}")
\end{lstlisting}

\textbf{Exercise 3: Export to multiple formats}
\begin{lstlisting}[caption=Multi-Format Data Export]
def export_cleaned_data(df, base_filename, formats=['csv', 'excel'], include_timestamp=True):
    """
    Export cleaned DataFrame to multiple formats
    """
    from datetime import datetime
    import os
    
    # Add timestamp if requested
    if include_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = f"{base_filename}_{timestamp}"
    
    export_report = {}
    
    # Create exports directory if it doesn't exist
    if not os.path.exists('exports'):
        os.makedirs('exports')
    
    for format_type in formats:
        try:
            if format_type.lower() == 'csv':
                filepath = f"exports/{base_filename}.csv"
                df.to_csv(filepath, index=False)
                export_report['CSV'] = f"Successfully exported to {filepath}"
                
            elif format_type.lower() == 'excel':
                filepath = f"exports/{base_filename}.xlsx"
                df.to_excel(filepath, index=False, sheet_name='CleanedData')
                export_report['Excel'] = f"Successfully exported to {filepath}"
                
            elif format_type.lower() == 'json':
                filepath = f"exports/{base_filename}.json"
                df.to_json(filepath, orient='records', indent=2)
                export_report['JSON'] = f"Successfully exported to {filepath}"
                
            elif format_type.lower() == 'parquet':
                filepath = f"exports/{base_filename}.parquet"
                df.to_parquet(filepath, index=False)
                export_report['Parquet'] = f"Successfully exported to {filepath}"
                
        except Exception as e:
            export_report[format_type] = f"Failed to export: {str(e)}"
    
    return export_report

# Example usage with our cleaned data
export_formats = ['csv', 'excel', 'json']
export_results = export_cleaned_data(cleaned_df, 'mexico_real_estate_clean', export_formats)

print("EXPORT RESULTS:")
print("=" * 25)
for format_name, result in export_results.items():
    print(f"{format_name}: {result}")

# Also create a summary file with cleaning statistics
summary_stats = {
    'original_rows': len(sample_df),
    'cleaned_rows': len(cleaned_df),
    'original_columns': len(sample_df.columns),
    'cleaned_columns': len(cleaned_df.columns),
    'missing_values_before': sample_df.isnull().sum().sum(),
    'missing_values_after': cleaned_df.isnull().sum().sum()
}

summary_df = pd.DataFrame([summary_stats])
summary_export = export_cleaned_data(summary_df, 'cleaning_summary', ['csv'])

print(f"\nCleaning Summary:")
for key, value in summary_stats.items():
    print(f"  {key.replace('_', ' ').title()}: {value}")
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Complete data validation pipeline}
\begin{lstlisting}[caption=Data Validation Pipeline]
class DataValidator:
    """
    Comprehensive data validation pipeline with error reporting
    """
    
    def __init__(self):
        self.validation_errors = []
        self.validation_warnings = []
        self.validation_passed = []
    
    def validate_price_range(self, df, price_column, min_price=1000, max_price=10000000):
        """Validate price values are within reasonable range"""
        invalid_prices = df[(df[price_column] < min_price) | (df[price_column] > max_price)]
        
        if len(invalid_prices) > 0:
            self.validation_errors.append({
                'type': 'Price Range',
                'column': price_column,
                'count': len(invalid_prices),
                'message': f"{len(invalid_prices)} prices outside range ${min_price:,} - ${max_price:,}"
            })
        else:
            self.validation_passed.append(f"All {price_column} values within valid range")
    
    def validate_area_consistency(self, df, area_column, min_area=10, max_area=10000):
        """Validate area values are reasonable"""
        invalid_areas = df[(df[area_column] < min_area) | (df[area_column] > max_area)]
        
        if len(invalid_areas) > 0:
            self.validation_errors.append({
                'type': 'Area Range',
                'column': area_column,
                'count': len(invalid_areas),
                'message': f"{len(invalid_areas)} area values outside range {min_area} - {max_area} sqm"
            })
        else:
            self.validation_passed.append(f"All {area_column} values within valid range")
    
    def validate_missing_data_threshold(self, df, max_missing_pct=30):
        """Check if any column has excessive missing data"""
        missing_pct = (df.isnull().sum() / len(df)) * 100
        problematic_columns = missing_pct[missing_pct > max_missing_pct]
        
        if len(problematic_columns) > 0:
            for col, pct in problematic_columns.items():
                self.validation_warnings.append({
                    'type': 'High Missing Data',
                    'column': col,
                    'percentage': pct,
                    'message': f"{col} has {pct:.1f}% missing data (>{max_missing_pct}%)"
                })
        else:
            self.validation_passed.append("No columns with excessive missing data")
    
    def validate_duplicates(self, df, subset=None):
        """Check for duplicate records"""
        duplicates = df.duplicated(subset=subset)
        duplicate_count = duplicates.sum()
        
        if duplicate_count > 0:
            self.validation_warnings.append({
                'type': 'Duplicate Records',
                'count': duplicate_count,
                'message': f"{duplicate_count} duplicate records found"
            })
        else:
            self.validation_passed.append("No duplicate records found")
    
    def validate_price_per_area_consistency(self, df, price_col, area_col):
        """Check for unreasonable price per area ratios"""
        df['price_per_area'] = df[price_col] / df[area_col]
        
        # Define reasonable price per sqm range (adjust for your market)
        min_price_per_sqm = 100
        max_price_per_sqm = 5000
        
        invalid_ratios = df[(df['price_per_area'] < min_price_per_sqm) | 
                           (df['price_per_area'] > max_price_per_sqm)]
        
        if len(invalid_ratios) > 0:
            self.validation_warnings.append({
                'type': 'Price per Area',
                'count': len(invalid_ratios),
                'message': f"{len(invalid_ratios)} properties with unusual price/area ratio"
            })
        else:
            self.validation_passed.append("Price per area ratios appear reasonable")
    
    def run_validation(self, df, price_column='price', area_column='area'):
        """Run complete validation pipeline"""
        print("RUNNING DATA VALIDATION PIPELINE")
        print("=" * 45)
        
        # Clear previous results
        self.validation_errors = []
        self.validation_warnings = []
        self.validation_passed = []
        
        # Run all validations
        self.validate_price_range(df, price_column)
        self.validate_area_consistency(df, area_column)
        self.validate_missing_data_threshold(df)
        self.validate_duplicates(df)
        self.validate_price_per_area_consistency(df, price_column, area_column)
        
        # Generate report
        self.generate_validation_report()
        
        return len(self.validation_errors) == 0
    
    def generate_validation_report(self):
        """Generate comprehensive validation report"""
        print(f"\nVALIDATION RESULTS:")
        print(f"  Passed: {len(self.validation_passed)}")
        print(f"  Warnings: {len(self.validation_warnings)}")
        print(f"  Errors: {len(self.validation_errors)}")
        
        if self.validation_errors:
            print(f"\nERRORS (Must Fix):")
            for error in self.validation_errors:
                print(f"  - {error['message']}")
        
        if self.validation_warnings:
            print(f"\nWARNINGS (Review Recommended):")
            for warning in self.validation_warnings:
                print(f"  - {warning['message']}")
        
        if self.validation_passed:
            print(f"\nPASSED VALIDATIONS:")
            for passed in self.validation_passed:
                print(f"  - {passed}")

# Test the validation pipeline
# Create sample data with some issues
validation_test_data = {
    'price': [150000, 200000, 50, 25000000, 180000],  # One too low, one too high
    'area': [100, 120, 5, 90, 110],  # One too small
    'location': ['City A', 'City B', 'City A', 'City A', 'City C'],
}

test_df = pd.DataFrame(validation_test_data)

# Run validation
validator = DataValidator()
is_valid = validator.run_validation(test_df)

print(f"\nOverall validation result: {'PASSED' if is_valid else 'FAILED'}")
\end{lstlisting}

\section{Next Steps in Your Data Journey}

\textbf{Coming up in future projects:}
\begin{itemize}
    \item \textbf{Project 1.3:} Exploratory data analysis and visualization
    \item \textbf{Advanced cleaning:} Text processing, date handling, complex merging
    \item \textbf{Data validation:} Automated testing and error detection
    \item \textbf{Big data:} Working with datasets too large for memory
\end{itemize}

Congratulations! You now know how to take messy, real-world data and transform it into a clean, analysis-ready dataset. This skill is essential for any data science work and will serve you well throughout your career.

\section{Commentary on New Concepts and Learning Outcomes}

\subsection{Data Preparation Mastery in Project 1.2}

\begin{concept}
\textbf{Data Cleaning Excellence:}
This project introduced professional-grade data preparation techniques:

\begin{itemize}
    \item \textbf{Missing Data Strategies:} Multiple approaches for handling null values (drop, fill, interpolate)
    \item \textbf{Outlier Detection:} Statistical methods for identifying and handling anomalous values
    \item \textbf{Data Type Optimization:} Converting data to appropriate types for memory efficiency
    \item \textbf{String Standardization:} Cleaning and normalizing text data for consistency
    \item \textbf{Data Validation:} Creating systematic checks for data quality assurance
\end{itemize}
\end{concept}

\subsection{Advanced Technical Skills Acquired}

\begin{enumerate}
    \item \textbf{Pandas Proficiency:} Advanced DataFrame manipulation and transformation
    \begin{lstlisting}[caption=Professional Data Cleaning Pattern]
# Industry-standard cleaning pipeline
df_clean = (df
    .dropna(subset=['essential_columns'])
    .fillna({'optional_column': df['optional_column'].median()})
    .astype({'category_column': 'category'})
    .query('price > 0 and area > 0')
)
    \end{lstlisting}
    
    \item \textbf{Statistical Outlier Detection:} Using IQR and z-scores for data quality
    
    \item \textbf{Memory Optimization:} Efficient data types and storage strategies
    
    \item \textbf{Data Export/Import:} Working with multiple file formats (CSV, Excel, JSON, Parquet)
    
    \item \textbf{Validation Frameworks:} Building systematic data quality checks
\end{enumerate}

\subsection{Industry-Standard Practices Learned}

\begin{itemize}
    \item \textbf{Reproducible Pipelines:} Creating consistent, repeatable data processing workflows
    \item \textbf{Data Documentation:} Tracking transformations and maintaining data lineage
    \item \textbf{Quality Assurance:} Implementing automated validation and error detection
    \item \textbf{Performance Optimization:} Efficient processing of large datasets
    \item \textbf{Error Handling:} Graceful management of data processing failures
\end{itemize}

\subsection{Real-World Problem Solving}

The techniques learned address common data science challenges:
\begin{itemize}
    \item \textbf{Inconsistent Data Entry:} Standardizing formats and correcting errors
    \item \textbf{Missing Information:} Intelligent strategies for incomplete data
    \item \textbf{Scale Mismatches:} Handling data from different sources and scales
    \item \textbf{Quality Control:} Ensuring data meets analysis requirements
    \item \textbf{Performance Issues:} Optimizing for speed and memory usage
\end{itemize}

\subsection{Business Impact Understanding}

\begin{itemize}
    \item \textbf{Data Quality ROI:} Understanding how clean data improves analysis outcomes
    \item \textbf{Time Management:} Balancing thorough cleaning with project deadlines
    \item \textbf{Stakeholder Communication:} Explaining data limitations and quality issues
    \item \textbf{Risk Assessment:} Identifying potential problems in dirty data
    \item \textbf{Decision Support:} Ensuring data reliability for business decisions
\end{itemize}

\subsection{Professional Development}

\begin{itemize}
    \item \textbf{Systematic Thinking:} Developing methodical approaches to complex problems
    \item \textbf{Quality Mindset:} Understanding the importance of data integrity
    \item \textbf{Tool Mastery:} Becoming proficient with industry-standard libraries
    \item \textbf{Documentation Skills:} Creating clear records of data processing steps
    \item \textbf{Problem Diagnosis:} Identifying and resolving data quality issues
\end{itemize}

\subsection{Preparation for Advanced Analytics}

This data preparation foundation enables:
\begin{itemize}
    \item \textbf{Machine Learning:} Providing clean, consistent input for models
    \item \textbf{Statistical Analysis:} Ensuring valid assumptions for statistical tests
    \item \textbf{Data Visualization:} Creating accurate and meaningful charts
    \item \textbf{Business Intelligence:} Supporting reliable reporting and dashboards
    \item \textbf{Predictive Modeling:} Establishing quality data for forecasting
\end{itemize}

\subsection{Critical Success Factors}

Key insights from this project:
\begin{itemize}
    \item \textbf{80/20 Rule:} Data cleaning often takes 80\% of project time but enables 100\% of analysis value
    \item \textbf{Domain Knowledge:} Understanding business context improves cleaning decisions
    \item \textbf{Iterative Process:} Data cleaning is rarely a one-time activity
    \item \textbf{Balance:} Perfect data is impossible; good enough data enables insights
    \item \textbf{Documentation:} Recording decisions enables reproducibility and debugging
\end{itemize}

The data preparation skills you've mastered in this project are among the most valuable in data science. Clean, well-structured data is the foundation that makes sophisticated analysis possible. Industry surveys consistently show that data scientists spend 60-80\% of their time on data preparation, making these skills essential for career success.

\end{document} 