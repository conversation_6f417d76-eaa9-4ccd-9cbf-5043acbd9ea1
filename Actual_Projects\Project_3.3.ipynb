import warnings

import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
from IPython.display import VimeoVideo
from pymongo import MongoClient
from sklearn.metrics import mean_absolute_error
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.tsa.ar_model import AutoReg

warnings.simplefilter(action="ignore", category=FutureWarning)

VimeoVideo("665851858", h="e39fc3d260", width=600)

VimeoVideo("665851852", h="16aa0a56e6", width=600)

client = MongoClient(host="localhost", port=27017)
db = client["air-quality"]
nairobi = db["nairobi"]

VimeoVideo("665851840", h="e048425f49", width=600)

def wrangle(collection):
    results = collection.find(
        {"metadata.site": 29, "metadata.measurement": "P2"},
        projection={"P2": 1, "timestamp": 1, "_id": 0},
    )

    # Read data into DataFrame
    df = pd.DataFrame(list(results)).set_index("timestamp")

    # Localize timezone
    df.index = df.index.tz_localize("UTC").tz_convert("Africa/Nairobi")

    # Remove outliers
    df = df[df["P2"] < 500]

    # Resample to 1hr window
    y = df["P2"].resample("1H").mean().fillna(method='ffill')

    return y

y = wrangle(nairobi)
y.head()

# Check your work
assert isinstance(y, pd.Series), f"`y` should be a Series, not type {type(y)}"
assert len(y) == 2928, f"`y` should have 2928 observations, not {len(y)}"
assert y.isnull().sum() == 0

VimeoVideo("665851830", h="85f58bc92b", width=600)

fig, ax = plt.subplots(figsize=(15, 6))
plot_acf(y, ax=ax)
plt.xlabel("Lag [hours]")
plt.ylabel("Correlation Coefficient")
plt.show()

VimeoVideo("665851811", h="ee3a2b5c24", width=600)

fig, ax = plt.subplots(figsize=(15, 6))
plot_pacf(y, ax=ax)
plt.xlabel("Lag [hours]")
plt.ylabel("Correlation Coefficient")
plt.show()

VimeoVideo("665851798", h="6c191cd94c", width=600)

cutoff_test = int(len(y) * 0.95)

y_train = y.iloc[:cutoff_test]
y_test = y.iloc[cutoff_test:]

y_train_mean = y_train.mean()
y_pred_baseline = [y_train_mean] * len(y_train)
mae_baseline = mean_absolute_error(y_train, y_pred_baseline)

print("Mean P2 Reading:", round(y_train_mean, 2))
print("Baseline MAE:", round(mae_baseline, 2))

VimeoVideo("665851769", h="94a4296cde", width=600)

model = AutoReg(y_train, lags=26).fit()

VimeoVideo("665851746", h="1a4511e883", width=600)

y_pred = model.fittedvalues
training_mae = mean_absolute_error(y_train[26:], y_pred)
print("Training MAE:", training_mae)

VimeoVideo("665851744", h="60d053b455", width=600)

y_train_resid = y_train[26:] - y_pred
y_train_resid.tail()

VimeoVideo("665851712", h="9ff0cdba9c", width=600)

fig, ax = plt.subplots(figsize=(15, 6))
y_train_resid.plot(ax=ax)
ax.set_title("Residuals")
ax.set_xlabel("Date")
ax.set_ylabel("Residual Value")
plt.show()

VimeoVideo("665851702", h="b494adc297", width=600)

fig = px.histogram(y_train_resid, title="Histogram of Residuals")
fig.show()

VimeoVideo("665851684", h="d6d782a1f3", width=600)

fig, ax = plt.subplots(figsize=(15, 6))
plot_acf(y_train_resid, ax=ax)
plt.show()

VimeoVideo("665851662", h="72e767e121", width=600)

y_pred_test = model.forecast(steps=len(y_test))
test_mae = mean_absolute_error(y_test, y_pred_test)
print("Test MAE:", test_mae)

df_pred_test = pd.DataFrame(
    {"y_test": y_test, "y_pred": y_pred_test}, index=y_test.index
)

VimeoVideo("665851628", h="29b43e482e", width=600)

fig = px.line(df_pred_test, labels={"value": "P2"})
fig.show()

VimeoVideo("665851599", h="bb30d96e43", width=600)

%%capture

y_pred_wfv = pd.Series(dtype=float, index=y_test.index)
history = y_train.copy()
for i in range(len(y_test)):
    model_wfv = AutoReg(history, lags=26).fit()
    next_pred = model_wfv.forecast(steps=1)
    y_pred_wfv.iloc[i] = next_pred
    history = pd.concat([history, y_test.iloc[i:i+1]])

VimeoVideo("665851568", h="a764ab5416", width=600)

test_mae = mean_absolute_error(y_test, y_pred_wfv)
print("Test MAE (walk forward validation):", round(test_mae, 2))

VimeoVideo("665851553", h="46338036cc", width=600)

print(model.params)

VimeoVideo("665851529", h="39284d37ac", width=600)

df_pred_test = pd.DataFrame(
    {"y_test": y_test, "y_pred": y_pred_wfv}, index=y_test.index
)
fig = px.line(df_pred_test, labels={"value": "P2"})
fig.show()