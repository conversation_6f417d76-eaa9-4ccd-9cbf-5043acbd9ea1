\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}
\usepackage{array}
\usepackage{longtable}
\usepackage{tcolorbox}

% Code listing style - NO LINE NUMBERS
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Learning tip box style
\newtcolorbox{learningtip}{
    colback=blue!5!white,
    colframe=blue!75!black,
    title=Learning Tip,
    fonttitle=\bfseries
}

% Concept box style
\newtcolorbox{concept}{
    colback=green!5!white,
    colframe=green!75!black,
    title=Key Concept,
    fonttitle=\bfseries
}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{Project 1.3: Exploratory Data Analysis}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Fix header height warning
\setlength{\headheight}{15pt}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}
\titleformat{\subsubsection}{\normalsize\bfseries\color{blue!40!black}}{\thesubsubsection}{1em}{}

\title{\textbf{Project 1.3: Exploratory Data Analysis} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Discovering Insights Through Data Visualization}
\author{Student-Friendly Guide}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction: Welcome to Data Detective Work!}

Welcome to exploratory data analysis (EDA) - the detective work of data science! Now that we have clean data, it's time to discover what stories it tells. We'll explore Mexico real estate data using visualizations and statistics to understand patterns and relationships.

\subsection{What You'll Learn Today}
\begin{itemize}
    \item Creating meaningful visualizations with matplotlib and seaborn
    \item Understanding distributions, outliers, and patterns
    \item Analyzing relationships between variables
    \item Asking the right questions about your data
    \item Drawing insights from visual and statistical evidence
    \item Creating a professional data exploration report
\end{itemize}

\begin{concept}
\textbf{What is Exploratory Data Analysis?}
EDA is like being a data detective:
\begin{itemize}
    \item \textbf{Investigate:} Look for patterns, trends, and anomalies
    \item \textbf{Question:} Ask "what," "where," "when," and "why"
    \item \textbf{Visualize:} Create charts that reveal hidden insights
    \item \textbf{Summarize:} Understand the story your data tells
\end{itemize}
\end{concept}

\section{Task 1.3.1: Setting Up for Analysis}

Let's load our cleaned data and visualization libraries.

\begin{lstlisting}[caption=Setting Up Your Analysis Environment]
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")

# Load our cleaned data
df = pd.read_csv("data/mexico-real-estate-clean.csv")

print(f"Dataset shape: {df.shape}")
print(f"Columns: {list(df.columns)}")

# First look at the data
print("\nFirst 5 rows:")
print(df.head())

# Basic info about our dataset
print(f"\nDataset summary:")
print(f"- {len(df):,} properties")
print(f"- {df['state'].nunique()} states")
print(f"- Price range: ${df['price_usd'].min():,.0f} to ${df['price_usd'].max():,.0f}")
\end{lstlisting}

\begin{learningtip}
\textbf{EDA Setup Tips:}
\begin{itemize}
    \item \texttt{matplotlib} and \texttt{seaborn} are the main plotting libraries
    \item Set consistent styles for professional-looking plots
    \item Always start with basic summary information
    \item Keep your questions in mind as you explore
\end{itemize}
\end{learningtip}

\section{Task 1.3.2: Understanding Price Distributions}

Let's start by understanding how property prices are distributed.

\begin{lstlisting}[caption=Exploring Price Distributions]
# Basic statistics for price
print("Price statistics:")
print(df['price_usd'].describe())

# Create a histogram of prices
plt.figure(figsize=(12, 5))

# Subplot 1: Regular histogram
plt.subplot(1, 2, 1)
plt.hist(df['price_usd'], bins=50, edgecolor='black', alpha=0.7)
plt.title('Distribution of Property Prices')
plt.xlabel('Price (USD)')
plt.ylabel('Number of Properties')
plt.ticklabel_format(style='plain', axis='x')

# Subplot 2: Log scale to see details
plt.subplot(1, 2, 2)
plt.hist(df['price_usd'], bins=50, edgecolor='black', alpha=0.7)
plt.title('Distribution of Property Prices (Log Scale)')
plt.xlabel('Price (USD)')
plt.ylabel('Number of Properties')
plt.yscale('log')
plt.ticklabel_format(style='plain', axis='x')

plt.tight_layout()
plt.show()

# Calculate some percentiles
percentiles = [25, 50, 75, 90, 95, 99]
for p in percentiles:
    value = df['price_usd'].quantile(p/100)
    print(f"{p}th percentile: ${value:,.0f}")
\end{lstlisting}

\begin{concept}
\textbf{Understanding Distributions:}
\begin{itemize}
    \item \textbf{Shape:} Is it normal, skewed, or has multiple peaks?
    \item \textbf{Center:} Where is the typical value (median)?
    \item \textbf{Spread:} How much variation is there?
    \item \textbf{Outliers:} Are there unusually high/low values?
\end{itemize}
Real estate prices are often right-skewed (few very expensive properties).
\end{concept}

\section{Task 1.3.3: Geographic Analysis}

Let's explore how property prices vary by location.

\begin{lstlisting}[caption=Geographic Price Analysis]
# Prices by state
plt.figure(figsize=(12, 8))

# Box plot of prices by state
plt.subplot(2, 1, 1)
sns.boxplot(data=df, x='state', y='price_usd')
plt.title('Property Prices by State')
plt.xlabel('State')
plt.ylabel('Price (USD)')
plt.xticks(rotation=45)

# Average price by state (bar chart)
plt.subplot(2, 1, 2)
state_avg_price = df.groupby('state')['price_usd'].mean().sort_values(ascending=False)
plt.bar(range(len(state_avg_price)), state_avg_price.values)
plt.title('Average Property Price by State')
plt.xlabel('State')
plt.ylabel('Average Price (USD)')
plt.xticks(range(len(state_avg_price)), state_avg_price.index, rotation=45)

plt.tight_layout()
plt.show()

# Summary statistics by state
print("Price summary by state:")
state_summary = df.groupby('state')['price_usd'].agg(['count', 'mean', 'median', 'std'])
print(state_summary.round(0))
\end{lstlisting}

\begin{learningtip}
\textbf{Geographic Analysis Tips:}
\begin{itemize}
    \item Box plots show distribution shape and outliers for each group
    \item Bar charts make it easy to compare averages across groups
    \item Always check sample sizes - some states might have very few properties
    \item Consider both mean and median - they tell different stories
\end{itemize}
\end{learningtip}

\section{Task 1.3.4: Size vs Price Relationship}

Let's investigate how property size relates to price.

\begin{lstlisting}[caption=Analyzing Size-Price Relationships]
# Scatter plot of area vs price
plt.figure(figsize=(12, 5))

# Regular scale
plt.subplot(1, 2, 1)
plt.scatter(df['area_sqm'], df['price_usd'], alpha=0.6)
plt.title('Property Size vs Price')
plt.xlabel('Area (square meters)')
plt.ylabel('Price (USD)')

# Log-log scale to see patterns better
plt.subplot(1, 2, 2)
plt.scatter(df['area_sqm'], df['price_usd'], alpha=0.6)
plt.title('Property Size vs Price (Log Scale)')
plt.xlabel('Area (square meters)')
plt.ylabel('Price (USD)')
plt.xscale('log')
plt.yscale('log')

plt.tight_layout()
plt.show()

# Calculate correlation
correlation = df['area_sqm'].corr(df['price_usd'])
print(f"Correlation between area and price: {correlation:.3f}")

# Price per square meter analysis
plt.figure(figsize=(10, 6))
plt.hist(df['price_per_sqm'], bins=50, edgecolor='black', alpha=0.7)
plt.title('Distribution of Price per Square Meter')
plt.xlabel('Price per Square Meter (USD)')
plt.ylabel('Number of Properties')
plt.show()

print(f"Price per sqm statistics:")
print(df['price_per_sqm'].describe())
\end{lstlisting}

\begin{concept}
\textbf{Correlation vs Causation:}
\begin{itemize}
    \item \textbf{Correlation:} Variables move together (0 to 1 scale)
    \item \textbf{Strong:} > 0.7, \textbf{Moderate:} 0.3-0.7, \textbf{Weak:} < 0.3
    \item \textbf{Causation:} One variable causes changes in another
    \item Correlation doesn't prove causation - bigger houses cost more, but size doesn't "cause" high prices (location, quality matter too)
\end{itemize}
\end{concept}

\section{Task 1.3.5: Finding and Understanding Outliers}

Let's identify unusual properties that might need special attention.

\begin{lstlisting}[caption=Outlier Detection and Analysis]
# Define outliers using the IQR method
Q1 = df['price_usd'].quantile(0.25)
Q3 = df['price_usd'].quantile(0.75)
IQR = Q3 - Q1

# Calculate outlier boundaries
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

# Find outliers
outliers = df[(df['price_usd'] < lower_bound) | (df['price_usd'] > upper_bound)]

print(f"Total properties: {len(df)}")
print(f"Outliers found: {len(outliers)} ({len(outliers)/len(df)*100:.1f}%)")
print(f"Outlier price range: ${outliers['price_usd'].min():,.0f} to ${outliers['price_usd'].max():,.0f}")

# Look at the most expensive properties
expensive_properties = df.nlargest(10, 'price_usd')
print("\nTop 10 most expensive properties:")
print(expensive_properties[['price_usd', 'area_sqm', 'price_per_sqm', 'state']])

# Look at unusually cheap properties
cheap_properties = df.nsmallest(10, 'price_usd')
print("\nTop 10 cheapest properties:")
print(cheap_properties[['price_usd', 'area_sqm', 'price_per_sqm', 'state']])

# Visualize outliers
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.boxplot(df['price_usd'])
plt.title('Price Distribution with Outliers')
plt.ylabel('Price (USD)')

plt.subplot(1, 2, 2)
plt.boxplot(df['price_per_sqm'])
plt.title('Price per SqM with Outliers')
plt.ylabel('Price per Square Meter (USD)')

plt.tight_layout()
plt.show()
\end{lstlisting}

\begin{learningtip}
\textbf{Dealing with Outliers:}
\begin{itemize}
    \item \textbf{Investigate:} Are they data errors or genuinely unusual properties?
    \item \textbf{Context matters:} A mansion vs data entry mistake
    \item \textbf{IQR method:} Values beyond Q1-1.5*IQR or Q3+1.5*IQR
    \item \textbf{Don't automatically remove:} Outliers might be your most interesting findings!
\end{itemize}
\end{learningtip}

\section{Task 1.3.6: Creating a Correlation Matrix}

Let's see how all our numeric variables relate to each other.

\begin{lstlisting}[caption=Correlation Analysis]
# Select numeric columns for correlation analysis
numeric_cols = ['price_usd', 'area_sqm', 'price_per_sqm', 'lat', 'lon']
correlation_matrix = df[numeric_cols].corr()

# Create a heatmap
plt.figure(figsize=(10, 8))
sns.heatmap(correlation_matrix, 
            annot=True,  # Show correlation values
            cmap='coolwarm',  # Color scheme
            center=0,  # Center colormap at 0
            square=True,  # Make cells square
            fmt='.3f')  # Format numbers to 3 decimal places

plt.title('Correlation Matrix of Property Features')
plt.tight_layout()
plt.show()

# Interpret the strongest correlations
print("Strongest positive correlations:")
# Find pairs with correlation > 0.5
strong_corr = []
for i in range(len(correlation_matrix.columns)):
    for j in range(i+1, len(correlation_matrix.columns)):
        corr_val = correlation_matrix.iloc[i, j]
        if abs(corr_val) > 0.5:
            var1 = correlation_matrix.columns[i]
            var2 = correlation_matrix.columns[j]
            strong_corr.append((var1, var2, corr_val))

for var1, var2, corr in sorted(strong_corr, key=lambda x: abs(x[2]), reverse=True):
    print(f"{var1} - {var2}: {corr:.3f}")
\end{lstlisting}

\begin{concept}
\textbf{Reading Correlation Matrices:}
\begin{itemize}
    \item \textbf{Diagonal:} Always 1.0 (variable correlates perfectly with itself)
    \item \textbf{Color intensity:} Darker colors = stronger correlations
    \item \textbf{Red:} Positive correlation (both increase together)
    \item \textbf{Blue:} Negative correlation (one increases, other decreases)
    \item \textbf{White/light:} Little to no correlation
\end{itemize}
\end{concept}

\section{Task 1.3.7: Advanced Visualizations}

Let's create more sophisticated plots to understand our data better.

\begin{lstlisting}[caption=Advanced Data Visualizations]
# Pairplot to see all relationships at once
plt.figure(figsize=(12, 10))
numeric_data = df[['price_usd', 'area_sqm', 'price_per_sqm']].sample(1000)  # Sample for speed
sns.pairplot(numeric_data, diag_kind='hist')
plt.suptitle('Pairwise Relationships Between Key Variables', y=1.02)
plt.show()

# Violin plot showing price distribution by state
plt.figure(figsize=(14, 6))
sns.violinplot(data=df, x='state', y='price_usd')
plt.title('Price Distribution by State (Violin Plot)')
plt.xlabel('State')
plt.ylabel('Price (USD)')
plt.xticks(rotation=45)
plt.show()

# Geographic scatter plot (if we have coordinates)
if 'lat' in df.columns and 'lon' in df.columns:
    plt.figure(figsize=(12, 8))
    scatter = plt.scatter(df['lon'], df['lat'], 
                         c=df['price_usd'], 
                         cmap='viridis', 
                         alpha=0.6,
                         s=20)
    plt.colorbar(scatter, label='Price (USD)')
    plt.title('Geographic Distribution of Properties Colored by Price')
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    plt.show()
\end{lstlisting}

\begin{learningtip}
\textbf{Advanced Plot Types:}
\begin{itemize}
    \item \textbf{Pairplot:} Shows all variable relationships in one view
    \item \textbf{Violin plot:} Like box plot but shows full distribution shape
    \item \textbf{Geographic scatter:} Reveals spatial patterns in your data
    \item \textbf{Color coding:} Use color to add an extra dimension to plots
\end{itemize}
\end{learningtip}

\section{Task 1.3.8: Statistical Hypothesis Testing}

Let's test some hypotheses about our data using statistics.

\begin{lstlisting}[caption=Basic Statistical Testing]
from scipy import stats

# Question: Are coastal states more expensive than inland states?
# (This is a simplified example - you'd need to define coastal vs inland)

# Compare two specific states
state1 = 'Distrito Federal'  # Replace with actual state names from your data
state2 = 'Morelos'

if state1 in df['state'].values and state2 in df['state'].values:
    prices_state1 = df[df['state'] == state1]['price_usd']
    prices_state2 = df[df['state'] == state2]['price_usd']
    
    # Perform t-test
    t_stat, p_value = stats.ttest_ind(prices_state1, prices_state2)
    
    print(f"Comparing {state1} vs {state2}:")
    print(f"{state1} average: ${prices_state1.mean():,.0f}")
    print(f"{state2} average: ${prices_state2.mean():,.0f}")
    print(f"T-statistic: {t_stat:.3f}")
    print(f"P-value: {p_value:.3f}")
    
    if p_value < 0.05:
        print("Statistically significant difference!")
    else:
        print("No statistically significant difference.")

# Test for normality of price distribution
shapiro_stat, shapiro_p = stats.shapiro(df['price_usd'].sample(5000))  # Sample for speed
print(f"\nNormality test (Shapiro-Wilk):")
print(f"Statistic: {shapiro_stat:.3f}, P-value: {shapiro_p:.3f}")

if shapiro_p < 0.05:
    print("Prices are NOT normally distributed")
else:
    print("Prices appear normally distributed")
\end{lstlisting}

\begin{concept}
\textbf{Statistical Testing Basics:}
\begin{itemize}
    \item \textbf{Hypothesis:} A testable statement about your data
    \item \textbf{P-value:} Probability your result occurred by chance
    \item \textbf{Significance:} If p < 0.05, result is "statistically significant"
    \item \textbf{T-test:} Compares averages between two groups
    \item \textbf{Normality test:} Checks if data follows a normal distribution
\end{itemize}
\end{concept}

\section{Task 1.3.9: Creating a Data Story}

Let's summarize our findings into a coherent data story.

\begin{lstlisting}[caption=Summarizing Key Insights]
# Calculate key summary statistics
total_properties = len(df)
avg_price = df['price_usd'].mean()
median_price = df['price_usd'].median()
avg_size = df['area_sqm'].mean()
avg_price_per_sqm = df['price_per_sqm'].mean()

# Most and least expensive states
most_expensive_state = df.groupby('state')['price_usd'].mean().idxmax()
least_expensive_state = df.groupby('state')['price_usd'].mean().idxmin()

most_expensive_avg = df.groupby('state')['price_usd'].mean().max()
least_expensive_avg = df.groupby('state')['price_usd'].mean().min()

print("MEXICO REAL ESTATE MARKET INSIGHTS")
print("=" * 50)
print(f"\nDataset Overview:")
print(f"- Total properties analyzed: {total_properties:,}")
print(f"- Geographic coverage: {df['state'].nunique()} states")
print(f"- Price range: ${df['price_usd'].min():,.0f} - ${df['price_usd'].max():,.0f}")

print(f"\nMarket Summary:")
print(f"- Average property price: ${avg_price:,.0f}")
print(f"- Median property price: ${median_price:,.0f}")
print(f"- Average property size: {avg_size:.0f} sqm")
print(f"- Average price per sqm: ${avg_price_per_sqm:.0f}")

print(f"\nRegional Variations:")
print(f"- Most expensive state: {most_expensive_state} (${most_expensive_avg:,.0f} avg)")
print(f"- Least expensive state: {least_expensive_state} (${least_expensive_avg:,.0f} avg)")
print(f"- Price difference: {most_expensive_avg/least_expensive_avg:.1f}x")

# Size categories
small_properties = len(df[df['area_sqm'] < 100])
medium_properties = len(df[(df['area_sqm'] >= 100) & (df['area_sqm'] < 200)])
large_properties = len(df[df['area_sqm'] >= 200])

print(f"\nProperty Size Distribution:")
print(f"- Small (<100 sqm): {small_properties:,} ({small_properties/total_properties*100:.1f}%)")
print(f"- Medium (100-200 sqm): {medium_properties:,} ({medium_properties/total_properties*100:.1f}%)")
print(f"- Large (>200 sqm): {large_properties:,} ({large_properties/total_properties*100:.1f}%)")
\end{lstlisting}

\section{Task 1.3.10: Creating a Professional Report}

Let's create visualizations for a professional data analysis report.

\begin{lstlisting}[caption=Professional Report Visualizations]
# Create a comprehensive figure with multiple subplots
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Mexico Real Estate Market Analysis Report', fontsize=16, fontweight='bold')

# 1. Price distribution
axes[0, 0].hist(df['price_usd'], bins=30, edgecolor='black', alpha=0.7)
axes[0, 0].set_title('Price Distribution')
axes[0, 0].set_xlabel('Price (USD)')
axes[0, 0].set_ylabel('Frequency')

# 2. Size vs Price scatter
axes[0, 1].scatter(df['area_sqm'], df['price_usd'], alpha=0.5)
axes[0, 1].set_title('Size vs Price Relationship')
axes[0, 1].set_xlabel('Area (sqm)')
axes[0, 1].set_ylabel('Price (USD)')

# 3. Price by state (top 10 states by count)
top_states = df['state'].value_counts().head(10).index
state_data = df[df['state'].isin(top_states)]
axes[0, 2].boxplot([state_data[state_data['state'] == state]['price_usd'] 
                   for state in top_states])
axes[0, 2].set_title('Price by State (Top 10)')
axes[0, 2].set_xlabel('State')
axes[0, 2].set_ylabel('Price (USD)')
axes[0, 2].set_xticklabels(top_states, rotation=45)

# 4. Price per sqm distribution
axes[1, 0].hist(df['price_per_sqm'], bins=30, edgecolor='black', alpha=0.7)
axes[1, 0].set_title('Price per SqM Distribution')
axes[1, 0].set_xlabel('Price per SqM (USD)')
axes[1, 0].set_ylabel('Frequency')

# 5. Geographic distribution (if coordinates available)
if 'lat' in df.columns and 'lon' in df.columns:
    scatter = axes[1, 1].scatter(df['lon'], df['lat'], c=df['price_usd'], 
                                cmap='viridis', alpha=0.6, s=10)
    axes[1, 1].set_title('Geographic Price Distribution')
    axes[1, 1].set_xlabel('Longitude')
    axes[1, 1].set_ylabel('Latitude')

# 6. Summary statistics table (as text)
summary_text = f"""
Key Statistics:
- Total Properties: {len(df):,}
- Avg Price: ${df['price_usd'].mean():,.0f}
- Median Price: ${df['price_usd'].median():,.0f}
- Avg Size: {df['area_sqm'].mean():.0f} sqm
- States Covered: {df['state'].nunique()}
- Price Range: ${df['price_usd'].min():,.0f} - ${df['price_usd'].max():,.0f}
"""
axes[1, 2].text(0.1, 0.5, summary_text, fontsize=10, 
                verticalalignment='center', transform=axes[1, 2].transAxes)
axes[1, 2].set_title('Market Summary')
axes[1, 2].axis('off')

plt.tight_layout()
plt.show()

# Save the report
plt.savefig('mexico_real_estate_analysis_report.png', dpi=300, bbox_inches='tight')
print("Report saved as 'mexico_real_estate_analysis_report.png'")
\end{lstlisting}

\begin{learningtip}
\textbf{Professional Report Tips:}
\begin{itemize}
    \item Use consistent styling across all plots
    \item Include clear titles and axis labels
    \item Provide context with summary statistics
    \item Save high-resolution images for presentations
    \item Tell a story - connect your visualizations to business insights
\end{itemize}
\end{learningtip}

\section{Summary: What We Accomplished}

\subsection{Technical Skills You Learned}
\begin{enumerate}
    \item \textbf{Data Visualization:} Creating histograms, scatter plots, box plots, and heatmaps
    \item \textbf{Statistical Analysis:} Understanding distributions, correlations, and outliers
    \item \textbf{Geographic Analysis:} Mapping data to understand spatial patterns
    \item \textbf{Hypothesis Testing:} Using statistics to test assumptions about your data
    \item \textbf{Data Storytelling:} Converting analysis into actionable insights
    \item \textbf{Professional Reporting:} Creating publication-ready visualizations
\end{enumerate}

\subsection{Key Concepts}
\begin{itemize}
    \item \textbf{Distribution Shape:} Normal, skewed, bimodal patterns tell different stories
    \item \textbf{Correlation:} Measuring how variables move together
    \item \textbf{Outliers:} Unusual values that might be errors or interesting cases
    \item \textbf{Statistical Significance:} Using p-values to test hypotheses
    \item \textbf{Visual Communication:} Charts convey insights faster than tables
\end{itemize}

\subsection{Real-World Applications}
\begin{itemize}
    \item Market research and competitive analysis
    \item Customer behavior analysis for marketing
    \item Quality control in manufacturing
    \item Financial risk assessment
    \item Healthcare outcome analysis
    \item Social science research
\end{itemize}

\section{Practice Exercises}

\textbf{Easy:}
\begin{enumerate}
    \item Create a histogram for property prices and describe the distribution
    \item Calculate and compare the median price for properties with and without balconies
    \item Create a simple bar chart showing the count of properties by state
\end{enumerate}

\textbf{Medium:}
\begin{enumerate}
    \item Perform correlation analysis between all numeric variables
    \item Create a multi-panel plot showing price distributions by different property types
    \item Investigate whether property age affects pricing using scatter plots and correlation
\end{enumerate}

\textbf{Challenging:}
\begin{enumerate}
    \item Create an interactive dashboard showing property data exploration
    \item Perform statistical hypothesis testing to compare prices between regions
    \item Build a comprehensive EDA report with visualizations and insights
\end{enumerate}

\section{Sample Solutions to Practice Exercises}

\subsection{Easy Exercise Solutions}

\textbf{Exercise 1: Property price histogram and distribution analysis}
\begin{lstlisting}[caption=Price Distribution Histogram]
# Create histogram for property prices
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.hist(df['price_usd'], bins=30, color='skyblue', edgecolor='black', alpha=0.7)
plt.title('Distribution of Property Prices')
plt.xlabel('Price (USD)')
plt.ylabel('Frequency')
plt.grid(axis='y', alpha=0.3)

# Add summary statistics
mean_price = df['price_usd'].mean()
median_price = df['price_usd'].median()
plt.axvline(mean_price, color='red', linestyle='--', label=f'Mean: ${mean_price:,.0f}')
plt.axvline(median_price, color='orange', linestyle='--', label=f'Median: ${median_price:,.0f}')
plt.legend()

# Log scale version for better visualization of skewed data
plt.subplot(1, 2, 2)
plt.hist(df['price_usd'], bins=30, color='lightgreen', edgecolor='black', alpha=0.7)
plt.title('Distribution of Property Prices (Log Scale)')
plt.xlabel('Price (USD)')
plt.ylabel('Frequency')
plt.xscale('log')
plt.grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

# Describe the distribution
print("PRICE DISTRIBUTION ANALYSIS")
print("=" * 40)
print(f"Mean: ${df['price_usd'].mean():,.0f}")
print(f"Median: ${df['price_usd'].median():,.0f}")
print(f"Standard Deviation: ${df['price_usd'].std():,.0f}")
print(f"Skewness: {df['price_usd'].skew():.2f}")

# Interpretation
if df['price_usd'].skew() > 1:
    skew_desc = "highly right-skewed (few very expensive properties)"
elif df['price_usd'].skew() > 0.5:
    skew_desc = "moderately right-skewed"
else:
    skew_desc = "approximately normal"

print(f"\nDistribution characteristics: {skew_desc}")
print(f"The mean is {'higher' if df['price_usd'].mean() > df['price_usd'].median() else 'lower'} than median, indicating {'positive' if df['price_usd'].mean() > df['price_usd'].median() else 'negative'} skew")
\end{lstlisting}

\textbf{Exercise 2: Compare median prices with/without balconies}
\begin{lstlisting}[caption=Balcony Price Comparison]
# Assuming we have a 'has_balcony' column (if not, we'll create sample data)
if 'has_balcony' not in df.columns:
    # Create sample balcony data for demonstration
    import numpy as np
    np.random.seed(42)
    df['has_balcony'] = np.random.choice([True, False], size=len(df), p=[0.6, 0.4])

# Calculate median prices
median_with_balcony = df[df['has_balcony'] == True]['price_usd'].median()
median_without_balcony = df[df['has_balcony'] == False]['price_usd'].median()

print("BALCONY IMPACT ON PRICE")
print("=" * 30)
print(f"Median price WITH balcony: ${median_with_balcony:,.0f}")
print(f"Median price WITHOUT balcony: ${median_without_balcony:,.0f}")
print(f"Difference: ${median_with_balcony - median_without_balcony:,.0f}")
print(f"Percentage difference: {((median_with_balcony / median_without_balcony) - 1) * 100:.1f}%")

# Statistical analysis
balcony_stats = df.groupby('has_balcony')['price_usd'].agg(['count', 'mean', 'median', 'std'])
print("\nDetailed Statistics:")
print(balcony_stats)

# Visualization
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
balcony_medians = [median_without_balcony, median_with_balcony]
categories = ['No Balcony', 'Has Balcony']
bars = plt.bar(categories, balcony_medians, color=['lightcoral', 'lightblue'], edgecolor='black')
plt.title('Median Price by Balcony Presence')
plt.ylabel('Median Price (USD)')

# Add value labels on bars
for bar, value in zip(bars, balcony_medians):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${value:,.0f}', ha='center', va='bottom')

plt.subplot(1, 2, 2)
# Box plot for more detailed comparison
balcony_data = [df[df['has_balcony'] == False]['price_usd'], 
                df[df['has_balcony'] == True]['price_usd']]
plt.boxplot(balcony_data, labels=categories)
plt.title('Price Distribution by Balcony Presence')
plt.ylabel('Price (USD)')
plt.yscale('log')  # Log scale to handle outliers

plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 3: Property count by state bar chart}
\begin{lstlisting}[caption=Property Count by State]
# Count properties by state
state_counts = df['state'].value_counts()

print("PROPERTIES BY STATE")
print("=" * 25)
print(state_counts)

# Create bar chart
plt.figure(figsize=(12, 6))
bars = plt.bar(range(len(state_counts)), state_counts.values, 
               color='steelblue', edgecolor='darkblue', alpha=0.7)

plt.title('Number of Properties by State', fontsize=16, pad=20)
plt.xlabel('State', fontsize=12)
plt.ylabel('Number of Properties', fontsize=12)
plt.xticks(range(len(state_counts)), state_counts.index, rotation=45, ha='right')

# Add value labels on bars
for i, bar in enumerate(bars):
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + max(state_counts.values) * 0.01,
             f'{int(height):,}', ha='center', va='bottom', fontsize=10)

# Add percentage labels
total_properties = len(df)
for i, (state, count) in enumerate(state_counts.items()):
    percentage = (count / total_properties) * 100
    plt.text(i, count/2, f'{percentage:.1f}%', ha='center', va='center', 
             fontweight='bold', color='white', fontsize=9)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.show()

# Summary statistics
print(f"\nTotal states: {len(state_counts)}")
print(f"Most properties: {state_counts.index[0]} ({state_counts.iloc[0]:,} properties)")
print(f"Fewest properties: {state_counts.index[-1]} ({state_counts.iloc[-1]:,} properties)")
print(f"Average per state: {state_counts.mean():.0f} properties")
\end{lstlisting}

\subsection{Medium Exercise Solutions}

\textbf{Exercise 1: Comprehensive correlation analysis}
\begin{lstlisting}[caption=Correlation Matrix Analysis]
# Select only numeric columns for correlation analysis
numeric_columns = df.select_dtypes(include=[np.number]).columns
correlation_matrix = df[numeric_columns].corr()

print("CORRELATION ANALYSIS")
print("=" * 30)

# Create correlation heatmap
plt.figure(figsize=(12, 10))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # Mask upper triangle
sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='RdBu_r', center=0,
            square=True, fmt='.3f', cbar_kws={"shrink": .8})
plt.title('Correlation Matrix of Numeric Variables', fontsize=16, pad=20)
plt.tight_layout()
plt.show()

# Find strongest correlations
def find_strong_correlations(corr_matrix, threshold=0.5):
    strong_corr = []
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            corr_value = corr_matrix.iloc[i, j]
            if abs(corr_value) >= threshold:
                strong_corr.append({
                    'Variable 1': corr_matrix.columns[i],
                    'Variable 2': corr_matrix.columns[j],
                    'Correlation': corr_value
                })
    return sorted(strong_corr, key=lambda x: abs(x['Correlation']), reverse=True)

strong_correlations = find_strong_correlations(correlation_matrix, 0.3)

print("STRONG CORRELATIONS (|r| >= 0.3):")
for corr in strong_correlations:
    direction = "positive" if corr['Correlation'] > 0 else "negative"
    strength = "very strong" if abs(corr['Correlation']) >= 0.8 else "strong" if abs(corr['Correlation']) >= 0.6 else "moderate"
    print(f"  {corr['Variable 1']} vs {corr['Variable 2']}: {corr['Correlation']:.3f} ({strength} {direction})")

# Interpretation guide
print("\nINTERPRETATION GUIDE:")
print("  0.8+ : Very strong relationship")
print("  0.6-0.8 : Strong relationship") 
print("  0.4-0.6 : Moderate relationship")
print("  0.2-0.4 : Weak relationship")
print("  <0.2 : Very weak/no relationship")
\end{lstlisting}

\textbf{Exercise 2: Multi-panel price distributions by property type}
\begin{lstlisting}[caption=Price Distributions by Property Type]
# Create property type categories (if not already available)
if 'property_type' not in df.columns:
    # Create sample property types based on area
    def categorize_property_type(area):
        if area < 50:
            return "Studio"
        elif area < 100:
            return "Apartment"
        elif area < 200:
            return "House" 
        else:
            return "Villa"
    
    df['property_type'] = df['area_sqm'].apply(categorize_property_type)

# Multi-panel visualization
property_types = df['property_type'].unique()
n_types = len(property_types)

fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.flatten()

colors = ['skyblue', 'lightcoral', 'lightgreen', 'lightyellow']

for i, prop_type in enumerate(property_types):
    if i < len(axes):
        type_data = df[df['property_type'] == prop_type]['price_usd']
        
        axes[i].hist(type_data, bins=20, color=colors[i % len(colors)], 
                    edgecolor='black', alpha=0.7)
        axes[i].set_title(f'{prop_type} Price Distribution (n={len(type_data)})')
        axes[i].set_xlabel('Price (USD)')
        axes[i].set_ylabel('Frequency')
        
        # Add statistics
        mean_price = type_data.mean()
        median_price = type_data.median()
        axes[i].axvline(mean_price, color='red', linestyle='--', alpha=0.8, 
                       label=f'Mean: ${mean_price:,.0f}')
        axes[i].axvline(median_price, color='orange', linestyle='--', alpha=0.8,
                       label=f'Median: ${median_price:,.0f}')
        axes[i].legend(fontsize=9)
        axes[i].grid(axis='y', alpha=0.3)

plt.tight_layout()
plt.show()

# Summary statistics by property type
print("PRICE STATISTICS BY PROPERTY TYPE")
print("=" * 45)
type_stats = df.groupby('property_type')['price_usd'].agg(['count', 'mean', 'median', 'std'])
print(type_stats.round(0))

# Box plot comparison
plt.figure(figsize=(10, 6))
df.boxplot(column='price_usd', by='property_type', ax=plt.gca())
plt.title('Price Distribution Comparison by Property Type')
plt.suptitle('')  # Remove automatic title
plt.yscale('log')  # Log scale for better visualization
plt.ylabel('Price (USD, log scale)')
plt.xlabel('Property Type')
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 3: Property age vs pricing analysis}
\begin{lstlisting}[caption=Property Age vs Price Analysis]
# Create property age if not available (assume current year is 2024)
if 'year_built' not in df.columns:
    # Create sample year_built data
    import numpy as np
    np.random.seed(42)
    df['year_built'] = np.random.randint(1980, 2024, size=len(df))

# Calculate property age
current_year = 2024
df['property_age'] = current_year - df['year_built']

# Scatter plot analysis
plt.figure(figsize=(15, 5))

plt.subplot(1, 3, 1)
plt.scatter(df['property_age'], df['price_usd'], alpha=0.6, color='steelblue')
plt.title('Property Age vs Price')
plt.xlabel('Property Age (years)')
plt.ylabel('Price (USD)')
plt.grid(True, alpha=0.3)

# Add trend line
z = np.polyfit(df['property_age'], df['price_usd'], 1)
p = np.poly1d(z)
plt.plot(df['property_age'], p(df['property_age']), "r--", alpha=0.8, linewidth=2)

plt.subplot(1, 3, 2)
plt.scatter(df['property_age'], df['price_per_sqm'], alpha=0.6, color='darkgreen')
plt.title('Property Age vs Price per SqM')
plt.xlabel('Property Age (years)')
plt.ylabel('Price per SqM (USD)')
plt.grid(True, alpha=0.3)

# Add trend line
z2 = np.polyfit(df['property_age'], df['price_per_sqm'], 1)
p2 = np.poly1d(z2)
plt.plot(df['property_age'], p2(df['property_age']), "r--", alpha=0.8, linewidth=2)

# Age categories analysis
plt.subplot(1, 3, 3)
def categorize_age(age):
    if age <= 5:
        return "New (0-5 years)"
    elif age <= 15:
        return "Modern (6-15 years)"
    elif age <= 30:
        return "Mature (16-30 years)"
    else:
        return "Vintage (30+ years)"

df['age_category'] = df['property_age'].apply(categorize_age)
age_stats = df.groupby('age_category')['price_usd'].median()

bars = plt.bar(range(len(age_stats)), age_stats.values, 
               color=['lightblue', 'lightgreen', 'orange', 'lightcoral'],
               edgecolor='black', alpha=0.7)
plt.title('Median Price by Age Category')
plt.xlabel('Age Category')
plt.ylabel('Median Price (USD)')
plt.xticks(range(len(age_stats)), age_stats.index, rotation=45, ha='right')

# Add value labels
for bar, value in zip(bars, age_stats.values):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1000,
             f'${value:,.0f}', ha='center', va='bottom', fontsize=9)

plt.tight_layout()
plt.show()

# Correlation analysis
age_price_corr = df['property_age'].corr(df['price_usd'])
age_price_sqm_corr = df['property_age'].corr(df['price_per_sqm'])

print("PROPERTY AGE ANALYSIS")
print("=" * 30)
print(f"Correlation between age and price: {age_price_corr:.3f}")
print(f"Correlation between age and price/sqm: {age_price_sqm_corr:.3f}")

# Interpretation
if abs(age_price_corr) < 0.1:
    age_interpretation = "very weak"
elif abs(age_price_corr) < 0.3:
    age_interpretation = "weak"
elif abs(age_price_corr) < 0.5:
    age_interpretation = "moderate"
else:
    age_interpretation = "strong"

direction = "negative" if age_price_corr < 0 else "positive"
print(f"\nInterpretation: There is a {age_interpretation} {direction} relationship between property age and price")

if age_price_corr < -0.2:
    print("Older properties tend to be less expensive")
elif age_price_corr > 0.2:
    print("Older properties tend to be more expensive (possibly vintage premium)")
else:
    print("Property age has minimal impact on pricing")
\end{lstlisting}

\subsection{Challenging Exercise Solutions}

\textbf{Exercise 1: Statistical hypothesis testing for regional price differences}
\begin{lstlisting}[caption=Statistical Hypothesis Testing]
from scipy import stats
import numpy as np

def perform_regional_price_test(df, region_col='state', price_col='price_usd'):
    """
    Perform statistical tests to compare prices between regions
    """
    
    print("STATISTICAL HYPOTHESIS TESTING")
    print("=" * 45)
    
    # Get regions with sufficient data
    region_counts = df[region_col].value_counts()
    valid_regions = region_counts[region_counts >= 30].index[:5]  # Top 5 regions with 30+ properties
    
    print(f"Testing price differences between {len(valid_regions)} regions:")
    for region in valid_regions:
        count = region_counts[region]
        print(f"  - {region}: {count} properties")
    
    # Extract price data for each region
    region_price_data = []
    for region in valid_regions:
        prices = df[df[region_col] == region][price_col].values
        region_price_data.append(prices)
    
    # 1. ANOVA Test (Are there ANY differences between regions?)
    print(f"\n1. ONE-WAY ANOVA TEST")
    print("-" * 25)
    f_stat, p_value_anova = stats.f_oneway(*region_price_data)
    
    print(f"F-statistic: {f_stat:.3f}")
    print(f"P-value: {p_value_anova:.6f}")
    
    alpha = 0.05
    if p_value_anova < alpha:
        print(f"Result: SIGNIFICANT difference between regions (p < {alpha})")
        print("Conclusion: At least one region has significantly different prices")
    else:
        print(f"Result: NO significant difference between regions (p >= {alpha})")
        print("Conclusion: All regions have similar average prices")
    
    # 2. Pairwise t-tests (Which specific regions differ?)
    if p_value_anova < alpha and len(valid_regions) > 2:
        print(f"\n2. PAIRWISE T-TESTS (Bonferroni corrected)")
        print("-" * 45)
        
        from itertools import combinations
        n_comparisons = len(list(combinations(valid_regions, 2)))
        bonferroni_alpha = alpha / n_comparisons
        
        print(f"Number of comparisons: {n_comparisons}")
        print(f"Bonferroni-corrected alpha: {bonferroni_alpha:.6f}")
        
        significant_pairs = []
        for i, (region1, region2) in enumerate(combinations(valid_regions, 2)):
            prices1 = df[df[region_col] == region1][price_col]
            prices2 = df[df[region_col] == region2][price_col]
            
            t_stat, p_value = stats.ttest_ind(prices1, prices2, equal_var=False)
            
            is_significant = p_value < bonferroni_alpha
            if is_significant:
                significant_pairs.append((region1, region2, p_value))
            
            print(f"{region1} vs {region2}: t={t_stat:.3f}, p={p_value:.6f} {'*' if is_significant else ''}")
        
        if significant_pairs:
            print(f"\nSignificant differences found:")
            for region1, region2, p_val in significant_pairs:
                mean1 = df[df[region_col] == region1][price_col].mean()
                mean2 = df[df[region_col] == region2][price_col].mean()
                print(f"  {region1} (${mean1:,.0f}) vs {region2} (${mean2:,.0f}), p={p_val:.6f}")
        else:
            print("\nNo significant pairwise differences after Bonferroni correction")
    
    # 3. Effect Size (How big are the differences?)
    print(f"\n3. EFFECT SIZE ANALYSIS")
    print("-" * 25)
    
    # Calculate eta-squared (proportion of variance explained by region)
    overall_mean = df[price_col].mean()
    ss_between = sum(len(data) * (np.mean(data) - overall_mean)**2 for data in region_price_data)
    ss_total = sum((df[price_col] - overall_mean)**2)
    eta_squared = ss_between / ss_total
    
    print(f"Eta-squared: {eta_squared:.4f}")
    print(f"Interpretation: Region explains {eta_squared*100:.2f}% of price variance")
    
    if eta_squared < 0.01:
        effect_size = "negligible"
    elif eta_squared < 0.06:
        effect_size = "small"
    elif eta_squared < 0.14:
        effect_size = "medium"
    else:
        effect_size = "large"
    
    print(f"Effect size: {effect_size}")
    
    return {
        'anova_p_value': p_value_anova,
        'eta_squared': eta_squared,
        'significant_pairs': significant_pairs if p_value_anova < alpha else []
    }

# Run the analysis
test_results = perform_regional_price_test(df)

# Visualization of results
plt.figure(figsize=(15, 5))

# Box plot
plt.subplot(1, 2, 1)
region_counts = df['state'].value_counts()
top_regions = region_counts.head(5).index
plot_data = [df[df['state'] == region]['price_usd'] for region in top_regions]
plt.boxplot(plot_data, labels=top_regions)
plt.title('Price Distribution by Region')
plt.ylabel('Price (USD)')
plt.xticks(rotation=45)
plt.yscale('log')

# Violin plot for more detail
plt.subplot(1, 2, 2)
top_5_data = df[df['state'].isin(top_regions)]
sns.violinplot(data=top_5_data, x='state', y='price_usd')
plt.title('Price Distribution Details by Region')
plt.ylabel('Price (USD)')
plt.xticks(rotation=45)
plt.yscale('log')

plt.tight_layout()
plt.show()
\end{lstlisting}

\textbf{Exercise 2: Comprehensive EDA report generation}
\begin{lstlisting}[caption=Automated EDA Report Generator]
def generate_eda_report(df, output_file='eda_report.html'):
    """
    Generate a comprehensive EDA report with visualizations and insights
    """
    
    report_sections = []
    
    # 1. Dataset Overview
    overview = f"""
    <h2>1. Dataset Overview</h2>
    <ul>
        <li><strong>Total Records:</strong> {len(df):,}</li>
        <li><strong>Total Features:</strong> {len(df.columns)}</li>
        <li><strong>Memory Usage:</strong> {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB</li>
        <li><strong>Date Range:</strong> Data loaded on {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}</li>
    </ul>
    """
    report_sections.append(overview)
    
    # 2. Data Quality Assessment
    missing_data = df.isnull().sum()
    missing_pct = (missing_data / len(df)) * 100
    
    quality_issues = []
    for col, pct in missing_pct.items():
        if pct > 20:
            quality_issues.append(f"{col}: {pct:.1f}% missing")
    
    quality_section = f"""
    <h2>2. Data Quality Assessment</h2>
    <h3>Missing Data Summary:</h3>
    <ul>
        <li><strong>Columns with missing data:</strong> {sum(missing_data > 0)}</li>
        <li><strong>Total missing values:</strong> {missing_data.sum():,}</li>
        <li><strong>Overall completeness:</strong> {((len(df) * len(df.columns) - missing_data.sum()) / (len(df) * len(df.columns)) * 100):.1f}%</li>
    </ul>
    """
    
    if quality_issues:
        quality_section += "<h3>Data Quality Issues:</h3><ul>"
        for issue in quality_issues:
            quality_section += f"<li>{issue}</li>"
        quality_section += "</ul>"
    else:
        quality_section += "<p><em>No major data quality issues detected.</em></p>"
    
    report_sections.append(quality_section)
    
    # 3. Numeric Variables Analysis
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    numeric_section = f"""
    <h2>3. Numeric Variables Analysis</h2>
    <p><strong>Numeric variables found:</strong> {len(numeric_cols)}</p>
    <table border="1" style="border-collapse: collapse;">
        <tr><th>Variable</th><th>Mean</th><th>Median</th><th>Std Dev</th><th>Min</th><th>Max</th></tr>
    """
    
    for col in numeric_cols:
        stats = df[col].describe()
        numeric_section += f"""
        <tr>
            <td>{col}</td>
            <td>{stats['mean']:,.2f}</td>
            <td>{stats['50%']:,.2f}</td>
            <td>{stats['std']:,.2f}</td>
            <td>{stats['min']:,.2f}</td>
            <td>{stats['max']:,.2f}</td>
        </tr>
        """
    
    numeric_section += "</table>"
    report_sections.append(numeric_section)
    
    # 4. Categorical Variables Analysis
    categorical_cols = df.select_dtypes(include=['object']).columns
    categorical_section = f"""
    <h2>4. Categorical Variables Analysis</h2>
    <p><strong>Categorical variables found:</strong> {len(categorical_cols)}</p>
    """
    
    for col in categorical_cols:
        unique_count = df[col].nunique()
        most_common = df[col].mode().iloc[0] if not df[col].mode().empty else "N/A"
        categorical_section += f"""
        <h3>{col}</h3>
        <ul>
            <li><strong>Unique values:</strong> {unique_count}</li>
            <li><strong>Most common:</strong> {most_common}</li>
        </ul>
        """
    
    report_sections.append(categorical_section)
    
    # 5. Key Insights
    insights_section = "<h2>5. Key Insights</h2><ul>"
    
    # Price insights
    if 'price_usd' in df.columns:
        price_skew = df['price_usd'].skew()
        insights_section += f"<li><strong>Price Distribution:</strong> "
        if price_skew > 1:
            insights_section += "Highly right-skewed, indicating presence of luxury properties</li>"
        elif price_skew > 0.5:
            insights_section += "Moderately right-skewed, typical for real estate markets</li>"
        else:
            insights_section += "Approximately normal distribution</li>"
    
    # Geographic distribution
    if 'state' in df.columns:
        state_concentration = (df['state'].value_counts().iloc[0] / len(df)) * 100
        insights_section += f"<li><strong>Geographic Concentration:</strong> {state_concentration:.1f}% of properties are in {df['state'].value_counts().index[0]}</li>"
    
    # Size insights
    if 'area_sqm' in df.columns:
        avg_size = df['area_sqm'].mean()
        insights_section += f"<li><strong>Average Property Size:</strong> {avg_size:.0f} square meters</li>"
    
    insights_section += "</ul>"
    report_sections.append(insights_section)
    
    # 6. Recommendations
    recommendations = """
    <h2>6. Recommendations for Further Analysis</h2>
    <ul>
        <li>Investigate outliers in price and size variables</li>
        <li>Perform correlation analysis between numeric variables</li>
        <li>Conduct regional price comparison analysis</li>
        <li>Explore seasonality patterns if date information is available</li>
        <li>Consider market segmentation based on property characteristics</li>
    </ul>
    """
    report_sections.append(recommendations)
    
    # Combine all sections
    full_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Exploratory Data Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            h1 {{ color: #2E86AB; }}
            h2 {{ color: #A23B72; border-bottom: 2px solid #A23B72; }}
            h3 {{ color: #F18F01; }}
            table {{ width: 100%; }}
            th, td {{ padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            ul {{ line-height: 1.6; }}
            li {{ margin-bottom: 5px; }}
        </style>
    </head>
    <body>
        <h1>Exploratory Data Analysis Report</h1>
        <p><em>Generated on {pd.Timestamp.now().strftime('%Y-%m-%d at %H:%M:%S')}</em></p>
        
        {''.join(report_sections)}
        
        <hr>
        <p><small>Report generated automatically using Python pandas and statistical analysis.</small></p>
    </body>
    </html>
    """
    
    # Save to file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(full_report)
    
    print(f"EDA Report saved to: {output_file}")
    return full_report

# Generate the report
report_html = generate_eda_report(df)
print("Comprehensive EDA report generated successfully!")
print("\nReport includes:")
print("- Dataset overview and statistics")  
print("- Data quality assessment")
print("- Numeric and categorical variable analysis")
print("- Key insights and patterns")
print("- Recommendations for further analysis")
\end{lstlisting}

\end{document} 