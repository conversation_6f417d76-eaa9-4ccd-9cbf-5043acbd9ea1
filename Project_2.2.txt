Skip to left side bar










Code




Python 3 (ipykernel)

Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
2.2. Predicting Price with Location

import warnings
​
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import wqet_grader
from IPython.display import VimeoVideo
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error
from sklearn.pipeline import Pipeline, make_pipeline
from sklearn.utils.validation import check_is_fitted
​
warnings.simplefilter(action="ignore", category=FutureWarning)
wqet_grader.init("Project 2 Assessment")
In this lesson, we're going to build on the work we did in the previous lesson. We're going to create a more complex wrangle function, use it to clean more data, and build a model that considers more features when predicting apartment price.

VimeoVideo("656752925", h="701f3f4081", width=600)
Prepare Data
Import
def wrangle(filepath):
    # Read CSV file
    df = pd.read_csv(filepath)
​
    # Subset data: Apartments in "Capital Federal", less than 400,000
    mask_ba = df["place_with_parent_names"].str.contains("Capital Federal")
    mask_apt = df["property_type"] == "apartment"
    mask_price = df["price_aprox_usd"] < 400_000
    df = df[mask_ba & mask_apt & mask_price]
​
    # Subset data: Remove outliers for "surface_covered_in_m2"
    low, high = df["surface_covered_in_m2"].quantile([0.1, 0.9])
    mask_area = df["surface_covered_in_m2"].between(low, high)
    df = df[mask_area]
    # Split "lat-lon" into "lat" and "lon"
    df[["lat", "lon"]] = df["lat-lon"].str.split(",", expand=True).astype(float)
​
    # Drop the original "lat-lon" column
    df = df.drop(columns="lat-lon")
​
    
​
    return df
VimeoVideo("656752771", h="3a42896eb6", width=600)
Task 2.2.1: Use your wrangle function to create a DataFrame frame1 from the CSV file data/buenos-aires-real-estate-1.csv.

frame1 = pd.read_csv('data/buenos-aires-real-estate-1.csv')
print(frame1.info())
frame1.head()
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 8606 entries, 0 to 8605
Data columns (total 16 columns):
 #   Column                      Non-Null Count  Dtype  
---  ------                      --------------  -----  
 0   operation                   8606 non-null   object 
 1   property_type               8606 non-null   object 
 2   place_with_parent_names     8606 non-null   object 
 3   lat-lon                     6936 non-null   object 
 4   price                       7590 non-null   float64
 5   currency                    7590 non-null   object 
 6   price_aprox_local_currency  7590 non-null   float64
 7   price_aprox_usd             7590 non-null   float64
 8   surface_total_in_m2         5946 non-null   float64
 9   surface_covered_in_m2       7268 non-null   float64
 10  price_usd_per_m2            4895 non-null   float64
 11  price_per_m2                6520 non-null   float64
 12  floor                       1259 non-null   float64
 13  rooms                       4752 non-null   float64
 14  expenses                    875 non-null    object 
 15  properati_url               8606 non-null   object 
dtypes: float64(9), object(7)
memory usage: 1.1+ MB
None
operation	property_type	place_with_parent_names	lat-lon	price	currency	price_aprox_local_currency	price_aprox_usd	surface_total_in_m2	surface_covered_in_m2	price_usd_per_m2	price_per_m2	floor	rooms	expenses	properati_url
0	sell	apartment	|Argentina|Capital Federal|Villa Crespo|	-34.6047834183,-58.4586812499	180000.0	USD	2729232.0	180000.0	120.0	110.0	1500.000000	1636.363636	NaN	4.0	NaN	http://villa-crespo.properati.com.ar/12egq_ven...
1	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|La Matanza...	NaN	250000.0	USD	3790600.0	250000.0	117.0	120.0	2136.752137	2083.333333	NaN	4.0	NaN	http://ramos-mejia.properati.com.ar/s7pd_venta...
2	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|Morón|Cast...	-34.6497002,-58.658073	410000.0	USD	6216584.0	410000.0	410.0	220.0	1000.000000	1863.636364	NaN	NaN	NaN	http://castelar-moron.properati.com.ar/11vgn_v...
3	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|Tres de Fe...	-34.5957086,-58.5669503	180000.0	USD	2729232.0	180000.0	200.0	135.0	900.000000	1333.333333	NaN	5.0	NaN	http://tres-de-febrero.properati.com.ar/7f7u_v...
4	sell	apartment	|Argentina|Capital Federal|Chacarita|	-34.5846508988,-58.4546932614	129000.0	USD	1955949.6	129000.0	76.0	70.0	1697.368421	1842.857143	NaN	NaN	NaN	http://chacarita.properati.com.ar/10qlv_venta_...
For our model, we're going to consider apartment location, specifically, latitude and longitude. Looking at the output from frame1.info(), we can see that the location information is in a single column where the data type is object (pandas term for str in this case). In order to build our model, we need latitude and longitude to each be in their own column where the data type is float.

VimeoVideo("656751955", h="e47002428d", width=600)
Task 2.2.2: Add to the wrangle function below so that, in the DataFrame it returns, the "lat-lon" column is replaced by separate "lat" and "lon" columns. Don't forget to also drop the "lat-lon" column. Be sure to rerun all the cells above before you continue.

What's a function?
Split the strings in one column to create another using pandas.
Drop a column from a DataFrame using pandas.
# Split "lat-lon" into "lat" and "lon"
frame1[["lat", "lon"]] = frame1["lat-lon"].str.split(",", expand=True).astype(float)
​
# Drop the original "lat-lon" column
frame1 = frame1.drop(columns="lat-lon")
operation	property_type	place_with_parent_names	price	currency	price_aprox_local_currency	price_aprox_usd	surface_total_in_m2	surface_covered_in_m2	price_usd_per_m2	price_per_m2	floor	rooms	expenses	properati_url	lat	lon
0	sell	apartment	|Argentina|Capital Federal|Villa Crespo|	180000.0	USD	2729232.0	180000.0	120.0	110.0	1500.000000	1636.363636	NaN	4.0	NaN	http://villa-crespo.properati.com.ar/12egq_ven...	-34.604783	-58.458681
1	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|La Matanza...	250000.0	USD	3790600.0	250000.0	117.0	120.0	2136.752137	2083.333333	NaN	4.0	NaN	http://ramos-mejia.properati.com.ar/s7pd_venta...	NaN	NaN
2	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|Morón|Cast...	410000.0	USD	6216584.0	410000.0	410.0	220.0	1000.000000	1863.636364	NaN	NaN	NaN	http://castelar-moron.properati.com.ar/11vgn_v...	-34.649700	-58.658073
3	sell	house	|Argentina|Bs.As. G.B.A. Zona Oeste|Tres de Fe...	180000.0	USD	2729232.0	180000.0	200.0	135.0	900.000000	1333.333333	NaN	5.0	NaN	http://tres-de-febrero.properati.com.ar/7f7u_v...	-34.595709	-58.566950
4	sell	apartment	|Argentina|Capital Federal|Chacarita|	129000.0	USD	1955949.6	129000.0	76.0	70.0	1697.368421	1842.857143	NaN	NaN	NaN	http://chacarita.properati.com.ar/10qlv_venta_...	-34.584651	-58.454693
...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...	...
8601	sell	apartment	|Argentina|Capital Federal|San Nicolás|	125000.0	USD	1895300.0	125000.0	NaN	70.0	NaN	1785.714286	2.0	3.0	NaN	http://san-nicolas.properati.com.ar/1004t_vent...	-34.601455	-58.378132
8602	sell	house	|Argentina|Bs.As. G.B.A. Zona Norte|San Fernando|	480000.0	USD	7277952.0	480000.0	683.0	235.0	702.781845	2042.553191	NaN	NaN	NaN	http://san-fernando-bs-as-g-b-a-zona-norte.pro...	NaN	NaN
8603	sell	house	|Argentina|Bs.As. G.B.A. Zona Norte|Tigre|Nord...	510000.0	USD	7732824.0	510000.0	0.0	256.0	NaN	1992.187500	NaN	NaN	NaN	http://nordelta.properati.com.ar/zg3s_venta_ca...	NaN	NaN
8604	sell	apartment	|Argentina|Capital Federal|Boedo|	78000.0	USD	1182667.2	78000.0	52.0	52.0	1500.000000	1500.000000	NaN	NaN	NaN	http://boedo.properati.com.ar/eh9b_venta_depar...	NaN	NaN
8605	sell	PH	|Argentina|Bs.As. G.B.A. Zona Norte|Pilar|	65000.0	USD	985556.0	65000.0	90.0	60.0	722.222222	1083.333333	NaN	NaN	NaN	http://pilar-bs-as-g-b-a-zona-norte.properati....	-34.454135	-58.902875
8606 rows × 17 columns

frame1 = wrangle('data/buenos-aires-real-estate-1.csv')
# Check your work
assert (
    frame1.shape[0] == 1343
), f"`frame1` should have 1343 rows, not {frame1.shape[0]}."
assert frame1.shape[1] == 17, f"`frame1` should have 17 columns, not {frame1.shape[1]}."
Now that our wrangle function is working, let's use it to clean more data!

VimeoVideo("656751853", h="da40b0a474", width=600)
Task 2.2.3: Use you revised wrangle function create a DataFrames frame2 from the file data/buenos-aires-real-estate-2.csv.

frame2 = wrangle("data/buenos-aires-real-estate-2.csv")
# Check your work
assert (
    frame2.shape[0] == 1315
), f"`frame1` should have 1315 rows, not {frame2.shape[0]}."
assert frame2.shape[1] == 17, f"`frame1` should have 17 columns, not {frame2.shape[1]}."
As you can see, using a function is much quicker than cleaning each file individually like we did in the last project. Let's combine our DataFrames so we can use then to train our model.

VimeoVideo("656751405", h="d1f95ab108", width=600)
Task 2.2.4: Use pd.concat to concatenate frame1 and frame2 into a new DataFrame df. Make sure you set the ignore_index argument to True.

Concatenate two or more DataFrames using pandas.
df = pd.concat([frame1, frame2], ignore_index=True)
​
print(df.info())
df.head()
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 2658 entries, 0 to 2657
Data columns (total 17 columns):
 #   Column                      Non-Null Count  Dtype  
---  ------                      --------------  -----  
 0   operation                   2658 non-null   object 
 1   property_type               2658 non-null   object 
 2   place_with_parent_names     2658 non-null   object 
 3   price                       2658 non-null   float64
 4   currency                    2658 non-null   object 
 5   price_aprox_local_currency  2658 non-null   float64
 6   price_aprox_usd             2658 non-null   float64
 7   surface_total_in_m2         1898 non-null   float64
 8   surface_covered_in_m2       2658 non-null   float64
 9   price_usd_per_m2            1818 non-null   float64
 10  price_per_m2                2658 non-null   float64
 11  floor                       769 non-null    float64
 12  rooms                       2137 non-null   float64
 13  expenses                    688 non-null    object 
 14  properati_url               2658 non-null   object 
 15  lat                         2561 non-null   float64
 16  lon                         2561 non-null   float64
dtypes: float64(11), object(6)
memory usage: 353.1+ KB
None
operation	property_type	place_with_parent_names	price	currency	price_aprox_local_currency	price_aprox_usd	surface_total_in_m2	surface_covered_in_m2	price_usd_per_m2	price_per_m2	floor	rooms	expenses	properati_url	lat	lon
0	sell	apartment	|Argentina|Capital Federal|Chacarita|	129000.0	USD	1955949.6	129000.0	76.0	70.0	1697.368421	1842.857143	NaN	NaN	NaN	http://chacarita.properati.com.ar/10qlv_venta_...	-34.584651	-58.454693
1	sell	apartment	|Argentina|Capital Federal|Villa Luro|	87000.0	USD	1319128.8	87000.0	48.0	42.0	1812.500000	2071.428571	NaN	NaN	NaN	http://villa-luro.properati.com.ar/12m82_venta...	-34.638979	-58.500115
2	sell	apartment	|Argentina|Capital Federal|Caballito|	118000.0	USD	1789163.2	118000.0	NaN	54.0	NaN	2185.185185	NaN	2.0	NaN	http://caballito.properati.com.ar/11wqh_venta_...	-34.615847	-58.459957
3	sell	apartment	|Argentina|Capital Federal|Constitución|	57000.0	USD	864256.8	57000.0	42.0	42.0	1357.142857	1357.142857	5.0	2.0	364	http://constitucion.properati.com.ar/k2f0_vent...	-34.625222	-58.382382
4	sell	apartment	|Argentina|Capital Federal|Once|	90000.0	USD	1364616.0	90000.0	57.0	50.0	1578.947368	1800.000000	NaN	3.0	450	http://once.properati.com.ar/suwa_venta_depart...	-34.610610	-58.412511
# Check your work
assert df.shape == (2658, 17), f"`df` is the wrong size: {df.shape}"
Explore
In the last lesson, we built a simple linear model that predicted apartment price based on one feature, "surface_covered_in_m2". In this lesson, we're building a multiple linear regression model that predicts price based on two features, "lon" and "lat". This means that our data visualizations now have to communicate three pieces of information: Longitude, latitude, and price. How can we represent these three attributes on a two-dimensional screen?

One option is to incorporate color into our scatter plot. For example, in the Mapbox scatter plot below, the location of each point represents latitude and longitude, and color represents price.

VimeoVideo("656751031", h="367be02e14", width=600)
Task 2.2.5: Complete the code below to create a Mapbox scatter plot that shows the location of the apartments in df.

What's a scatter plot?
Create a Mapbox scatter plot in plotly express.
fig = px.scatter_mapbox(
    df,  # Our DataFrame
    lat="lat",
    lon="lon",
    width=600,  # Width of map
    height=600,  # Height of map
    color="price_aprox_usd",
    hover_data=["price_aprox_usd"],  # Display price when hovering mouse over house
)
​
fig.update_layout(mapbox_style="open-street-map")
​
fig.show()
Another option is to add a third dimension to our scatter plot. We can plot longitude on the x-axis and latitude on the y-axis (like we do in the map above), and then add a z-axis with price.

VimeoVideo("656750669", h="574287f687", width=600)
Task 2.2.6: Complete the code below to create a 3D scatter plot, with "lon" on the x-axis, "lat" on the y-axis, and "price_aprox_usd" on the z-axis.

What's a scatter plot?
Create a 3D scatter plot in plotly express.
# Create 3D scatter plot
fig = px.scatter_3d(
    df,
    x="lon",
    y="lat",
    z="price_aprox_usd",
    labels={"lon": "longitude", "lat": "latitude", "price_aprox_usd": "price"},
    width=600,
    height=500,
)
​
# Refine formatting
fig.update_traces(
    marker={"size": 4, "line": {"width": 2, "color": "DarkSlateGrey"}},
    selector={"mode": "markers"},
)
​
# Display figure
fig.show()

Tip: 3D visualizations are often harder for someone to interpret than 2D visualizations. We're using one here because it will help us visualize our model once it's built, but as a rule, it's better to stick with 2D when your communicating with an audience.
In the last lesson, we represented our simple model as a line imposed on a 2D scatter plot.

Scatter plot of price versus area with line plot
How do you think we'll represent our multiple linear regression model in the 3D plot we just made?

Split
Even though we're building a different model, the steps we follow will be the same. Let's separate our features (latitude and longitude) from our target (price).

VimeoVideo("656750457", h="09f5fe3962", width=600)
Task 2.2.7: Create the feature matrix named X_train. It should contain two features: ["lon", "lat"].

What's a feature matrix?
Subset a DataFrame by selecting one or more columns in pandas.
features = ["lon", "lat"]
X_train = df[features]
VimeoVideo("656750323", h="1a82090b9b", width=600)
Task 2.2.8: Create the target vector named y_train, which you'll use to train your model. Your target should be "price_aprox_usd". Remember that, in most cases, your target vector should be one-dimensional.

What's a target vector?
Select a Series from a DataFrame in pandas.
target = "price_aprox_usd"
y_train = df[target]
Build Model
Baseline
Again, we need to set a baseline so we can evaluate our model's performance. You'll notice that the value of y_mean is not exactly the same as it was in the previous lesson. That's because we've added more observations to our training data.

VimeoVideo("656750112", h="1ef669fe2b", width=600)
Task 2.2.9: Calculate the mean of your target vector y_train and assign it to the variable y_mean.

Calculate summary statistics for a DataFrame or Series in pandas.
y_mean = y_train.mean()
Task 2.2.10: Create a list named y_pred_baseline that contains the value of y_mean repeated so that it's the same length at y_train.

Calculate the length of a list in Python.
y_pred_baseline = [y_mean] * len(y_train)
VimeoVideo("656749994", h="50c71bf4e5", width=600)
Task 2.2.11: Calculate the baseline mean absolute error for your predictions in y_pred_baseline as compared to the true targets in y_train.

What's a performance metric?
What's mean absolute error?
Calculate the mean absolute error for a list of predictions in scikit-learn.
mae_baseline = mean_absolute_error(y_train, y_pred_baseline)
​
print("Mean apt price", round(y_mean, 2))
print("Baseline MAE:", round(mae_baseline, 2))
Mean apt price 134732.97
Baseline MAE: 45422.75
Iterate
Take a moment to scroll up to the output for df.info() and look at the values in the "Non-Null Count" column. Because of the math it uses, a linear regression model can't handle observations where there are missing values. Do you see any columns where this will be a problem?

In the last project, we simply dropped rows that contained NaN values, but this isn't ideal. Models generally perform better when they have more data to train with, so every row is precious. Instead, we can fill in these missing values using information we get from the whole column — a process called imputation. There are many different strategies for imputing missing values, and one of the most common is filling in the missing values with the mean of the column.

In addition to predictors like LinearRegression, scikit-learn also has transformers that help us deal with issues like missing values. Let's see how one works, and then we'll add it to our model.

VimeoVideo("656748776", h="014f943c46", width=600)
Task 2.2.12: Instantiate a SimpleImputer named imputer.

What's imputation?
Instantiate a transformer in scikit-learn.
from sklearn.impute import SimpleImputer
imputer = imputer = SimpleImputer()
# Check your work
assert isinstance(imputer, SimpleImputer)
Just like a predictor, a transformer has a fit method. In the case of our SimpleImputer, this is the step where it calculates the mean values for each numerical column.

VimeoVideo("656748659", h="fdaa8d0329", width=600)
Task 2.2.13: Fit your transformer imputer to the feature matrix X.

Fit a transformer to training data in scikit-learn.
imputer.fit(X_train)
SimpleImputer()
In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook.
On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.
# Check your work
check_is_fitted(imputer)
Here's where transformers diverge from predictors. Instead of using a method like predict, we use the transform method. This is the step where the transformer fills in the missing values with the means it's calculated.

VimeoVideo("656748527", h="d76e63760c", width=600)
VimeoVideo("656748527", h="d76e63760c", width=600)
---------------------------------------------------------------------------
NameError                                 Traceback (most recent call last)
Cell In[1], line 1
----> 1 VimeoVideo("656748527", h="d76e63760c", width=600)

NameError: name 'VimeoVideo' is not defined
Task 2.2.14: Use your imputer to transform the feature matrix X_train. Assign the transformed data to the variable XT_train.

Transform data using a transformer in scikit-learn.
XT_train = imputer.transform(X_train)
pd.DataFrame(XT_train, columns=X_train.columns).info()
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 2658 entries, 0 to 2657
Data columns (total 2 columns):
 #   Column  Non-Null Count  Dtype  
---  ------  --------------  -----  
 0   lon     2658 non-null   float64
 1   lat     2658 non-null   float64
dtypes: float64(2)
memory usage: 41.7 KB
# Check your work
assert XT_train.shape == (2658, 2), f"`XT_train` is the wrong shape: {XT_train.shape}"
assert (
    np.isnan(XT_train).sum() == 0
), "Your feature matrix still has `NaN` values. Did you forget to transform is using `imputer`?"
Okay! Our data is free of missing values, and we have a good sense for how predictors work in scikit-learn. However, the truth is you'll rarely do data transformations this way. Why? A model may require multiple transformers, and doing all those transformations one-by-one is slow and likely to lead to errors. 🤦‍♀️ Instead, we can combine our transformer and predictor into a single object called a pipeline. WQU WorldQuant University Applied Data Science Lab QQQQ

VimeoVideo("656748360", h="50b4643a26", width=600)
Task 2.2.15: Create a pipeline named model that contains a SimpleImputer transformer followed by a LinearRegression predictor.

What's a pipeline?
Create a pipeline in scikit-learn.
model = make_pipeline(
    SimpleImputer(),
    LinearRegression()
)
assert isinstance(model, Pipeline), "Did you instantiate your model?"
With our pipeline assembled, we use the fit method, which will train the transformer, transform the data, then pass the transformed data to the predictor for training, all in one step. Much easier!

VimeoVideo("656748234", h="59ba7958d5", width=600)
Task 2.2.16: Fit your model to the data, X_train and y_train.

Fit a model to training data in scikit-learn.
model.fit(X_train, y_train)
​
Pipeline(steps=[('simpleimputer', SimpleImputer()),
                ('linearregression', LinearRegression())])
In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook.
On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.
# Check your work
check_is_fitted(model["linearregression"])
Success! Let's see how our trained model performs.

Evaluate
As always, we'll start by evaluating our model's performance on the training data.

VimeoVideo("656748155", h="5672ef44cb", width=600)
Task 2.2.17: Using your model's predict method, create a list of predictions for the observations in your feature matrix X_train. Name this list y_pred_training.

Generate predictions using a trained model in scikit-learn.
y_pred_training = model.predict(X_train)
# Check your work
assert y_pred_training.shape == (2658,)
VimeoVideo("656748205", h="13144556a6", width=600)
Task 2.2.18: Calculate the training mean absolute error for your predictions in y_pred_training as compared to the true targets in y_train.

Calculate the mean absolute error for a list of predictions in scikit-learn.
mae_training = mean_absolute_error(y_train, y_pred_training)
print("Training MAE:", round(mae_training, 2))
Training MAE: 42962.72
It looks like our model performs a little better than the baseline. This suggests that latitude and longitude aren't as strong predictors of price as size is.

Now let's check our test performance. Remember, once we test our model, there's no more iteration allowed.

Task 2.2.19: Run the code below to import your test data buenos-aires-test-features.csv into a DataFrame and generate a Series of predictions using your model. Then run the following cell to submit your predictions to the grader.

What's generalizability?
Generate predictions using a trained model in scikit-learn.
Calculate the mean absolute error for a list of predictions in scikit-learn.
X_test = pd.read_csv("data/buenos-aires-test-features.csv")[features]
y_pred_test = pd.Series(model.predict(X_test))
y_pred_test.head()
0    136372.324695
1    168620.352353
2    130231.628267
3    102497.549527
4    123482.077850
dtype: float64
wqet_grader.grade("Project 2 Assessment", "Task 2.2.19", y_pred_test)
Your model's mean absolute error is 48064.487. Yup. You got it.

Score: 1

Again, we want our test performance to be about the same as our training performance, but it's OK if it's not quite as good.

Communicate Results
Let's take a look at the equation our model has come up with for predicting price based on latitude and longitude. We'll need to expand on our formula to account for both features.

Equation: y = beta 0 + beta 1 * x
VimeoVideo("*********", h="b90db6b373", width=600)
Task 2.2.20: Extract the intercept and coefficients for your model.

What's an intercept in a linear model?
What's a coefficient in a linear model?
Access an object in a pipeline in scikit-learn.
linear_model = model.named_steps['linearregression']
intercept = linear_model.intercept_
​
coefficient = linear_model.coef_
Task 2.2.21: Complete the code below and run the cell to print the equation that your model has determined for predicting apartment price based on latitude and longitude.

What's an f-string?
print(
    f"price = {intercept:.2f} + ({coefficient[0]:.2f} * longitude) + ({coefficient[1]:.2f} * latitude)"
)
​
price = ********.05 + (196709.42 * longitude) + (765466.58 * latitude)
What does this equation tell us? As you move north and east, the predicted apartment price increases.

At the start of the notebook, you thought about how we would represent our linear model in a 3D plot. If you guessed that we would use a plane, you're right!

VimeoVideo("656746928", h="71bfe94764", width=600)
Task 2.2.22: Complete the code below to create a 3D scatter plot, with "lon" on the x-axis, "lat" on the y-axis, and "price_aprox_usd" on the z-axis.

What's a scatter plot?
Create a 3D scatter plot in plotly express.






# Create 3D scatter plot
fig = px.scatter_3d(
    df,
    x="lat",
    y="lon",
    z="price_aprox_usd",
    labels={"lon": "longitude", "lat": "latitude", "price_aprox_usd": "price"},
    width=600,
    height=500,
)
​
# Create x and y coordinates for model representation
x_plane = np.linspace(df["lon"].min(), df["lon"].max(), 10)
y_plane = np.linspace(df["lat"].min(), df["lat"].max(), 10)
xx, yy = np.meshgrid(x_plane, y_plane)
​
# Use model to predict z coordinates
z_plane = model.predict(pd.DataFrame({"lon": x_plane, "lat": y_plane}))
zz = np.tile(z_plane, (10, 1))
​
# Add plane to figure
fig.add_trace(go.Surface(x=xx, y=yy, z=zz))
​
# Refine formatting
fig.update_traces(
    marker={"size": 4, "line": {"width": 2, "color": "DarkSlateGrey"}},
    selector={"mode": "markers"},
)
​
# Display figure
fig.show()
Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
8
Python 3 (ipykernel) | Idle
0
022-price-and-location.ipynb
Ln 4, Col 13
Mode: Command