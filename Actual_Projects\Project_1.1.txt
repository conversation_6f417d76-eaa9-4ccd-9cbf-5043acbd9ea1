Skip to left side bar










Code




Python 3 (ipykernel)






# WELCOME THE FIRST LESSON OF PROJECT 1!
#
# If you haven't watched the virtual machine orientation video yet,
# 👆 Click the ▶️ button above to get started.
​
from IPython.display import VimeoVideo
​
VimeoVideo("656189257", h="3298dbabb7", width=600)

Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
1.1. Organizing Tabular Data in Python

What's Tabular Data?
VimeoVideo("645390762", h="578b5a9e19", width=600)
---------------------------------------------------------------------------
NameError                                 Traceback (most recent call last)
Cell In[1], line 1
----> 1 VimeoVideo("645390762", h="578b5a9e19", width=600)

NameError: name 'VimeoVideo' is not defined
Frequent Question: When I try to run this cell ☝️, I get:

NameError: name 'VimeoVideo' is not defined
Tip: NameError usually means that you're asking Python to use a tool that you haven't imported yet. So make sure you run the first cell of this notebook, the one that contains from IPython.display import VimeoVideo.

Information can come in many forms, and part of a data scientist's job is making sure that information is organized in a way that's conducive to analysis. Take for example these five houses from the Mexico real estate dataset we'll use in this project:

Five houses showing price, number of rooms, and area in square meters for each

One common way to organize this information is in a table, which is a group of cells organized into rows and columns:

Table, organized into rows and columns, with housing information from previous image

When working with this sort of tabular data, it's important to organize row and columns following the principles of "tidy data." What does that mean in the case of our dataset?

Each row corresponds to a single house in our dataset. We'll call each of these houses an observation.
Each column corresponds to a characteristic of each house. We'll call these features.
Each cell contains only one value.
Three copies of table from previous image, emphasizing observations as rows and features as columns

So whenever you encounter a new dataset, make sure your data is "tidy." NOTE: need to add activity here

Tabular Data and Python Data Structures
Working with Lists
Python comes with several data structures that we can use to organize tabular data. Let's start by putting a single observation in a list.

# Declare variable `house_0_list`
house_0_list = [115910.26, 128, 4]
​
# Print object type of `house_0_list`
# (We'll learn more about object types in later projects 😉)
print("house_0_list type:", type(house_0_list))
​
# Print length of `house_0_list`
print("house_0_list length:", len(house_0_list))
​
# Get output of `house_0_list`
house_0_list
house_0_list type: <class 'list'>
house_0_list length: 3
[115910.26, 128, 4]
VimeoVideo("645390786", h="da0bb831d1", width=600)
Task 1.1.1: One metric that people in the real estate industry look at is price per square meter because it allows them to compare houses of different sizes. Can you use the information in this list to calculate the price per square meter for house_0?

What's a list?
Access an item in a list using Python.
Perform basic mathematical operations in Python.WQU WorldQuant University Applied Data Science Lab QQQQ
# Declare variable `house_0_price_m2`
house_0_price_m2 = house_0_list[0]/house_0_list[1]
​
# Print object type of `house_0_price_m2`
print("house_0_price_m2 type:", type(house_0_price_m2))
​
# Get output of `house_0_price_m2`
house_0_price_m2
house_0_price_m2 type: <class 'float'>
905.54890625
VimeoVideo("645390797", h="86c579a9cb", width=600)
Task 1.1.2: Next, use the append method to add the price per square meter to the end of the end of house_0.

Append an item to a list in Python.
# Append price / sq. meter to `house_0_list`
house_0_list.append(house_0_price_m2)
​
# Print object type of `house_0_list`
print("house_0_list type:", type(house_0_list))
​
# Print length of `house_0_list`
print("house_0_list length:", len(house_0_list))
​
# Get output of `house_0_list`
house_0_list
house_0_list type: <class 'list'>
house_0_list length: 5
[115910.26, 128, 4, 905.54890625, 905.54890625]
Now that you can work with data for a single house, let's think about how to organize the whole dataset. One option would be to create a list for each observation and then put those together in another list. This is called a nested list.

# Declare variable `houses_nested_list`
houses_nested_list = [
    [115910.26, 128.0, 4.0],
    [48718.17, 210.0, 3.0],
    [28977.56, 58.0, 2.0],
    [36932.27, 79.0, 3.0],
    [83903.51, 111.0, 3.0],
]
​
# Print `houses_nested_list` type
print("houses_nested_list type:", type(houses_nested_list))
​
# Print `houses_nested_list` length
print("houses_nested_list length:", len(houses_nested_list))
​
# Get output of `houses_nested_list`
houses_nested_list
houses_nested_list type: <class 'list'>
houses_nested_list length: 5
[[115910.26, 128.0, 4.0],
 [48718.17, 210.0, 3.0],
 [28977.56, 58.0, 2.0],
 [36932.27, 79.0, 3.0],
 [83903.51, 111.0, 3.0]]
Now that we have more observations, it doesn't make sense to calculate the price per square meter for each house one-by-one. Instead, we can automate this repetitive task using a for loop.

VimeoVideo("645390807", h="4536120cf5", width=600)
Task 1.1.3: Append the price per square meter to each observation in houses_nested_list using a for loop.

What's a for loop?
Write a for loop in Python.
# Create for loop to iterate through `houses_nested_list`
for _ in range(len(houses_nested_list)):
    houses_nested_list[_].append(houses_nested_list[_][0]/houses_nested_list[_][1])
    # For each observation, append price / sq. meter
​
​
# Print `houses_nested_list` type
print("houses_nested_list type:", type(houses_nested_list))
​
# Print `houses_nested_list` length
print("houses_nested_list length:", len(houses_nested_list))
​
# Get output of `houses_nested_list`
houses_nested_list
houses_nested_list type: <class 'list'>
houses_nested_list length: 5
[[115910.26, 128.0, 4.0, 905.54890625],
 [48718.17, 210.0, 3.0, 231.9912857142857],
 [28977.56, 58.0, 2.0, 499.61310344827587],
 [36932.27, 79.0, 3.0, 467.4970886075949],
 [83903.51, 111.0, 3.0, 755.8874774774774]]
Working with Dictionaries
Lists are a good way to organize data, but one drawback is that we can only represent values. Why is that a problem? For example, someone looking at [115910.26, 128.0, 4] wouldn't know which values corresponded to price, area, etc. A better option might be a dictionary, where each value is associated with a key. Here's what house_0 looks like as a dictionary instead of a list.

# Declare variable `house_0_dict`
house_0_dict = {
    "price_approx_usd": 115910.26,
    "surface_covered_in_m2": 128,
    "rooms": 4,
}
​
# Print `house_0_dict` type
print("house_0_dict type:", type(house_0_dict))
​
# Get output of `house_0_dict`
house_0_dict
house_0_dict type: <class 'dict'>
{'price_approx_usd': 115910.26, 'surface_covered_in_m2': 128, 'rooms': 4}
VimeoVideo("645390821", h="884613d46b", width=600)
Task 1.1.4: Calculate the price per square meter for house_0 and add it to the dictionary under the key "price_per_m2".

What's a dictionary?
Access an item in a dictionary in Python.
# Add "price_per_m2" key-value pair to `house_0_dict`
house_0_dict["price_per_m2"] = house_0_dict["price_approx_usd"]/house_0_dict["surface_covered_in_m2"]
​
# Get output of `house_0_dict`
house_0_dict
{'price_approx_usd': 115910.26,
 'surface_covered_in_m2': 128,
 'rooms': 4,
 'price_per_m2': 905.54890625}
If we wanted to combine all our observations together, the best way would be to create a list of dictionaries.

# Declare variable `houses_rowwise`
houses_rowwise = [
    {
        "price_approx_usd": 115910.26,
        "surface_covered_in_m2": 128,
        "rooms": 4,
    },
    {
        "price_approx_usd": 48718.17,
        "surface_covered_in_m2": 210,
        "rooms": 3,
    },
    {
        "price_approx_usd": 28977.56,
        "surface_covered_in_m2": 58,
        "rooms": 2,
    },
    {
        "price_approx_usd": 36932.27,
        "surface_covered_in_m2": 79,
        "rooms": 3,
    },
    {
        "price_approx_usd": 83903.51,
        "surface_covered_in_m2": 111,
        "rooms": 3,
    },
]
​
# Print `houses_rowwise` object type
print("houses_rowwise type:", type(houses_rowwise))
​
# Print `houses_rowwise` length
print("houses_rowwise length:", len(houses_rowwise))
​
# Get output of `houses_rowwise`
houses_rowwise
houses_rowwise type: <class 'list'>
houses_rowwise length: 5
[{'price_approx_usd': 115910.26, 'surface_covered_in_m2': 128, 'rooms': 4},
 {'price_approx_usd': 48718.17, 'surface_covered_in_m2': 210, 'rooms': 3},
 {'price_approx_usd': 28977.56, 'surface_covered_in_m2': 58, 'rooms': 2},
 {'price_approx_usd': 36932.27, 'surface_covered_in_m2': 79, 'rooms': 3},
 {'price_approx_usd': 83903.51, 'surface_covered_in_m2': 111, 'rooms': 3}]
This way of storing data is so popular, it has its own name: JSON. We'll learn more about it later in the course. For now, let's build another for loop, but this time, we'll add a add the price per square meter to each dictionary.

VimeoVideo("645390833", h="0d3963c0d0", width=600)
Task 1.1.5: Using a for loop, calculate the price per square meter and store the result under a "price_per_m2" key for each observation in houses_rowwise.

What's JSON?
Write a for loop in Python.
# Create for loop to iterate through `houses_rowwise`
for house in houses_rowwise:
    house["price_per_m2"]= house['price_approx_usd']/house['surface_covered_in_m2']
    # For each observation, add "price_per_m2" key-value pair
    
​
# Print `houses_rowwise` object type
print("houses_rowwise type:", type(houses_rowwise))
​
# Print `houses_rowwise` length
print("houses_rowwise length:", len(houses_rowwise))
​
# Get output of `houses_rowwise`
houses_rowwise
houses_rowwise type: <class 'list'>
houses_rowwise length: 5
[{'price_approx_usd': 115910.26,
  'surface_covered_in_m2': 128,
  'rooms': 4,
  'price_per_m2': 905.54890625},
 {'price_approx_usd': 48718.17,
  'surface_covered_in_m2': 210,
  'rooms': 3,
  'price_per_m2': 231.9912857142857},
 {'price_approx_usd': 28977.56,
  'surface_covered_in_m2': 58,
  'rooms': 2,
  'price_per_m2': 499.61310344827587},
 {'price_approx_usd': 36932.27,
  'surface_covered_in_m2': 79,
  'rooms': 3,
  'price_per_m2': 467.4970886075949},
 {'price_approx_usd': 83903.51,
  'surface_covered_in_m2': 111,
  'rooms': 3,
  'price_per_m2': 755.8874774774774}]
JSON is a great way to organize data, but it does have some downsides. Note that each dictionary represents a single house or, if we think about it as tabular data, a row in our dataset. This means that it's pretty easy to do row-wise calculations (like we did with price per square meter), but column-wise calculations are more complicated. For instance, what if we wanted to know the mean house price for our dataset? First we'd need to collect the price for each house in a list and then calculate mean.

VimeoVideo("645390848", h="889a3cfb33", width=600)
Task 1.1.6: To calculate the mean price for houses_rowwise by completing the code below.

Write a for loop in Python.
Append an item to a list in Python.
# Declare `house_prices` as empty list
house_prices = []
​
# Iterate through `houses_rowwise`
for house in houses_rowwise:
    house_prices.append(house['price_approx_usd'])
    # For each house, append "price_approx_usd" to `house_prices`
​
​
# Calculate `mean_house_price` using `house_prices`
mean_house_price = sum(house_prices) / len(house_prices)
​
# Print `mean_house_price` object type
print("mean_house_price type:", type(mean_house_price))
​
# Get output of `mean_house_price`
mean_house_price
mean_house_price type: <class 'float'>
62888.35399999999
One way to make this sort of calculation easier is to organize our data by features instead of observations. We'll still use dictionaries and lists, but we'll implement them a slightly differently.

# Declare variable `houses_columnwise`
houses_columnwise = {
    "price_approx_usd": [115910.26, 48718.17, 28977.56, 36932.27, 83903.51],
    "surface_covered_in_m2": [128.0, 210.0, 58.0, 79.0, 111.0],
    "rooms": [4.0, 3.0, 2.0, 3.0, 3.0],
}
​
# Print `houses_columnwise` object type
print("houses_columnwise type:", type(houses_columnwise))
​
# Get output of `houses_columnwise`
houses_columnwise
houses_columnwise type: <class 'dict'>
{'price_approx_usd': [115910.26, 48718.17, 28977.56, 36932.27, 83903.51],
 'surface_covered_in_m2': [128.0, 210.0, 58.0, 79.0, 111.0],
 'rooms': [4.0, 3.0, 2.0, 3.0, 3.0]}
VimeoVideo("645390869", h="ef4d49bf66", width=600)
Task 1.1.7: Calculate the mean house price in houses_columnwise

Perform common aggregation tasks on a list in Python.
# Calculate `mean_house_price` using `houses_columnwise`
mean_house_price = sum(houses_columnwise['price_approx_usd'])/len(houses_columnwise['price_approx_usd'])
​
# Print `mean_house_price` object type
print("mean_house_price type:", type(mean_house_price))
​
# Get output of `mean_house_price`
mean_house_price
mean_house_price type: <class 'float'>
62888.35399999999
Of course, when we organize our data according to columns / features, row-wise operations become more difficult.

VimeoVideo("645396267", h="66eda35f00", width=600)
Task 1.1.8: Create a "price_per_m2" column in houses_columnwise?

Add a a key-value pair to a dictionary in Python.
Zip two lists together in Python.
Write a for loop in Python.
print(houses_columnwise['price_approx_usd'])
print(houses_columnwise['surface_covered_in_m2'])
houses_columnwise["price_per_m2"] =[]
for p,a in zip(houses_columnwise['price_approx_usd'],houses_columnwise['surface_covered_in_m2']):
# Add "price_per_m2" key-value pair for `houses_columnwise`
    houses_columnwise["price_per_m2"].append(p/a)
​
# Print `houses_columnwise` object type
print("houses_columnwise type:", type(houses_columnwise))
​
# Get output of `houses_columnwise`
houses_columnwise
[115910.26, 48718.17, 28977.56, 36932.27, 83903.51]
[128.0, 210.0, 58.0, 79.0, 111.0]
houses_columnwise type: <class 'dict'>
{'price_approx_usd': [115910.26, 48718.17, 28977.56, 36932.27, 83903.51],
 'surface_covered_in_m2': [128.0, 210.0, 58.0, 79.0, 111.0],
 'rooms': [4.0, 3.0, 2.0, 3.0, 3.0],
 'price_per_m2': [905.54890625,
  231.9912857142857,
  499.61310344827587,
  467.4970886075949,
  755.8874774774774]}
Tabular Data and pandas DataFrames
VimeoVideo("645396345", h="ba25c25741", width=600)
While you've shown that you can wrangle data using lists and dictionaries, it's not as intuitive as working with, say, a spreadsheet. Fortunately, there are lots of libraries for Python that make it an even better tool for tabular data — way better than spreadsheet applications like Microsoft Excel or Google Sheets! One of the best known data science libraries is pandas, which allows you to organize data into DataFrames.

Let's import pandas and then create a DataFrame from houses_columnwise.

# Import pandas library, aliased as `pd`
import pandas as pd
​
# Declare variable `df_houses`
df_houses = pd.DataFrame(houses_columnwise)
​
# Print `df_houses` object type
print("df_houses type:", type(df_houses))
​
# Print `df_houses` shape
print("df_houses shape:", df_houses.shape)
​
# Get output of `df_houses`
df_houses
df_houses type: <class 'pandas.core.frame.DataFrame'>
df_houses shape: (5, 4)
price_approx_usd	surface_covered_in_m2	rooms	price_per_m2
0	115910.26	128.0	4.0	905.548906
1	48718.17	210.0	3.0	231.991286
2	28977.56	58.0	2.0	499.613103
3	36932.27	79.0	3.0	467.497089
4	83903.51	111.0	3.0	755.887477
Excellent work! You've mastered the concept of tabular data, understand the principles behind tidy data, and used lists and dictionaries to organize and augment our Mexico housing dataset. Next, we'll use these skills on the entire dataset — with over 150,000 observations — to better understand the real estate market in the country.

Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
10
Python 3 (ipykernel) | Idle
0
011-tabular-and-tidy-data.2024-10-14T07-01-41-403Z.ipynb
Ln 1, Col 1
Mode: Command