\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{listings}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{fancyhdr}
\usepackage{titlesec}
\usepackage{enumitem}
\usepackage{booktabs}
\usepackage{float}

% Code listing style
\lstdefinestyle{pythonstyle}{
    language=Python,
    basicstyle=\ttfamily\footnotesize,
    keywordstyle=\color{blue}\bfseries,
    commentstyle=\color{green!60!black},
    stringstyle=\color{red},
    numberstyle=\tiny\color{gray},
    numbers=left,
    numbersep=5pt,
    frame=single,
    breaklines=true,
    breakatwhitespace=true,
    tabsize=4,
    showstringspaces=false,
    backgroundcolor=\color{gray!10}
}

\lstset{style=pythonstyle}

% Header and footer
\pagestyle{fancy}
\fancyhf{}
\rhead{MongoDB Data Wrangling Guide}
\lhead{WorldQuant University}
\cfoot{\thepage}

% Title formatting
\titleformat{\section}{\Large\bfseries\color{blue!70!black}}{\thesection}{1em}{}
\titleformat{\subsection}{\large\bfseries\color{blue!50!black}}{\thesubsection}{1em}{}

\title{\textbf{Comprehensive Guide to Data Wrangling with MongoDB} \\ 
       \large WorldQuant University - Applied Data Science Lab \\ 
       \large Lesson 3.1: Working with Air Quality Data from Kenya}
\author{Based on WQU Curriculum}
\date{\today}

\hypersetup{
    colorlinks=true,
    linkcolor=black,
    filecolor=black,
    urlcolor=black,
    citecolor=black,
    pdfborder={0 0 0}
}

\begin{document}

\maketitle

\tableofcontents
\newpage

\section{Introduction}

This document provides a comprehensive guide to data wrangling with MongoDB, based on the WorldQuant University Applied Data Science Lab curriculum. The lesson focuses on working with air quality data from Kenya, specifically from Nairobi, using Python and PyMongo.

\subsection{Learning Objectives}
\begin{itemize}
    \item Connect to MongoDB databases using PyMongo
    \item Explore database structure and collections
    \item Query and aggregate data from MongoDB collections
    \item Convert MongoDB data to pandas DataFrames
    \item Work with time-series air quality data
\end{itemize}

\subsection{Prerequisites}
\begin{itemize}
    \item Python programming knowledge
    \item Basic understanding of databases
    \item Familiarity with pandas library
    \item MongoDB installed and running locally
\end{itemize}

\section{Setup and Initial Configuration}

\subsection{Task 3.1.1: Instantiate PrettyPrinter}

\textbf{Objective:} Create a PrettyPrinter instance for formatted output display.

\textbf{Concept:} PrettyPrinter provides a capability to "pretty-print" arbitrary Python data structures in a form which can be used as input to the interpreter.

\begin{lstlisting}[caption=Task 3.1.1 Solution]
from pprint import PrettyPrinter
import pandas as pd
from IPython.display import VimeoVideo
from pymongo import MongoClient

# Create PrettyPrinter instance with 2-space indentation
pp = PrettyPrinter(indent=2)
\end{lstlisting}

\textbf{Key Points:}
\begin{itemize}
    \item \texttt{indent=2} sets the indentation level for nested structures
    \item PrettyPrinter makes complex data structures more readable
    \item Essential for debugging and data exploration
\end{itemize}

\section{Database Connection and Exploration}

\subsection{Task 3.1.2: Create MongoDB Client}

\textbf{Objective:} Establish connection to MongoDB server running locally.

\textbf{Concepts:}
\begin{itemize}
    \item \textbf{Database Client:} Software that connects to and communicates with a database server
    \item \textbf{Database Server:} Software that manages databases and handles client requests
\end{itemize}

\begin{lstlisting}[caption=Task 3.1.2 Solution]
# Connect to MongoDB server on localhost, port 27017 (default)
client = MongoClient(host="localhost", port=27017)
\end{lstlisting}

\textbf{Key Points:}
\begin{itemize}
    \item Port 27017 is MongoDB's default port
    \item \texttt{localhost} refers to the local machine
    \item Client object manages the connection to MongoDB
\end{itemize}

\subsection{Task 3.1.3: List Available Databases}

\textbf{Objective:} Explore what databases are available on the MongoDB server.

\textbf{Concept:} Iterators provide a way to access elements of a collection sequentially without exposing the underlying structure.

\begin{lstlisting}[caption=Task 3.1.3 Solution]
# Demonstrate iterator efficiency
from sys import getsizeof 
print(getsizeof([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]))  # 216
print(getsizeof(range(1,21)))  # 48

# List databases
databases = list(client.list_databases())
pp.pprint(databases)
\end{lstlisting}

\textbf{Expected Output:}
\begin{lstlisting}
[ {'empty': False, 'name': 'admin', 'sizeOnDisk': 40960},
  {'empty': False, 'name': 'air-quality', 'sizeOnDisk': 4190208},
  {'empty': False, 'name': 'config', 'sizeOnDisk': 12288},
  {'empty': False, 'name': 'local', 'sizeOnDisk': 73728},
  {'empty': False, 'name': 'wqu-abtest', 'sizeOnDisk': 585728}]
\end{lstlisting}

\subsection{Task 3.1.4: Access Specific Database}

\textbf{Objective:} Connect to the "air-quality" database.

\textbf{Concept:} A MongoDB database is a container for collections, similar to a schema in relational databases.

\begin{lstlisting}[caption=Task 3.1.4 Solution]
# Access the air-quality database
db = client["air-quality"]
print(db)
\end{lstlisting}

\subsection{Task 3.1.5: List Collections in Database}

\textbf{Objective:} Explore collections within the air-quality database.

\textbf{Concept:} A MongoDB collection is a group of documents, similar to a table in relational databases.

\begin{lstlisting}[caption=Task 3.1.5 Solution]
# List all collections in the database
collections = list(db.list_collections())

# Print collection names
for c in db.list_collections():
    print(c["name"])
\end{lstlisting}

\textbf{Expected Output:}
\begin{lstlisting}
lagos
system.buckets.lagos
system.views
dar-es-salaam
system.buckets.dar-es-salaam
nairobi
system.buckets.nairobi
\end{lstlisting}

\subsection{Task 3.1.6: Access Specific Collection}

\textbf{Objective:} Connect to the "nairobi" collection.

\begin{lstlisting}[caption=Task 3.1.6 Solution]
# Access the nairobi collection
nairobi = db["nairobi"]
print(nairobi)
\end{lstlisting}

\section{Data Exploration and Analysis}

\subsection{Task 3.1.7: Count Documents}

\textbf{Objective:} Determine the total number of documents in the nairobi collection.

\textbf{Concept:} A MongoDB document is a record in a collection, similar to a row in a relational database table.

\begin{lstlisting}[caption=Task 3.1.7 Solution]
# Count all documents in the collection
total_documents = nairobi.count_documents({})
print(f"Total documents: {total_documents}")

# Alternative using pandas
df = pd.DataFrame(list(nairobi.find()))
document_count = df.shape[0]
print(f"Number of documents: {document_count}")
\end{lstlisting}

\textbf{Expected Output:} 202,212 documents

\subsection{Task 3.1.8: Retrieve Sample Document}

\textbf{Objective:} Examine the structure of a single document.

\textbf{Concepts:}
\begin{itemize}
    \item \textbf{Metadata:} Data that provides information about other data
    \item \textbf{Semi-structured data:} Data that has some organizational properties but lacks rigid structure
\end{itemize}

\begin{lstlisting}[caption=Task 3.1.8 Solution]
# Retrieve one document from the collection
result = nairobi.find_one()
pp.pprint(result)
\end{lstlisting}

\textbf{Expected Output:}
\begin{lstlisting}
{ '_id': ObjectId('6525d772f44bfedd842a6fcc'),
  'metadata': { 'lat': -1.3,
                'lon': 36.785,
                'measurement': 'temperature',
                'sensor_id': 58,
                'sensor_type': 'DHT22',
                'site': 29},
  'temperature': 16.5,
  'timestamp': datetime.datetime(2018, 9, 1, 0, 0, 4, 301000)}
\end{lstlisting}

\subsection{Task 3.1.9: Find Distinct Sensor Sites}

\textbf{Objective:} Identify unique sensor sites in the collection.

\begin{lstlisting}[caption=Task 3.1.9 Solution]
# Get distinct site values
sites = nairobi.distinct("metadata.site")
print(f"Sensor sites: {sites}")
\end{lstlisting}

\textbf{Expected Output:} [29, 6]

\subsection{Task 3.1.10: Count Documents by Site}

\textbf{Objective:} Determine the number of readings from each sensor site.

\begin{lstlisting}[caption=Task 3.1.10 Solution]
# Count documents for each site
site_6_count = nairobi.count_documents({"metadata.site": 6})
site_29_count = nairobi.count_documents({"metadata.site": 29})

print(f"Documents from site 6: {site_6_count}")
print(f"Documents from site 29: {site_29_count}")
\end{lstlisting}

\textbf{Expected Output:}
\begin{itemize}
    \item Site 6: 70,360 documents
    \item Site 29: 131,852 documents
\end{itemize}

\subsection{Task 3.1.11: Aggregate Site Counts}

\textbf{Objective:} Use aggregation pipeline to count documents by site.

\begin{lstlisting}[caption=Task 3.1.11 Solution]
# Method 1: Using loop and count_documents
site_counts = {}
for site in nairobi.distinct("metadata.site"):
    site_counts[site] = nairobi.count_documents({"metadata.site": site})
print(site_counts)

# Method 2: Using aggregation pipeline
result = nairobi.aggregate([
    {"$group": {"_id": "$metadata.site", "count": {"$count": {}}}}
])
pp.pprint(list(result))
\end{lstlisting}

\section{Measurement Type Analysis}

\subsection{Task 3.1.12: Identify Measurement Types}

\textbf{Objective:} Discover what types of measurements are recorded.

\begin{lstlisting}[caption=Task 3.1.12 Solution]
# Get distinct measurement types
measurements = nairobi.distinct("metadata.measurement")
print(f"Measurement types: {measurements}")
\end{lstlisting}

\textbf{Expected Output:} ['P1', 'temperature', 'humidity', 'P2']

\textbf{Measurement Explanations:}
\begin{itemize}
    \item \textbf{P1:} Particles $\leq$ 10 micrometers
    \item \textbf{P2:} Particles $\leq$ 2.5 micrometers
    \item \textbf{temperature:} Air temperature (°C)
    \item \textbf{humidity:} Relative humidity (\%)
\end{itemize}

\subsection{Task 3.1.13: Query PM2.5 Readings}

\textbf{Objective:} Retrieve specific measurement type with limited results.

\begin{lstlisting}[caption=Task 3.1.13 Solution]
# Find PM2.5 readings, limit to 3 results
result = nairobi.find({"metadata.measurement": "P2"}).limit(3)
pp.pprint(list(result))
\end{lstlisting}

\subsection{Task 3.1.14: Count Measurements by Type (Site 6)}

\textbf{Objective:} Analyze measurement distribution for site 6.

\begin{lstlisting}[caption=Task 3.1.14 Solution]
# Method 1: Using count_documents
reading_counts = {}
for reading_type in ["humidity", "temperature", "P2", "P1"]:
    count = nairobi.count_documents({
        "metadata.site": 6, 
        "metadata.measurement": reading_type
    })
    reading_counts[reading_type] = count

print("Readings in site 6:")
for reading_type, count in reading_counts.items():
    print(f"{reading_type}: {count}")

# Method 2: Using aggregation
result = nairobi.aggregate([
    {"$match": {"metadata.site": 6}},
    {"$group": {"_id": "$metadata.measurement", "count": {"$count": {}}}}
])
pp.pprint(list(result))

# Method 3: Using pandas
cursor = nairobi.find({"metadata.site": 6})
df = pd.DataFrame(list(cursor))
measurement_counts = df.groupby(
    df['metadata'].apply(lambda x: x.get('measurement', 'Unknown'))
).size().reset_index(name='count')
print("Measurements in site 6:")
print(measurement_counts)
\end{lstlisting}

\textbf{Expected Output for Site 6:}
\begin{itemize}
    \item humidity: 17,011
    \item temperature: 17,011
    \item P2: 18,169
    \item P1: 18,169
\end{itemize}

\subsection{Task 3.1.15: Count Measurements by Type (Site 29)}

\textbf{Objective:} Analyze measurement distribution for site 29.

\begin{lstlisting}[caption=Task 3.1.15 Solution]
# Using aggregation for site 29
result = nairobi.aggregate([
    {"$match": {"metadata.site": 29}},
    {"$group": {"_id": "$metadata.measurement", "count": {"$count": {}}}}
])
pp.pprint(list(result))
\end{lstlisting}

\textbf{Expected Output for Site 29:}
\begin{itemize}
    \item P1: 32,907
    \item temperature: 33,019
    \item humidity: 33,019
    \item P2: 32,907
\end{itemize}

\section{Data Import and DataFrame Creation}

\subsection{Task 3.1.16: Query with Projection}

\textbf{Objective:} Retrieve specific fields using projection to limit data transfer.

\begin{lstlisting}[caption=Task 3.1.16 Solution]
# Query without projection (full document)
result = nairobi.find({
    "metadata.site": 29, 
    "metadata.measurement": "P2"
})
pp.pprint(result.next())

# Query with projection (specific fields only)
result = nairobi.find(
    {"metadata.site": 29, "metadata.measurement": "P2"},
    projection={"P2": 1, "timestamp": 1, "_id": 0}
)
pp.pprint(result.next())
\end{lstlisting}

\textbf{Projection Benefits:}
\begin{itemize}
    \item Reduces network traffic
    \item Improves query performance
    \item Simplifies data structure
    \item Saves memory
\end{itemize}

\subsection{Task 3.1.17: Create DataFrame with Time Index}

\textbf{Objective:} Convert MongoDB cursor to pandas DataFrame with datetime index.

\begin{lstlisting}[caption=Task 3.1.17 Solution]
# Query PM2.5 data from site 29 with projection
result = nairobi.find(
    {"metadata.site": 29, "metadata.measurement": "P2"},
    projection={"P2": 1, "timestamp": 1, "_id": 0}
)

# Convert to DataFrame
df = pd.DataFrame(list(result))

# Ensure timestamp is datetime type
df['timestamp'] = pd.to_datetime(df['timestamp'])

# Set timestamp as index
df.set_index('timestamp', inplace=True)

# Display first few rows
print(df.head())
\end{lstlisting}

\textbf{Expected DataFrame Structure:}
\begin{table}[H]
\centering
\begin{tabular}{@{}ll@{}}
\toprule
timestamp & P2 \\
\midrule
2018-09-01 00:05:03.941 & 30.53 \\
2018-09-01 00:10:04.374 & 22.80 \\
2018-09-01 00:15:04.245 & 13.30 \\
2018-09-01 00:20:04.869 & 16.57 \\
2018-09-01 00:25:04.659 & 14.07 \\
\bottomrule
\end{tabular}
\caption{Sample PM2.5 Data from Site 29}
\end{table}

\section{Key Concepts Summary}

\subsection{MongoDB Concepts}
\begin{itemize}
    \item \textbf{Database:} Container for collections
    \item \textbf{Collection:} Group of documents (like a table)
    \item \textbf{Document:} Individual record (like a row)
    \item \textbf{Field:} Key-value pair within a document (like a column)
\end{itemize}

\subsection{PyMongo Operations}
\begin{itemize}
    \item \textbf{find():} Query documents
    \item \textbf{find\_one():} Retrieve single document
    \item \textbf{count\_documents():} Count matching documents
    \item \textbf{distinct():} Get unique values
    \item \textbf{aggregate():} Perform complex operations
\end{itemize}

\subsection{Query Operators}
\begin{itemize}
    \item \textbf{\$match:} Filter documents
    \item \textbf{\$group:} Group documents by field
    \item \textbf{\$count:} Count documents in group
    \item \textbf{projection:} Specify fields to return
\end{itemize}

\section{Best Practices}

\subsection{Performance Optimization}
\begin{enumerate}
    \item Use projection to limit returned fields
    \item Apply filters early in aggregation pipelines
    \item Use appropriate indexes for frequent queries
    \item Limit result sets when exploring data
\end{enumerate}

\subsection{Data Quality Checks}
\begin{enumerate}
    \item Verify document structure with \texttt{find\_one()}
    \item Check for missing or null values
    \item Validate data types after conversion
    \item Ensure proper datetime handling
\end{enumerate}

\subsection{Memory Management}
\begin{enumerate}
    \item Use iterators for large datasets
    \item Process data in chunks when possible
    \item Close cursors after use
    \item Monitor memory usage during operations
\end{enumerate}

\section{Conclusion}

This comprehensive guide covered essential MongoDB data wrangling techniques using Python and PyMongo. Key achievements include:

\begin{itemize}
    \item Successfully connected to MongoDB and explored database structure
    \item Performed various query operations and aggregations
    \item Converted MongoDB data to pandas DataFrames
    \item Analyzed air quality data from Nairobi sensor sites
    \item Applied best practices for performance and data quality
\end{itemize}

The skills demonstrated here form the foundation for working with NoSQL databases in data science projects, particularly for handling semi-structured time-series data like environmental sensor readings.

\subsection{Next Steps}
\begin{itemize}
    \item Explore advanced aggregation pipelines
    \item Implement data cleaning and preprocessing
    \item Perform time-series analysis on air quality data
    \item Create visualizations and statistical summaries
    \item Build predictive models using the processed data
\end{itemize}

\end{document} 