Skip to left side bar










Markdown




Python 3 (ipykernel)







Usage Guidelines
This lesson is part of the DS Lab core curriculum. For that reason, this notebook can only be used on your WQU virtual machine.

This means:

ⓧ No downloading this notebook.
ⓧ No re-sharing of this notebook with friends or colleagues.
ⓧ No downloading the embedded videos in this notebook.
ⓧ No re-sharing embedded videos with friends or colleagues.
ⓧ No adding this notebook to public or private repositories.
ⓧ No uploading this notebook (or screenshots of it) to other websites, including websites for study resources.
3.4. ARMA Models

import inspect
import time
import warnings
​
import matplotlib.pyplot as plt
import pandas as pd
import plotly.express as px
import seaborn as sns
from IPython.display import VimeoVideo
from pymongo import MongoClient
from sklearn.metrics import mean_absolute_error
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.tsa.arima.model import ARIMA
​
warnings.filterwarnings("ignore")
VimeoVideo("665851728", h="95c59d2805", width=600)
Prepare Data
Import
Task 3.4.1: Create a client to connect to the MongoDB server, then assign the "air-quality" database to db, and the "nairobi" collection to nairobi.

Create a client object for a MongoDB instance.
Access a database using PyMongo.
Access a collection in a database using PyMongo.
client = ...
db = ...
nairobi = ...
def wrangle(collection):
​
    results = collection.find(
        {"metadata.site": 29, "metadata.measurement": "P2"},
        projection={"P2": 1, "timestamp": 1, "_id": 0},
    )
​
    # Read results into DataFrame
    df = pd.DataFrame(list(results)).set_index("timestamp")
​
    # Localize timezone
    df.index = df.index.tz_localize("UTC").tz_convert("Africa/Nairobi")
​
    # Remove outliers
    df = df[df["P2"] < 500]
​
    # Resample and forward-fill
    y = ...
​
    return y
VimeoVideo("665851670", h="3efc0c20d4", width=600)
Task 3.4.2: Change your wrangle function so that it has a resample_rule argument that allows the user to change the resampling interval. The argument default should be "1H".

What's an argument?
Include an argument in a function in Python.
# Check your work
func_params = set(inspect.signature(wrangle).parameters.keys())
assert func_params == set(
    ["collection", "resample_rule"]
), f"Your function should take two arguments: `'collection'`, `'resample_rule'`. Your function takes the following arguments: {func_params}"
Task 3.4.3: Use your wrangle function to read the data from the nairobi collection into the Series y.

y = ...
y.head()
# Check your work
assert isinstance(y, pd.Series), f"`y` should be a Series, not a {type(y)}."
assert len(y) == 2928, f"`y` should have 2,928 observations, not {len(y)}."
assert (
    y.isnull().sum() == 0
), f"There should be no null values in `y`. Your `y` has {y.isnull().sum()} null values."
Explore
VimeoVideo("665851654", h="687ff8d5ee", width=600)
Task 3.4.4: Create an ACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient".

What's an ACF plot?
Create an ACF plot using statsmodels
​
VimeoVideo("665851644", h="e857f05bfb", width=600)
Task 3.4.5: Create an PACF plot for the data in y. Be sure to label the x-axis as "Lag [hours]" and the y-axis as "Correlation Coefficient".

What's a PACF plot?
Create an PACF plot using statsmodels
​
Split
Task 3.4.6: Create a training set y_train that contains only readings from October 2018, and a test set y_test that contains readings from November 1, 2018.

Subset a DataFrame by selecting one or more rows in pandas.
y_train = ...
y_test = ...
# Check your work
assert (
    len(y_train) == 744
), f"`y_train` should have 744 observations, not {len(y_train)}."
assert len(y_test) == 24, f"`y_test` should have 24 observations, not {len(y_test)}."
Build Model
Baseline
Task 3.4.7: Calculate the baseline mean absolute error for your model.

​
print("Mean P2 Reading:", round(y_train_mean, 2))
print("Baseline MAE:", round(mae_baseline, 2))
Iterate
VimeoVideo("665851576", h="36e2dc6269", width=600)
Task 3.4.8: Create ranges for possible p and q values. p_params should range between 0 and 25, by steps of 8. q_params should range between 0 and 3 by steps of 1.

What's a hyperparameter?
What's an iterator?
Create a range in Python.
p_params = ...
q_params = ...
VimeoVideo("665851476", h="d60346ed30", width=600)
Task 3.4.9: Complete the code below to train a model with every combination of hyperparameters in p_params and q_params. Every time the model is trained, the mean absolute error is calculated and then saved to a dictionary. If you're not sure where to start, do the code-along with Nicholas!

What's an ARMA model?
Append an item to a list in Python.
Calculate the mean absolute error for a list of predictions in scikit-learn.
Instantiate a predictor in statsmodels.
Train a model in statsmodels.
Write a for loop in Python.
# Create dictionary to store MAEs
mae_grid = dict()
# Outer loop: Iterate through possible values for `p`
for p in p_params:
    # Create key-value pair in dict. Key is `p`, value is empty list.
    mae_grid[p] = list()
    # Inner loop: Iterate through possible values for `q`
    for q in q_params:
        # Combination of hyperparameters for model
        order = (p, 0, q)
        # Note start time
        start_time = time.time()
        # Train model
        model = ...
        # Calculate model training time
        elapsed_time = round(time.time() - start_time, 2)
        print(f"Trained ARIMA {order} in {elapsed_time} seconds.")
        # Generate in-sample (training) predictions
        y_pred = ...
        # Calculate training MAE
        mae = ...
        # Append MAE to list in dictionary
        mae_grid[p].append(mae)
​
print()
print(mae_grid)
VimeoVideo("665851464", h="12f4080d0b", width=600)
Task 3.4.10: Organize all the MAE's from above in a DataFrame names mae_df. Each row represents a possible value for $q$ and each column represents a possible value for $p$.

Create a DataFrame from a dictionary using pandas.
mae_df = ...
mae_df.round(4)
VimeoVideo("665851453", h="dfd415bc08", width=600)
Task 3.4.11: Create heatmap of the values in mae_grid. Be sure to label your x-axis "p values" and your y-axis "q values".

Create a heatmap in seaborn.
​
VimeoVideo("665851444", h="8b58161f26", width=600)
Task 3.4.12: Use the plot_diagnostics method to check the residuals for your model. Keep in mind that the plot will represent the residuals from the last model you trained, so make sure it was your best model, too!

Examine time series model residuals using statsmodels.
fig, ax = plt.subplots(figsize=(15, 12))
​
Evaluate
VimeoVideo("665851439", h="c48d80cdf4", width=600)
Task 3.4.13: Complete the code below to perform walk-forward validation for your model for the entire test set y_test. Store your model's predictions in the Series y_pred_wfv. Choose the values for $p$ and $q$ that best balance model performance and computation time. Remember: This model is going to have to train 24 times before you can see your test MAE!WQU WorldQuant University Applied Data Science Lab QQQQ

y_pred_wfv = pd.Series()
history = y_train.copy()
for i in range(len(y_test)):
    model = ...
    next_pred = model.forecast()
    y_pred_wfv = y_pred_wfv.append(next_pred)
    history = history.append(y_test[next_pred.index])
test_mae = ...
print("Test MAE (walk forward validation):", round(test_mae, 2))
Communicate Results
VimeoVideo("665851423", h="8236ff348f", width=600)
Task 3.4.14: First, generate the list of training predictions for your model. Next, create a DataFrame df_predictions with the true values y_test and your predictions y_pred_wfv (don't forget the index). Finally, plot df_predictions using plotly express. Make sure that the y-axis is labeled "P2".

Generate in-sample predictions for a model in statsmodels.
Create a DataFrame from a dictionary using pandas.
Create a line plot in pandas.
df_predictions = ...
fig = ...
fig.show()
Copyright 2023 WorldQuant University. This content is licensed solely for personal use. Redistribution or publication of this material is strictly prohibited.


Simple
0
7
Python 3 (ipykernel) | Idle
0
034-arma-models-and-hyperparameter-tuning.ipynb
Ln 1, Col 1
Mode: Command